package vn.com.mbbank.kanban.mbmonitor.server.services;


import java.util.List;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WebHookEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.WebHookDto;

/**
 * WebHookService.
 *
 * <AUTHOR>
 * @created_date 8/19/2024
 */
@Service
public interface WebHookService extends BaseService<WebHookEntity, Long> {


  /**
   * Save config webhook.
   *
   * @param body data config
   * @return data config after save
   */
  WebHookEntity saveConfig(WebHookDto body) throws BusinessException;

  /**
   * Refresh Token config by Id.
   *
   * @param id id
   */
  void refreshToken(Long id) throws BusinessException;

  /**
   * find by serviceId.
   *
   * @param serviceId serviceId
   * @return list webhook config
   */
  List<WebHookEntity> findAllByServiceId(String serviceId);

  /**
   * Finds all webhooks by application ID.
   *
   * @param applicationId the ID of the application
   * @return a list of WebHookEntity containing webhook configurations
   */
  List<WebHookEntity> findAllByApplicationId(String applicationId);

  /**
   * find all webhook by priorityId.
   *
   * @param priorityId id of priority.
   * @return list webhook config
   */
  List<WebHookEntity> findAllByAlertPriorityConfigId(Long priorityId);

  /**
   * delete webhook by id.
   *
   * @param webhookId the ID of the webhook
   */
  void deleteWithId(Long webhookId) throws BusinessException;
}
