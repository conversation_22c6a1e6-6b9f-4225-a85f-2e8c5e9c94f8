package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.DatabaseConnectionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ExternalDatabaseUrl;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.DatabaseConnectionResponseToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.services.DatabaseConnectionService;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/5/2024
 */
@RestController
@RequestMapping(ExternalDatabaseUrl.DATABASE_CONNECTION)
@RequiredArgsConstructor
public class DatabaseConnectionController extends BaseController {
  private final DatabaseConnectionService databaseConnectionService;

  /**
   * Test connection.
   *
   * @param request database info
   * @return OK/ exception
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.DATABASE_CONNECTION, action = PermissionActionEnum.CREATE),
      @AclPermission(module = PermissionModuleEnum.DATABASE_CONNECTION, action = PermissionActionEnum.EDIT)
  })
  @PostMapping("test-connection")
  public ResponseData<String> testConnection(@RequestBody @Valid DatabaseConnectionRequest request)
      throws BusinessException {
    databaseConnectionService.checkConnection(request);
    return ResponseUtils.success("OK");
  }

  /**
   * Save connection.
   *
   * @param request database info
   * @return database info
   * @throws BusinessException ex
   */
  @PostMapping
  public ResponseData<DatabaseConnectionResponse> save(
      @RequestBody @Valid DatabaseConnectionRequest request)
      throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.DATABASE_CONNECTION, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(request.getId()));
    return ResponseUtils.success(DatabaseConnectionResponseToEntityMapper.INSTANCE.map(
        databaseConnectionService.saveDataValid(request)));
  }

  /**
   * Fill all connection.
   *
   * @param orderBy orderBy
   * @return list connection
   */
  @GetMapping
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.DATABASE_CONNECTION, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.EXECUTION, action = PermissionActionEnum.CREATE),
      @AclPermission(module = PermissionModuleEnum.EXECUTION, action = PermissionActionEnum.EDIT),
      @AclPermission(module = PermissionModuleEnum.DATABASE_COLLECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_REQUEST, action = PermissionActionEnum.APPROVE),
      @AclPermission(module = PermissionModuleEnum.EXECUTION, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_REQUEST, action = PermissionActionEnum.VIEW)
  })
  public ResponseData<List<DatabaseConnectionResponse>> findAll(@RequestParam(required = false) String orderBy) {
    Sort sort =
        Sort.by(Sort.Order.desc(!KanbanCommonUtil.isEmpty(orderBy) ? orderBy : "createdDate"));
    return ResponseUtils.success(DatabaseConnectionResponseToEntityMapper.INSTANCE.map(
        databaseConnectionService.findAll(sort)));
  }

  /**
   * Active connection.
   *
   * @param id       id
   * @param isActive isActive
   * @return ok/exception
   * @throws BusinessException ex
   */

  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.DATABASE_CONNECTION, action = PermissionActionEnum.EDIT)
  })
  @PutMapping("/{id}/status")
  public ResponseData<String> settingStatus(@PathVariable Long id,
                                            @RequestParam(value = "active") boolean isActive)
      throws BusinessException {
    databaseConnectionService.setActiveById(id, isActive);
    return ResponseUtils.success("OK");
  }


  /**
   * Delete by id.
   *
   * @param id id
   * @return data
   * @throws BusinessException ex
   */

  @DeleteMapping("/{id}")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.DATABASE_CONNECTION, action = PermissionActionEnum.DELETE)
  })
  public ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    databaseConnectionService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }

  /**
   * Delete batch.
   *
   * @param ids ids
   * @return data
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.DATABASE_CONNECTION, action = PermissionActionEnum.DELETE)
  })
  @DeleteMapping("/batch")
  public ResponseData<String> deleteBatch(@RequestParam(value = "ids[]") List<Long> ids)
      throws BusinessException {
    databaseConnectionService.deleteByIdIn(ids);
    return ResponseUtils.success("OK");
  }

}
