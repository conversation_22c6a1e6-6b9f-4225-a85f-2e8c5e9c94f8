package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailPartnerPaginationModel;

/**
 * Model view attribute to filter in email partner.
 */

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailPartnerPaginationRequest extends EmailPartnerPaginationModel {

}
