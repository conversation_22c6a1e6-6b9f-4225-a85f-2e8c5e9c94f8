package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.AlertRequestRejectRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.AlertRequestRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.AlertRequestResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertRequestService;
import vn.com.mbbank.kanban.test.ControllerTest;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class AlertRequestControllerTest extends ControllerTest {

    @Mock
    private AlertRequestService alertRequestService;

    @InjectMocks
    @Spy
    private AlertRequestController alertRequestController;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findAll_success() {
        verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.ALERT_REQUEST, PermissionActionEnum.VIEW)),
                "findAll", AlertRequestController.class);
        Page<AlertRequestResponse> mockPage =
                new PageImpl<>(Collections.singletonList(new AlertRequestResponse()));
        when(alertRequestService.findAll(any(PaginationRequest.class))).thenReturn(mockPage);

        PaginationRequest paginationRequest = new PaginationRequest();
        ResponseData<Page<AlertRequestResponse>> response =
                alertRequestController.findAll(paginationRequest);

        assertEquals(1, response.getData().getContent().size());
        verify(alertRequestService, times(1)).findAll(paginationRequest);
    }

    @Test
    void createOrUpdate_success() throws BusinessException {
        verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.ALERT_REQUEST, PermissionActionEnum.VIEW)),
                "createOrUpdate", AlertRequestController.class);
        AlertRequestRequest request = new AlertRequestRequest();
        AlertRequestResponse mockResponse = new AlertRequestResponse();
        when(alertRequestService.createOrUpdate(any(AlertRequestRequest.class))).thenReturn(mockResponse);

        ResponseData<AlertRequestResponse> response = alertRequestController.createOrUpdate(request);

        assertEquals(mockResponse, response.getData());
        verify(alertRequestService, times(1)).createOrUpdate(request);
    }

    @Test
    void findById_success() throws Exception {
        verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.ALERT_REQUEST, PermissionActionEnum.VIEW),
                new AclPermissionModel(PermissionModuleEnum.ALERT_REQUEST, PermissionActionEnum.APPROVE)),
                "findById", AlertRequestController.class);
        String id = "123";
        AlertRequestResponse mockResponse = new AlertRequestResponse();
        when(alertRequestService.findWithId(id)).thenReturn(mockResponse);

        ResponseData<AlertRequestResponse> response = alertRequestController.findById(id);

        assertEquals(mockResponse, response.getData());
        verify(alertRequestService, times(1)).findWithId(id);
    }

    @Test
    void approve_success() throws BusinessException {
        verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.ALERT_REQUEST, PermissionActionEnum.APPROVE)),
                "approve", AlertRequestController.class);
        AlertRequestRequest request = new AlertRequestRequest();
        AlertRequestResponse mockResponse = new AlertRequestResponse();
        when(alertRequestService.approve(any(AlertRequestRequest.class))).thenReturn(mockResponse);

        ResponseData<AlertRequestResponse> response = alertRequestController.approve(request);

        assertEquals(mockResponse, response.getData());
        verify(alertRequestService, times(1)).approve(request);
    }

    @Test
    void rejectById_success() throws BusinessException {
        verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.ALERT_REQUEST, PermissionActionEnum.APPROVE)),
                "rejectById", AlertRequestController.class);
        AlertRequestRejectRequest request = new AlertRequestRejectRequest();
        request.setId("123");
        request.setRejectedReason("Reason");
        doNothing().when(alertRequestService).rejectById(eq("123"), eq("Reason"));

        ResponseData<String> response = alertRequestController.rejectById(request);

        assertEquals("OK", response.getData());
        verify(alertRequestService, times(1)).rejectById(eq("123"), eq("Reason"));
    }

    @Test
    void deleteById_success() throws BusinessException {
        String id = "123";
        doNothing().when(alertRequestService).deleteWithId(id);

        ResponseData<String> response = alertRequestController.deleteById(id);

        assertEquals("OK", response.getData());
        verify(alertRequestService, times(1)).deleteWithId(id);
    }
}