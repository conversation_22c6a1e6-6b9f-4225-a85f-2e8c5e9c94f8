package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * EmailTemplateDependenciesResponse.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = false)
public class EmailTemplateDependenciesResponse {
  @Builder.Default
  List<String> workflowTemplates = new ArrayList<>();
}
