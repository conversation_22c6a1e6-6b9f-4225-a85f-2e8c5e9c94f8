package vn.com.mbbank.kanban.mbmonitor.server.utils.handler;

import static vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants.EMAIL_RECEIVER_MAX_LENGTH;

import jakarta.activation.DataHandler;
import jakarta.activation.DataSource;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;
import jakarta.mail.util.ByteArrayDataSource;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.modelmapper.internal.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.mbmonitor.common.constants.BeanNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailPartnerAddressEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailPartnerEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.S3FileService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailComposedModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailConfigModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailPartnerModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.FileStorageModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailPartnerRequest;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.EmailConfigModelEmailConfigEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailPartnerAddressService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailPartnerService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailService;
import vn.com.mbbank.kanban.mbmonitor.server.services.FileStorageService;


/**
 * Implementation of send emails.
 */
@Service
public class SendEmailHandler {
  protected static final Logger logger = LoggerFactory.getLogger(SendEmailHandler.class);
  @Autowired
  @Qualifier(BeanNameConstants.FILE_STORAGE_S3_SERVICE)
  FileStorageService fileStorageService;
  @Autowired
  EmailPartnerService emailPartnerService;
  @Autowired
  EmailPartnerAddressService emailPartnerAddressService;
  @Autowired
  EmailConfigService emailConfigService;
  @Autowired
  EmailService emailService;
  @Autowired
  S3FileService s3FileService;
  @Value("${mbmonitor.email.max-total-size}")
  private long emailMaxTotalSize;
  @Value("${thread-pool.pool-size.core}")
  private Integer corePoolSize;

  /**
   * Sends an email with the specified content and attachments.
   *
   * @param emailComposed the email details including subject, content, and recipients.
   * @param files         a list of files to be attached to the email.
   * @throws BusinessException if an error occurs while sending the email.
   */
  public void sendEmails(EmailComposedModel emailComposed, List<MultipartFile> files)
      throws BusinessException {
    this.validateEmailRecipients(emailComposed);
    EmailConfigEntity emailConfig =
        this.emailConfigService.findByEmailAndProtocolType(emailComposed.getEmailSender(),
                EmailProtocolTypeEnum.SMTP)
            .orElseThrow(() -> new BusinessException(ErrorCode.EMAIL_SENDER_NOT_FOUND));
    if (!emailConfig.isActive()) {
      throw new BusinessException(ErrorCode.EMAIL_CONFIG_INACTIVE);
    }
    List<File> validFileStorages = new ArrayList<>();
    try {
      EmailConfigModel emailConfigModel =
          EmailConfigModelEmailConfigEntityMapper.INSTANCE.map(emailConfig);
      emailConfigModel.setPassword(KanbanEncryptorUtils.decrypt(emailConfig.getPassword()));
      JavaMailSender mailSender = this.emailService.configSessionMailSender(emailConfigModel);
      long totalSize = emailComposed.getContent().getBytes(StandardCharsets.UTF_8).length;
      List<MultipartFile> validFilesUpload = new ArrayList<>();
      if (!CollectionUtils.isEmpty(files)) {
        validFilesUpload = files.stream()
            .filter(Objects::nonNull)
            .toList();
        totalSize += validFilesUpload.stream().mapToLong(MultipartFile::getSize).sum();
      }
      if (!CollectionUtils.isEmpty(emailComposed.getFileStorages())) {
        validFileStorages = this.findValidFileStorages(emailComposed.getFileStorages());
        totalSize += validFileStorages.stream().mapToLong(File::length).sum();
      }
      if (totalSize > emailMaxTotalSize) {
        throw new BusinessException(ErrorCode.EMAIL_COMPOSED_SIZE_TOO_LARGE);
      }
      if (emailComposed.getIsOneEmail() || CollectionUtils.size(emailComposed.getPartners()) <= 1) {
        this.sendSingleEmail(emailComposed, mailSender, validFilesUpload, validFileStorages);
      } else {
        this.sendSeparateEmails(emailComposed, mailSender, validFilesUpload, validFileStorages);
      }
    } catch (Exception e) {
      throw new BusinessException(ErrorCode.EMAIL_SENDING_ERROR, e.getMessage());
    } finally {
      validFileStorages.forEach(File::delete);
    }
  }

  void validateEmailRecipients(EmailComposedModel emailComposed) throws BusinessException {
    if (CollectionUtils.isEmpty(emailComposed.getTo())
        &&
        CollectionUtils.isEmpty(emailComposed.getPartners())) {
      throw new BusinessException(ErrorCode.EMAIL_RECEIVER_IS_EMPTY);
    }
    List<String> baseRecipients = new ArrayList<>();
    if (!CollectionUtils.isEmpty(emailComposed.getTo())) {
      baseRecipients.addAll(emailComposed.getTo());
    }
    if (!CollectionUtils.isEmpty(emailComposed.getPartners())) {
      List<Long> idsToCheck = emailComposed.getPartners()
          .stream()
          .filter(Objects::nonNull)
          .map(EmailPartnerRequest::getId)
          .toList();

      Set<Long> existingIds = emailPartnerService.findAllById(idsToCheck)
          .stream()
          .map(EmailPartnerEntity::getId)
          .collect(Collectors.toSet());

      List<String> nonExistentPartners = emailComposed.getPartners()
          .stream()
          .filter(request -> !existingIds.contains(request.getId()))
          .map(EmailPartnerModel::getName)
          .toList();

      if (!nonExistentPartners.isEmpty()) {
        throw new BusinessException(ErrorCode.EMAIL_PARTNER_NOT_FOUND, nonExistentPartners);
      }

      for (EmailPartnerModel partner : emailComposed.getPartners()) {
        if (CollectionUtils.isEmpty(partner.getAddresses())) {
          continue;
        }
        Set<String> addressExisting =
            emailPartnerAddressService.findAllByEmailPartnerId(partner.getId())
                .stream()
                .map(EmailPartnerAddressEntity::getAddress)
                .collect(Collectors.toSet());

        List<String> nonAddressExistent = partner.getAddresses()
            .stream()
            .filter(address -> Objects.nonNull(address) && !addressExisting.contains(address))
            .toList();
        if (!nonAddressExistent.isEmpty()) {
          throw new BusinessException(ErrorCode.EMAIL_ADDRESS_NOT_FOUND, nonAddressExistent,
              partner.getName());
        }
        Set<String> recipientsForPartner = new HashSet<>(baseRecipients);
        recipientsForPartner.addAll(partner.getAddresses());
        if (recipientsForPartner.size() > EMAIL_RECEIVER_MAX_LENGTH) {
          throw new BusinessException(ErrorCode.EMAIL_RECEIVER_TOO_MUCH, partner.getName());
        }
      }
    }
  }

  List<File> findValidFileStorages(List<FileStorageModel> fileStorages) throws BusinessException, IOException {
    List<File> validFiles = new ArrayList<>();
    List<FileStorageEntity> fileStorageList = this.fileStorageService.findAllById(
        fileStorages.stream()
            .map(FileStorageModel::getId)
            .toList()
    );
    for (FileStorageEntity fileStorage : fileStorageList) {
      Pair<String, String> parts = StringUtils.extractNameParts(fileStorage.getPath());
      File tempFile = File.createTempFile(parts.getLeft(), parts.getRight());
      Files.write(tempFile.toPath(), s3FileService.getObject(fileStorage.getPath()));
      validFiles.add(tempFile);
    }
    return validFiles;
  }


  void sendSingleEmail(EmailComposedModel emailComposed, JavaMailSender mailSender,
                       List<MultipartFile> validFilesUpload, List<File> validFileResources)
      throws MessagingException, IOException, BusinessException {
    Set<String> allRecipients = new HashSet<>();
    if (!CollectionUtils.isEmpty(emailComposed.getTo())) {
      allRecipients.addAll(emailComposed.getTo());
    }
    if (!CollectionUtils.isEmpty(emailComposed.getPartners())) {
      allRecipients.addAll(emailComposed.getPartners().stream()
          .map(EmailPartnerModel::getAddresses)
          .filter(CollectionUtils::isNotEmpty)
          .flatMap(List::stream)
          .toList());
    }
    MimeMessageHelper helper =
        this.builderHelper(emailComposed, mailSender, validFilesUpload, validFileResources);
    if (!allRecipients.isEmpty()) {
      helper.setTo(allRecipients.toArray(new String[0]));
      mailSender.send(helper.getMimeMessage());
    }
  }

  void sendSeparateEmails(EmailComposedModel emailComposed, JavaMailSender mailSender,
                          List<MultipartFile> validFilesUpload, List<File> validFileStorages) {
    ExecutorService executor = Executors.newFixedThreadPool(corePoolSize);
    CompletableFuture<?>[] futures = emailComposed.getPartners().stream()
        .map(partner -> CompletableFuture.runAsync(() -> {
          try {
            Set<String> recipientAddresses = new HashSet<>();
            if (!CollectionUtils.isEmpty(partner.getAddresses())) {
              recipientAddresses.addAll(partner.getAddresses());
            }
            if (!CollectionUtils.isEmpty(emailComposed.getTo())) {
              recipientAddresses.addAll(emailComposed.getTo());
            }
            if (!recipientAddresses.isEmpty()) {
              MimeMessageHelper helper = this.builderHelper(
                  emailComposed, mailSender, validFilesUpload, validFileStorages);
              helper.setTo(recipientAddresses.toArray(new String[0]));
              logger.info(
                  Arrays.toString(helper.getMimeMessage().getRecipients(Message.RecipientType.TO)));
              mailSender.send(helper.getMimeMessage());
            }
          } catch (Exception e) {
            logger.error("An unexpected error occurred during email sending", e);
            throw new RuntimeException(e);
          }
        }, executor))
        .toArray(CompletableFuture[]::new);
    CompletableFuture.allOf(futures).join();
    executor.shutdown();
  }

  MimeMessageHelper builderHelper(EmailComposedModel emailComposed, JavaMailSender mailSender,
                                  List<MultipartFile> validFilesUpload,
                                  List<File> validFileStorages)
      throws MessagingException, IOException, BusinessException {
    MimeMessage newMessage = mailSender.createMimeMessage();
    MimeMessageHelper newHelper = new MimeMessageHelper(newMessage, true, "UTF-8");
    newHelper.setSubject(emailComposed.getSubject());
    newHelper.setFrom(new InternetAddress(emailComposed.getEmailSender()).getAddress());
    if (!CollectionUtils.isEmpty(emailComposed.getCc())) {
      newHelper.setCc(emailComposed.getCc().toArray(new String[0]));
    }
    MimeMultipart parentMultipart = new MimeMultipart("mixed");

    this.processHtmlContentWithBase64Images(emailComposed.getContent(), newHelper, parentMultipart);
    for (MultipartFile file : validFilesUpload) {
      MimeBodyPart attachmentPart = new MimeBodyPart();
      attachmentPart.setDataHandler(
          new DataHandler(new ByteArrayDataSource(file.getBytes(), file.getContentType())));
      attachmentPart.setFileName(file.getOriginalFilename());
      parentMultipart.addBodyPart(attachmentPart);
    }

    for (File file : validFileStorages) {
      MimeBodyPart attachmentPart = new MimeBodyPart();
      byte[] fileBytes = Files.readAllBytes(file.toPath());
      attachmentPart.setDataHandler(
          new DataHandler(
              new ByteArrayDataSource(fileBytes, Files.probeContentType(file.toPath()))));
      attachmentPart.setFileName(file.getName());
      parentMultipart.addBodyPart(attachmentPart);
    }
    newMessage.setContent(parentMultipart);
    return newHelper;
  }

  void processHtmlContentWithBase64Images(String htmlContent, MimeMessageHelper newHelper,
                                          MimeMultipart parentMultipart)
      throws MessagingException {
    MimeMultipart relatedMultipart = new MimeMultipart("related");
    MimeBodyPart textPart = new MimeBodyPart();
    textPart.setContent(htmlContent, "text/html; charset=UTF-8");
    relatedMultipart.addBodyPart(textPart);

    String base64ImagePattern = "data:image/(.*?);base64,([A-Za-z0-9+/=]+)";
    Pattern pattern = Pattern.compile(base64ImagePattern);
    Matcher matcher = pattern.matcher(htmlContent);

    while (matcher.find()) {
      String contentType = matcher.group(1);
      String base64Data = matcher.group(2);
      String cid = "image" + System.nanoTime();
      byte[] imageBytes = Base64.getDecoder().decode(base64Data);
      DataSource dataSource = new ByteArrayDataSource(imageBytes, "image/" + contentType);
      newHelper.addInline(cid, dataSource);
      MimeBodyPart imagePart = new MimeBodyPart();
      imagePart.setDataHandler(new DataHandler(dataSource));
      imagePart.setHeader("Content-ID", "<" + cid + ">");
      relatedMultipart.addBodyPart(imagePart);
      htmlContent = htmlContent.replace(matcher.group(0), "cid:" + cid);
    }

    textPart.setContent(htmlContent, "text/html; charset=UTF-8");

    MimeBodyPart contentPart = new MimeBodyPart();
    contentPart.setContent(relatedMultipart);
    parentMultipart.addBodyPart(contentPart);
  }

}

