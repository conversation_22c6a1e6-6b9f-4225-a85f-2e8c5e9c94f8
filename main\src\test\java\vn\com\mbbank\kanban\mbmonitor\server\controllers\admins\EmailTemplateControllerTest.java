package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailTemplatePaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailTemplateRequest;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailTemplateDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailTemplateService;
import vn.com.mbbank.kanban.test.ApplicationTest;

@ExtendWith({MockitoExtension.class})
class EmailTemplateControllerTest extends ApplicationTest {

  @Mock
  EmailTemplateService service;

  @Mock
  EmailTemplateDependencyService emailTemplateDependencyService;

  @InjectMocks
  @Spy
  EmailTemplateController controller;


  @Test
  void findAll_success() {
    when(service.findAll()).thenReturn(List.of());
    var res = controller.findAll();
    verify(service, times(1)).findAll();
    assertNotNull(res.getData());
  }
  @TestForDev
  void download_success() throws BusinessException, IOException {
    when(service.download(any())).thenReturn(null);
    controller.download(1L);
    verify(service,times(1)).download(any());
  }

  @Test
  void findById_success() throws BusinessException {
    when(service.findEmailTemplateById(any())).thenReturn(null);
    var res = controller.findById(1L);
    verify(service, times(1)).findEmailTemplateById(any());
    assertNull(res.getData());
  }

  @Test
  void createOrUpdate_success() throws BusinessException, IOException {
    doNothing().when(controller)
        .makeSureCreateOrUpdate(any(), any(), any(), any());
    var res = controller.createOrUpdate(new EmailTemplateRequest(), List.of());
    verify(service, times(1)).createOrUpdate(any(), any());
    assertNull(res.getData());
  }

  @Test
  void deleteById_success() throws BusinessException {
    var res = controller.deleteById(any());
    verify(emailTemplateDependencyService, times(1)).deleteWithId(any());
    assertEquals("OK", res.getData());
  }

}