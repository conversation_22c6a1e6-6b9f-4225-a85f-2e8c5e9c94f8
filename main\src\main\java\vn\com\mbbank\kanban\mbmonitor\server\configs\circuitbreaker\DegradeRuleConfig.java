package vn.com.mbbank.kanban.mbmonitor.server.configs.circuitbreaker;


import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration class for managing Sentinel degrade rules.
 * Reads configuration properties prefixed with "mbmonitor.sentinel.degrade"
 * and maps them to a list of {@link DegradeRule} objects.
 */
@Component
@ConfigurationProperties(prefix = "mbmonitor.sentinel.degrade")
@Data
public class DegradeRuleConfig {

  private List<DegradeRule> rules;
}