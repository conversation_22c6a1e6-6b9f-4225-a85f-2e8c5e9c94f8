package vn.com.mbbank.kanban.mbmonitor.server.services.impl;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.JpaEntityInformationSupport;
import vn.com.mbbank.kanban.core.enums.SortType;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CollectEmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.JobKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailConfigPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.CollectEmailConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.EmailConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailService;
import vn.com.mbbank.kanban.test.ApplicationTest;

@ExtendWith(MockitoExtension.class)
class EmailConfigServiceImplTest extends ApplicationTest {

  @Mock
  EmailConfigRepository emailConfigRepository;
  @Mock
  CollectEmailConfigRepository collectEmailConfigRepository;
  @Mock
  JobKafkaProducerService jobKafkaProducerService;
  @Mock
  SysLogKafkaProducerService sysLogKafkaProducerService;
  @Mock
  EmailService emailService;

  @InjectMocks
  EmailConfigServiceImpl emailConfigService;

  @Test
  void getRepository_success() {
    JpaCommonRepository<EmailConfigEntity, Long> result = emailConfigService.getRepository();
    assertEquals(emailConfigRepository, result);
  }

  @Test
  void updateStatus_success() throws BusinessException {
    when(emailConfigRepository.findById(any())).thenReturn(Optional.of(new EmailConfigEntity()));
    when(emailConfigRepository.save(any())).thenReturn(new EmailConfigEntity());
    var res = emailConfigService.updateStatus(1L);
    assertNotNull(res);
  }

  @Test
  void updateStatus_success_active() throws BusinessException {
    var config = new EmailConfigEntity();
    config.setActive(true);
    when(emailConfigRepository.findById(any())).thenReturn(Optional.of(config));
    when(emailConfigRepository.save(any())).thenReturn(config);
    var res = emailConfigService.updateStatus(1L);
    assertNotNull(res);
  }

  @Test
  void updateStatus_errror_notFoundCollectEmailConfig() {
    when(emailConfigRepository.findById(any())).thenReturn(Optional.empty());
    assertThrows(BusinessException.class, () -> {
      emailConfigService.updateStatus(1L);
    }, ErrorCode.COLLECT_EMAIL_CONFIG_NOT_FOUND.getMessage());
  }

  @Test
  void existsByEmailAndProtocolTypeIn_success() {
    when(emailConfigRepository.existsByEmailAndProtocolTypeIn(any(), any())).thenReturn(true);
    var res = emailConfigService.existByEmailAndProtocolTypeIn("1", List.of());
    verify(emailConfigRepository, times(1)).existsByEmailAndProtocolTypeIn(any(), any());
    assertEquals(true, res);
  }

  @Test
  void existByIdNotAndName_success() {
    when(emailConfigRepository.existsByIdNotAndEmailAndProtocolTypeIn(any(), any(),
        any())).thenReturn(true);
    var res = emailConfigService.existByIdNotAndEmailAndProtocolTypeIn(1L, "1", List.of());
    verify(emailConfigRepository, times(1)).existsByIdNotAndEmailAndProtocolTypeIn(any(), any(),
        any());
    assertEquals(true, res);
  }

  @Test
  void findWithPaging_success_emailNotEmpty() throws BusinessException {
    JpaEntityInformation mockEntityInformation = mock(JpaEntityInformation.class);
    when(mockEntityInformation.getIdAttributeNames()).thenReturn(Set.of("id"));
    try (MockedStatic<JpaEntityInformationSupport> mockedStatic = Mockito.mockStatic(
        JpaEntityInformationSupport.class)) {
      mockedStatic.when(() -> JpaEntityInformationSupport.getEntityInformation(any(), any()))
          .thenReturn(mockEntityInformation);
      EmailConfigPaginationRequest emailConfigPaginationRequest =
          new EmailConfigPaginationRequest();
      emailConfigPaginationRequest.setSortOrder(SortType.ASC);
      emailConfigPaginationRequest.setSortBy("123");
      emailConfigPaginationRequest.setEmail("123");
      emailConfigPaginationRequest.setProtocolTypes(List.of(EmailProtocolTypeEnum.SMTP));
      when(emailConfigRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(
          Page.empty());
      var res = emailConfigService.findAll(emailConfigPaginationRequest);
      assertNotNull(res.getContent());
    }
  }

  @Test
  void findWithPaging_success_protocolIsEmpty() throws BusinessException {
    JpaEntityInformation mockEntityInformation = mock(JpaEntityInformation.class);
    when(mockEntityInformation.getIdAttributeNames()).thenReturn(Set.of("id"));
    try (MockedStatic<JpaEntityInformationSupport> mockedStatic = Mockito.mockStatic(
        JpaEntityInformationSupport.class)) {
      mockedStatic.when(() -> JpaEntityInformationSupport.getEntityInformation(any(), any()))
          .thenReturn(mockEntityInformation);
      EmailConfigPaginationRequest emailConfigPaginationRequest =
          new EmailConfigPaginationRequest();
      emailConfigPaginationRequest.setSortOrder(SortType.ASC);
      emailConfigPaginationRequest.setSortBy("123");
      emailConfigPaginationRequest.setEmail("123");
      emailConfigPaginationRequest.setProtocolTypes(List.of());
      when(emailConfigRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(
          Page.empty());
      var res = emailConfigService.findAll(emailConfigPaginationRequest);
      assertNotNull(res.getContent());
    }
  }

  @Test
  void findWithPaging_success_emailIsEmpty() throws BusinessException {
    JpaEntityInformation mockEntityInformation = mock(JpaEntityInformation.class);
    when(mockEntityInformation.getIdAttributeNames()).thenReturn(Set.of("id"));
    try (MockedStatic<JpaEntityInformationSupport> mockedStatic = Mockito.mockStatic(
        JpaEntityInformationSupport.class)) {
      mockedStatic.when(() -> JpaEntityInformationSupport.getEntityInformation(any(), any()))
          .thenReturn(mockEntityInformation);
      EmailConfigPaginationRequest emailConfigPaginationRequest =
          new EmailConfigPaginationRequest();
      emailConfigPaginationRequest.setSortOrder(SortType.ASC);
      emailConfigPaginationRequest.setSortBy("123");
      emailConfigPaginationRequest.setProtocolTypes(List.of());
      when(emailConfigRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(
          Page.empty());
      var res = emailConfigService.findAll(emailConfigPaginationRequest);
      assertNotNull(res.getContent());
    }
  }

  @Test
  void deleteEmailConfigById_existDepend() {
    when(emailConfigRepository.findById(anyLong())).thenReturn(Optional.of(new EmailConfigEntity()));
    when(collectEmailConfigRepository.findAllByEmailConfigId(any())).thenReturn(
        List.of(new CollectEmailConfigEntity()));
    assertThrows(BusinessException.class, () -> {
      emailConfigService.deleteEmailConfigById(1L);
    }, ErrorCode.EMAIL_CONFIG_DEPENDENCY_ERROR.getMessage());
  }

  @Test
  void deleteEmailConfigById_success() throws BusinessException {
    when(emailConfigRepository.findById(anyLong())).thenReturn(Optional.of(new EmailConfigEntity()));
    when(collectEmailConfigRepository.findAllByEmailConfigId(any())).thenReturn(List.of());
    emailConfigService.deleteEmailConfigById(1L);
  }

  @Test
  void findWithPaging_success_isWithInactived() throws BusinessException {
    JpaEntityInformation mockEntityInformation = mock(JpaEntityInformation.class);
    when(mockEntityInformation.getIdAttributeNames()).thenReturn(Set.of("id"));
    try (MockedStatic<JpaEntityInformationSupport> mockedStatic = Mockito.mockStatic(
        JpaEntityInformationSupport.class)) {
      mockedStatic.when(() -> JpaEntityInformationSupport.getEntityInformation(any(), any()))
          .thenReturn(mockEntityInformation);
      EmailConfigPaginationRequest emailConfigPaginationRequest =
          new EmailConfigPaginationRequest();
      emailConfigPaginationRequest.setSortOrder(SortType.ASC);
      emailConfigPaginationRequest.setSortBy("123");
      emailConfigPaginationRequest.setProtocolTypes(List.of());
      emailConfigPaginationRequest.setWithInactived(true);
      when(emailConfigRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(
          Page.empty());
      var res = emailConfigService.findAll(emailConfigPaginationRequest);
      assertNotNull(res.getContent());
    }
  }


  @Test
  void createOrUpdate_success_imap() throws BusinessException {
    EmailConfigRequest emailConfigRequest = new EmailConfigRequest();
    emailConfigRequest.setUsername("");
    emailConfigRequest.setId(1L);
    when(emailConfigRepository.findById(any())).thenReturn(Optional.of(new EmailConfigEntity()));
    emailConfigRequest.setProtocolType(EmailProtocolTypeEnum.IMAP);
    var res = emailConfigService.createOrUpdate(emailConfigRequest);
    assertNull(res);
  }

  @Test
  void createOrUpdate_success_smtp() throws BusinessException {
    EmailConfigRequest emailConfigRequest = new EmailConfigRequest();
    emailConfigRequest.setUsername("");
    emailConfigRequest.setId(1L);
    when(emailConfigRepository.findById(any())).thenReturn(Optional.of(new EmailConfigEntity()));

    emailConfigRequest.setProtocolType(EmailProtocolTypeEnum.SMTP);
    var res = emailConfigService.createOrUpdate(emailConfigRequest);
    assertNull(res);
  }

  @Test
  void createOrUpdate_success_idIsZero() throws BusinessException {
    EmailConfigRequest emailConfigRequest = new EmailConfigRequest();
    emailConfigRequest.setUsername("");
    emailConfigRequest.setId(0L);
    emailConfigRequest.setProtocolType(EmailProtocolTypeEnum.SMTP);
    var res = emailConfigService.createOrUpdate(emailConfigRequest);
    assertNull(res);
  }

  @Test
  void createOrUpdate_success_idIsNull() throws BusinessException {
    EmailConfigRequest emailConfigRequest = new EmailConfigRequest();
    emailConfigRequest.setUsername("");
    emailConfigRequest.setId(null);
    emailConfigRequest.setProtocolType(EmailProtocolTypeEnum.SMTP);
    var res = emailConfigService.createOrUpdate(emailConfigRequest);
    assertNull(res);
  }


  @Test
  void createOrUpdate__error_emailConfigNotFound() {
    when(emailConfigRepository.findById(any())).thenReturn(Optional.empty());
    EmailConfigRequest emailConfigRequest = new EmailConfigRequest();
    emailConfigRequest.setId(1L);
    emailConfigRequest.setProtocolType(EmailProtocolTypeEnum.SMTP);
    assertThrows(BusinessException.class, () -> {
      emailConfigService.createOrUpdate(emailConfigRequest);
    }, ErrorCode.EMAIL_CONFIG_EXIST.getMessage());
  }

  @Test
  void createOrUpdate__success_update() {
    when(emailConfigRepository.findById(any())).thenReturn(Optional.of(new EmailConfigEntity()));
    EmailConfigRequest emailConfigRequest = new EmailConfigRequest();
    emailConfigRequest.setId(1L);
    emailConfigRequest.setProtocolType(EmailProtocolTypeEnum.SMTP);
    try {
      var res = emailConfigService.createOrUpdate(emailConfigRequest);
      assertNull(res);
    } catch (BusinessException e) {
      throw new RuntimeException(e);
    }

  }

  @Test
  void createOrUpdate__success_update_sameEmail() {
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setUsername("123");
    emailConfigEntity.setProtocolType(EmailProtocolTypeEnum.SMTP);
    when(emailConfigRepository.findById(any())).thenReturn(Optional.of(emailConfigEntity));
    EmailConfigRequest emailConfigRequest = new EmailConfigRequest();
    emailConfigRequest.setId(1L);
    emailConfigRequest.setProtocolType(EmailProtocolTypeEnum.SMTP);
    emailConfigRequest.setUsername("123");
    try {
      var res = emailConfigService.createOrUpdate(emailConfigRequest);
      assertNull(res);
    } catch (BusinessException e) {
      throw new RuntimeException(e);
    }

  }

  @Test
  void createOrUpdate__success_update_notEmptyConfigs() {
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setUsername("123");
    when(emailConfigRepository.findById(any())).thenReturn(Optional.of(emailConfigEntity));
    when(collectEmailConfigRepository.findAllByEmailConfigId(any())).thenReturn(
        List.of(new CollectEmailConfigEntity()));
    EmailConfigRequest emailConfigRequest = new EmailConfigRequest();
    emailConfigRequest.setId(1L);
    emailConfigRequest.setProtocolType(EmailProtocolTypeEnum.SMTP);
    emailConfigRequest.setUsername("1223");
    try {
      emailConfigService.createOrUpdate(emailConfigRequest);
    } catch (BusinessException e) {
      assertNotNull(e);
    }
  }

  @Test
  void createOrUpdate__success_update_case() {
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setUsername("1223");
    emailConfigEntity.setProtocolType(EmailProtocolTypeEnum.SMTP);
    when(emailConfigRepository.findById(any())).thenReturn(Optional.of(emailConfigEntity));
    EmailConfigRequest emailConfigRequest = new EmailConfigRequest();
    emailConfigRequest.setId(1L);
    emailConfigRequest.setProtocolType(EmailProtocolTypeEnum.SMTP);
    emailConfigRequest.setUsername("1223");
    try {
      emailConfigService.createOrUpdate(emailConfigRequest);
    } catch (BusinessException e) {
      assertNotNull(e);
    }
  }

  @Test
  void validateSaveCustomObjectRequest_error_collectEmailConfigNameExist() {
    when(emailConfigRepository.existsByEmailAndProtocolTypeIn(any(), any())).thenReturn(true);
    EmailConfigRequest emailConfigRequest = new EmailConfigRequest();
    emailConfigRequest.setUsername("");
    emailConfigRequest.setId(0L);
    emailConfigRequest.setProtocolType(EmailProtocolTypeEnum.SMTP);
    assertThrows(BusinessException.class, () -> {
      emailConfigService.validateSaveEmailConfigRequest(emailConfigRequest);
    }, ErrorCode.EMAIL_CONFIG_EXIST.getMessage());
  }

  @Test
  void validateSaveCustomObjectRequest_error_caseIdNull() {
    when(emailConfigRepository.existsByEmailAndProtocolTypeIn(any(), any())).thenReturn(true);
    EmailConfigRequest emailConfigRequest = new EmailConfigRequest();
    emailConfigRequest.setUsername("");
    emailConfigRequest.setProtocolType(EmailProtocolTypeEnum.SMTP);
    assertThrows(BusinessException.class, () -> {
      emailConfigService.validateSaveEmailConfigRequest(emailConfigRequest);
    }, ErrorCode.EMAIL_CONFIG_EXIST.getMessage());
  }

  @Test
  void validateSaveCustomObjectRequest_error_collectEmailConfigNameExist2() {
    when(emailConfigRepository.existsByEmailAndProtocolTypeIn(any(), any())).thenReturn(true);
    EmailConfigRequest emailConfigRequest = new EmailConfigRequest();
    emailConfigRequest.setUsername("");
    emailConfigRequest.setId(null);
    emailConfigRequest.setProtocolType(EmailProtocolTypeEnum.SMTP);
    assertThrows(BusinessException.class, () -> {
      emailConfigService.validateSaveEmailConfigRequest(emailConfigRequest);
    }, ErrorCode.EMAIL_CONFIG_EXIST.getMessage());
  }
  @Test
  void validateSaveEmailConfigRequest_error_collectEmailConfigFound() {
    EmailConfigRequest emailConfigRequest = new EmailConfigRequest();
    emailConfigRequest.setId(1L);
    emailConfigRequest.setUsername("");
    emailConfigRequest.setProtocolType(EmailProtocolTypeEnum.SMTP);
    when(emailConfigRepository.existsByIdNotAndEmailAndProtocolTypeIn(any(), any(),
        any())).thenReturn(true);
    assertThrows(BusinessException.class, () -> {
      emailConfigService.validateSaveEmailConfigRequest(emailConfigRequest);
    }, ErrorCode.EMAIL_CONFIG_EXIST.getMessage());
  }

  @Test
  void findAllByProtocolTypeIn_success() {
    when(emailConfigRepository.findAllByProtocolTypeIn(List.of())).thenReturn(List.of());
    var res = emailConfigService.findAllByProtocolTypeIn(List.of());
    assertNotNull(res);
  }

  @Test
  void findByUserName() {
    var emailConfig = new EmailConfigEntity();
    emailConfig.setId(1L);
    emailConfig.setEmail("<EMAIL>");
    doReturn(Optional.of(emailConfig)).when(emailConfigRepository)
        .findByEmailAndProtocolType(any(), any());
    var result = emailConfigService.findByEmailAndProtocolType(any(), any());
    assertEquals(result.get().getId(), 1);
  }

  @Test
  void updateStatus_failed_caseNotFound() {
    when(emailConfigRepository.findById(anyLong())).thenReturn(Optional.empty());
    Assertions.assertThrows(BusinessException.class, () -> emailConfigService.updateStatus(1L));
  }

  @Test
  void updateStatus__success_caseNotFound() throws BusinessException {
    var emailConfig = new EmailConfigEntity();
    emailConfig.setActive(true);
    emailConfig.setExecuted(true);
    when(emailConfigRepository.findById(anyLong())).thenReturn(Optional.of(emailConfig));
    when(emailConfigRepository.save(any())).thenReturn(emailConfig);
    var res = emailConfigService.updateStatus(1L);
    assertNotNull(res);
  }

  @Test
  void updateStatus__success_caseNotFound_SMTP() throws BusinessException {
    var emailConfig = new EmailConfigEntity();
    emailConfig.setActive(true);
    emailConfig.setExecuted(true);
    emailConfig.setProtocolType(EmailProtocolTypeEnum.SMTP);
    when(emailConfigRepository.findById(anyLong())).thenReturn(Optional.of(emailConfig));
    when(emailConfigRepository.save(any())).thenReturn(emailConfig);
    var res = emailConfigService.updateStatus(1L);
    assertNotNull(res);
  }


}