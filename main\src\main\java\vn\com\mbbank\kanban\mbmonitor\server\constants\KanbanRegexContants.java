package vn.com.mbbank.kanban.mbmonitor.server.constants;

/**
 * Regex text.
 *
 * <AUTHOR>
 * @created_date 8/23/2024
 */
public class KanbanRegexContants {
  public static final String JSON_PATH = "^\\$\\.(\\w+)(\\[\\d+\\])?(\\.\\w+)*(\\[\\d+\\])*$";
  public static final String USER_NAME = "^[a-zA-Z0-9_.-]+$";

  public static final String SERVICE_APPLICATION_PATTERN = "^[\\p{L}\\p{N} ,._-]*$";
  public static final String CUSTOM_OBJECT_PATTERN = "^[\\p{L}\\p{N} ,._-]*$";
  public static final String SIMPLE_TEXT_PATTERN = "^[\\p{L}\\p{N} ,._-]*$";
  public static final String EMAIL_PATTERN = "^[a-zA-Z0-9_!#$%&'*+/=?`{|}~^.-]+@[a-zA-Z0-9.-]+$";
  public static final String COMMON_FIELD_NAME = "^[a-zA-Z0-9_.-]+$";
  /**
   * List constants of email config.
   */
  public static final String COLLECT_EMAIL_CONFIG_NAME_PATTERN = "^[\\p{L}\\p{N} ,._-]*$";
  public static final String EMAIL_HOST_PATTERN = "^[a-zA-Z0-9 ._-]*$";
  public static final String EMAIL_ADDRESS_PATTERN = "^[a-zA-Z0-9 @._-]*$";
  public static final String EMAIL_PASSWORD_PATTERN = EMAIL_ADDRESS_PATTERN;

  public static final String DATABASE_COLUMN = "^[A-Za-z0-9_]+$";

}
