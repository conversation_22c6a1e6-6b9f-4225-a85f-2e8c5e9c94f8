package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.Collections;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.MonitorWebConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.MonitorWebConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.MonitorWebConfigService;

/**
 * Controller logic monitor web config.
 */
@RestController
@RequestMapping(ServerUrl.WEB_MONITOR_CONFIG_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class MonitorWebConfigController extends BaseController {
  MonitorWebConfigService monitorWebConfigService;
  
  /**
   * Find all monitor web configs with paging.
   *
   * @param paginationRequest paginationRequest
   * @return all monitor web config config MonitorWebConfigResponse
   */
  @HasPermission(value = {
    @AclPermission(module = PermissionModuleEnum.MONITOR_WEB_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @Operation(summary = "Find all monitor web configs with paging.")
  @GetMapping
  public ResponseData<Page<MonitorWebConfigResponse>> findAll(
      @ModelAttribute PaginationRequest paginationRequest) {
    return ResponseUtils.success(monitorWebConfigService.findAll(paginationRequest));
  }
  

  /**
   * Create or update monitor web config.
   *
   * @param request request
   * @return new monitor web config MonitorWebConfigResponse
   */
  
  @Operation(summary = "Create or update monitor web config.")
  @PostMapping
  public ResponseData<MonitorWebConfigResponse> createOrUpdate(
        @RequestBody @Valid MonitorWebConfigRequest request) throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.MONITOR_WEB_CONFIG, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(request.getId()));
    return ResponseUtils.success(monitorWebConfigService.createOrUpdate(request));
  }

  /**
   * Find monitor web config by id.
   *
   * @param id id
   * @return data
   */
  @HasPermission(value = {
    @AclPermission(module = PermissionModuleEnum.MONITOR_WEB_CONFIG, action = PermissionActionEnum.VIEW)})
  @Operation(summary = "Find monitor web config by id.")
  @GetMapping("/{id}")
  public ResponseData<MonitorWebConfigResponse> findById(@PathVariable String id) throws Exception {
    return ResponseUtils.success(monitorWebConfigService.findWithId(id));
  }

  /**
   * Delete monitor web config by id.
   *
   * @param id id
   * @return Object
   */
  @HasPermission(value = {
    @AclPermission(module = PermissionModuleEnum.MONITOR_WEB_CONFIG, action = PermissionActionEnum.DELETE)
  })
  @Operation(summary = "Delete monitor web config by id.")
  @DeleteMapping("/{id}")
  public ResponseData<String> deleteById(@PathVariable String id) throws BusinessException {
    monitorWebConfigService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }

  /**
   * Active or inactive monitor web config by id.
   *
   * @param id id
   * @return Object
   */
  @HasPermission(value = {
    @AclPermission(module = PermissionModuleEnum.MONITOR_WEB_CONFIG, action = PermissionActionEnum.EDIT),
  })
  @Operation(summary = "Active or inactive monitor web config by id.")
  @PutMapping("/{id}/toggle-status")
  public ResponseData<MonitorWebConfigResponse> active(@PathVariable String id) throws BusinessException {
    return ResponseUtils.success(monitorWebConfigService.updateStatus(id));
  }
  
}
