package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;


import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;

/**
 * Model view list email config to filter in alert table tab collect email config.
 */

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailConfigPaginationRequest extends PaginationRequestDTO {
  List<EmailProtocolTypeEnum> protocolTypes;
  String email;
  boolean withInactived;
}

