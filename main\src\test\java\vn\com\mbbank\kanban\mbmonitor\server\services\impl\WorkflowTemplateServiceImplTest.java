package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.BaseFormBuilderElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.BaseFormBuilderInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.CheckboxElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.NumberInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.OptionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.RadioElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.RowElement;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.SectionElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.SelectElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.TextInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowEdgeModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowNodeModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowNodePositionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.EmailNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.ExecutionNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.StartEndNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.TeamsNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.WorkflowTemplateRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowTemplateEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.FormBuilderElementTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailTemplateModel;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.WorkflowTemplateRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.CustomInputService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailTemplateService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionService;
import vn.com.mbbank.kanban.mbmonitor.server.services.WorkflowNodeService;
import vn.com.mbbank.kanban.mbmonitor.server.services.WorkflowService;

@ExtendWith(MockitoExtension.class)
class WorkflowTemplateServiceImplTest {

  @Mock
  private WorkflowTemplateRepository workflowTemplateRepository;

  @Mock
  private CustomInputService customInputService;

  @Mock
  private WorkflowNodeService workflowNodeService;

  @Mock
  private EmailTemplateService emailTemplateService;

  @Mock
  private WorkflowService workflowService;

  @Mock
  private ExecutionService executionService;

  @InjectMocks
  private WorkflowTemplateServiceImpl workflowTemplateService;

  // Test data
  private final String testId = "test-id-123";
  private final String testName = "Test Workflow Template";
  private final String testDescription = "Test Description";

  @Test
  void getRepository_success() {
    // Act
    JpaCommonRepository<WorkflowTemplateEntity, String> result = workflowTemplateService.getRepository();

    // Assert
    assertEquals(workflowTemplateRepository, result);
  }

  @Test
  void findWithId_success() throws BusinessException {
    // Arrange
    WorkflowTemplateEntity workflowTemplateEntity = createTestWorkflowTemplateEntity();
    workflowTemplateEntity.setWorkflowId(testId); // Set workflow ID to match entity ID
    when(workflowTemplateRepository.findById(testId)).thenReturn(Optional.of(workflowTemplateEntity));
    when(customInputService.findAllById(anyList())).thenReturn(List.of());

    // Mock workflow service to handle the workflow ID from entity
    WorkflowModel mockWorkflow = new WorkflowModel();
    when(workflowService.findWithId(testId)).thenReturn(mockWorkflow);

    // Act
    var result = workflowTemplateService.findWithId(testId);

    // Assert
    assertNotNull(result);
    assertEquals(testId, result.getId());
    assertEquals(testName, result.getName());
    assertEquals(testDescription, result.getDescription());
    verify(workflowTemplateRepository, times(1)).findById(testId);
    verify(workflowService, times(1)).findWithId(testId);
  }

  @Test
  void findWithId_failed_whenNotFound() {
    // Arrange
    when(workflowTemplateRepository.findById(testId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.findWithId(testId);
    });

    verify(workflowTemplateRepository, times(1)).findById(testId);
  }

  @Test
  void createOrUpdate_success_whenCreate() throws BusinessException {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode
    request.setWorkflow(createValidWorkflowWithEdges()); // Use workflow with proper edges
    WorkflowTemplateEntity savedEntity = createTestWorkflowTemplateEntity();

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(workflowTemplateRepository.save(any(WorkflowTemplateEntity.class))).thenReturn(savedEntity);
    when(emailTemplateService.findEmailTemplateById(anyLong())).thenReturn(new vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailTemplateModel());

    // Mock workflow creation to return a workflow with proper ID
    WorkflowEntity mockWorkflow = new WorkflowEntity();
    mockWorkflow.setId(testId);
    when(workflowService.create(any(WorkflowModel.class))).thenReturn(mockWorkflow);

    // Act
    WorkflowTemplateEntity result = workflowTemplateService.createOrUpdate(request);

    // Assert
    assertNotNull(result);
    assertEquals(testId, result.getId());
    assertEquals(testName, result.getName());
    verify(workflowTemplateRepository, times(1)).existsByNameIgnoreCase(testName);
    verify(workflowTemplateRepository, times(1)).save(any(WorkflowTemplateEntity.class));
    verify(workflowTemplateRepository, never()).findById(anyString());
  }

  @Test
  void createOrUpdate_success_whenUpdate() throws BusinessException {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setWorkflow(createValidWorkflowWithEdges()); // Use workflow with proper edges
    WorkflowTemplateEntity existingEntity = createTestWorkflowTemplateEntity();
    WorkflowTemplateEntity savedEntity = createTestWorkflowTemplateEntity();

    when(workflowTemplateRepository.existsByIdNotAndNameIgnoreCase(testId, testName)).thenReturn(false);
    when(workflowTemplateRepository.findById(testId)).thenReturn(Optional.of(existingEntity));
    when(workflowTemplateRepository.save(any(WorkflowTemplateEntity.class))).thenReturn(savedEntity);
    when(emailTemplateService.findEmailTemplateById(anyLong())).thenReturn(new vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailTemplateModel());

    // Mock workflow creation to return a workflow with proper ID
    WorkflowEntity mockWorkflow = new WorkflowEntity();
    mockWorkflow.setId(testId);
    when(workflowService.create(any(WorkflowModel.class))).thenReturn(mockWorkflow);

    // Act
    WorkflowTemplateEntity result = workflowTemplateService.createOrUpdate(request);

    // Assert
    assertNotNull(result);
    assertEquals(testId, result.getId());
    assertEquals(testName, result.getName());
    verify(workflowTemplateRepository, times(1)).existsByIdNotAndNameIgnoreCase(testId, testName);
    verify(workflowTemplateRepository, times(1)).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenNameExistsInCreateMode() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    when(workflowTemplateRepository.existsByNameIgnoreCase(testName)).thenReturn(true);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, times(1)).existsByNameIgnoreCase(testName);
    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenNameExistsInUpdateMode() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    WorkflowTemplateEntity existingEntity = createTestWorkflowTemplateEntity();

    // Mock findById to return existing entity first
    when(workflowTemplateRepository.findById(testId)).thenReturn(Optional.of(existingEntity));
    when(workflowTemplateRepository.existsByIdNotAndNameIgnoreCase(testId, testName)).thenReturn(true);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, times(1)).findById(testId);
    verify(workflowTemplateRepository, times(1)).existsByIdNotAndNameIgnoreCase(testId, testName);
    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenEntityNotFoundInUpdateMode() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();

    // findById is called first and throws exception immediately
    when(workflowTemplateRepository.findById(testId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    assertEquals(ErrorCode.WORKFLOW_TEMPLATE_NOT_FOUND.getCode(), exception.getCode());
    verify(workflowTemplateRepository, times(1)).findById(testId);
    // existsByIdNotAndNameIgnoreCase is never called because findById throws exception first
    verify(workflowTemplateRepository, never()).existsByIdNotAndNameIgnoreCase(anyString(), anyString());
    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void deleteWithId_success() throws BusinessException {
    // Arrange
    WorkflowTemplateEntity workflowTemplateEntity = createTestWorkflowTemplateEntity();
    when(workflowTemplateRepository.findById(testId)).thenReturn(Optional.of(workflowTemplateEntity));

    // Act
    workflowTemplateService.deleteWithId(testId);

    // Assert
    verify(workflowTemplateRepository, times(1)).findById(testId);
    verify(workflowTemplateRepository, times(1)).delete(workflowTemplateEntity);
    // workflowNodeService.deleteAllByWorkflowId is not called in the actual implementation
  }

  @Test
  void deleteWithId_failed_whenNotFound() {
    // Arrange
    when(workflowTemplateRepository.findById(testId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.deleteWithId(testId);
    });

    assertEquals(ErrorCode.WORKFLOW_TEMPLATE_NOT_FOUND.getCode(), exception.getCode());
    verify(workflowTemplateRepository, times(1)).findById(testId);
    verify(workflowTemplateRepository, never()).delete(any(WorkflowTemplateEntity.class));
  }

  // Helper methods to create test data
  private WorkflowTemplateEntity createTestWorkflowTemplateEntity() {
    WorkflowTemplateEntity entity = new WorkflowTemplateEntity();
    entity.setId(testId);
    entity.setName(testName);
    entity.setDescription(testDescription);
    entity.setFormLayout(new ArrayList<>());
    return entity;
  }

  private WorkflowTemplateRequest createTestWorkflowTemplateRequest() {
    return WorkflowTemplateRequest.builder()
        .id(testId)
        .name(testName)
        .description(testDescription)
        .formLayout(new ArrayList<>())
        .workflow(createValidWorkflow())
        .build();
  }

  private WorkflowModel createValidWorkflow() {
    WorkflowModel workflow = new WorkflowModel();
    List<WorkflowNodeModel> nodes = new ArrayList<>();

    WorkflowNodeModel startNode = createStartNode("start-1", "Start Node");
    nodes.add(startNode);

    WorkflowNodeModel emailNode = createEmailNode("email-1", "Email Node", List.of("1"));
    nodes.add(emailNode);

    WorkflowNodeModel endNode = createEndNode("end-1", "End Node");
    nodes.add(endNode);

    workflow.setNodes(nodes);
    workflow.setEdges(new ArrayList<>());
    return workflow;
  }

  private WorkflowModel createValidWorkflowWithEdges() {
    WorkflowModel workflow = new WorkflowModel();
    List<WorkflowNodeModel> nodes = new ArrayList<>();
    List<WorkflowEdgeModel> edges = new ArrayList<>();

    WorkflowNodeModel startNode = createStartNode("start-1", "Start Node");
    nodes.add(startNode);

    WorkflowNodeModel emailNode = createEmailNode("email-1", "Email Node", List.of("1"));
    nodes.add(emailNode);

    WorkflowNodeModel endNode = createEndNode("end-1", "End Node");
    nodes.add(endNode);

    // Create edges to connect the nodes
    WorkflowEdgeModel edge1 = new WorkflowEdgeModel();
    edge1.setSource("start-1");
    edge1.setTarget("email-1");
    edges.add(edge1);

    WorkflowEdgeModel edge2 = new WorkflowEdgeModel();
    edge2.setSource("email-1");
    edge2.setTarget("end-1");
    edges.add(edge2);

    workflow.setNodes(nodes);
    workflow.setEdges(edges);
    return workflow;
  }

  private WorkflowNodeModel createStartNode(String id, String name) {
    WorkflowNodeModel node = new WorkflowNodeModel();
    node.setId(id);
    node.setType(WorkflowNodeTypeEnum.START);
    node.setPosition(new WorkflowNodePositionModel());
    node.setStatus(WorkflowNodeStatusEnum.WAITING);
    StartEndNodeConfigurationModel config = new StartEndNodeConfigurationModel();
    config.setName(name);
    config.setType(WorkflowNodeTypeEnum.START);
    node.setConfiguration(config);
    return node;
  }

  private WorkflowNodeModel createEndNode(String id, String name) {
    WorkflowNodeModel node = new WorkflowNodeModel();
    node.setId(id);
    node.setType(WorkflowNodeTypeEnum.END);
    node.setPosition(new WorkflowNodePositionModel());
    node.setStatus(WorkflowNodeStatusEnum.WAITING);
    StartEndNodeConfigurationModel config = new StartEndNodeConfigurationModel();
    config.setName(name);
    config.setType(WorkflowNodeTypeEnum.END);
    node.setConfiguration(config);
    return node;
  }

  private WorkflowNodeModel createEmailNode(String id, String name, List<String> templateIds) {
    WorkflowNodeModel node = new WorkflowNodeModel();
    node.setId(id);
    node.setType(WorkflowNodeTypeEnum.EMAIL);
    node.setPosition(new WorkflowNodePositionModel());
    node.setStatus(WorkflowNodeStatusEnum.WAITING);
    EmailNodeConfigurationModel config = new EmailNodeConfigurationModel();
    config.setName(name);
    config.setType(WorkflowNodeTypeEnum.EMAIL);
    config.setEmailTemplateIds(templateIds != null ? templateIds : new ArrayList<>());
    node.setConfiguration(config);
    return node;
  }

  private WorkflowNodeModel createExecutionNode(String id, String name, List<String> executionIds) {
    WorkflowNodeModel node = new WorkflowNodeModel();
    node.setId(id);
    node.setType(WorkflowNodeTypeEnum.EXECUTION);
    node.setPosition(new WorkflowNodePositionModel());
    node.setStatus(WorkflowNodeStatusEnum.WAITING);
    ExecutionNodeConfigurationModel config = new ExecutionNodeConfigurationModel();
    config.setName(name);
    config.setType(WorkflowNodeTypeEnum.EXECUTION);
    config.setExecutionIds(executionIds != null ? executionIds : new ArrayList<>());
    node.setConfiguration(config);
    return node;
  }

  private WorkflowNodeModel createTeamsNode(String id, String name, List<String> contacts, String message) {
    WorkflowNodeModel node = new WorkflowNodeModel();
    node.setId(id);
    node.setType(WorkflowNodeTypeEnum.TEAMS);
    node.setPosition(new WorkflowNodePositionModel());
    node.setStatus(WorkflowNodeStatusEnum.WAITING);
    TeamsNodeConfigurationModel config = new TeamsNodeConfigurationModel();
    config.setName(name);
    config.setType(WorkflowNodeTypeEnum.TEAMS);
    config.setContacts(contacts != null ? contacts : new ArrayList<>());
    config.setMessage(message);
    node.setConfiguration(config);
    return node;
  }

  // Form Layout Validation Tests

  @Test
  void createOrUpdate_success_withValidFormLayout() throws BusinessException {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode
    request.setWorkflow(createValidWorkflowWithEdges()); // Use valid workflow

    // Create valid form layout
    List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();
    TextInputElementModel textElement = new TextInputElementModel();
    textElement.setId("text-1");
    textElement.setType(FormBuilderElementTypeEnum.TEXT);
    textElement.setLabel("Valid Label");
    formLayout.add(textElement);
    request.setFormLayout(formLayout);

    WorkflowTemplateEntity savedEntity = createTestWorkflowTemplateEntity();

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(workflowTemplateRepository.save(any(WorkflowTemplateEntity.class))).thenReturn(savedEntity);
    when(emailTemplateService.findEmailTemplateById(anyLong())).thenReturn(new vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailTemplateModel());

    // Mock workflow creation to return a workflow with proper ID
    WorkflowEntity mockWorkflow = new WorkflowEntity();
    mockWorkflow.setId(testId);
    when(workflowService.create(any(WorkflowModel.class))).thenReturn(mockWorkflow);

    // Act
    WorkflowTemplateEntity result = workflowTemplateService.createOrUpdate(request);

    // Assert
    assertNotNull(result);
    verify(workflowTemplateRepository, times(1)).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenFormElementMissingLabel() throws BusinessException {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode
    request.setWorkflow(createValidWorkflowWithEdges()); // Use valid workflow

    // Create form layout with missing label
    List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();
    TextInputElementModel textElement = new TextInputElementModel();
    textElement.setId("text-1");
    textElement.setType(FormBuilderElementTypeEnum.TEXT);
    textElement.setLabel(null); // Missing label
    formLayout.add(textElement);
    request.setFormLayout(formLayout);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(emailTemplateService.findEmailTemplateById(anyLong())).thenReturn(new vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailTemplateModel());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    assertEquals(ErrorCode.WORKFLOW_TEMPLATE_FORM_ELEMENT_MISSING_LABEL.getCode(), exception.getCode());
    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenFormElementEmptyLabel() throws BusinessException {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode
    request.setWorkflow(createValidWorkflowWithEdges()); // Use valid workflow

    // Create form layout with empty label
    List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();
    TextInputElementModel textElement = new TextInputElementModel();
    textElement.setId("text-1");
    textElement.setType(FormBuilderElementTypeEnum.TEXT);
    textElement.setLabel("   "); // Empty label (whitespace only)
    formLayout.add(textElement);
    request.setFormLayout(formLayout);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(emailTemplateService.findEmailTemplateById(anyLong())).thenReturn(new vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailTemplateModel());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    assertEquals(ErrorCode.WORKFLOW_TEMPLATE_FORM_ELEMENT_MISSING_LABEL.getCode(), exception.getCode());
    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenFormElementLabelTooLong() throws BusinessException {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode
    request.setWorkflow(createValidWorkflowWithEdges()); // Use valid workflow

    // Create form layout with label exceeding 50 characters
    List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();
    TextInputElementModel textElement = new TextInputElementModel();
    textElement.setId("text-1");
    textElement.setType(FormBuilderElementTypeEnum.TEXT);
    textElement.setLabel("This is a very long label that exceeds the maximum allowed length of 50 characters This is a very long label that exceeds the maximum allowed length of 50 characters This is a very long label that exceeds the maximum allowed length of 50 characters");
    formLayout.add(textElement);
    request.setFormLayout(formLayout);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(emailTemplateService.findEmailTemplateById(anyLong())).thenReturn(new vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailTemplateModel());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    assertEquals(ErrorCode.WORKFLOW_TEMPLATE_FORM_ELEMENT_LABEL_TOO_LONG.getCode(), exception.getCode());
    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_success_withValidSectionFormLayout() throws BusinessException {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode
    request.setWorkflow(createValidWorkflowWithEdges()); // Use valid workflow

    // Create valid section form layout
    List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();

    SectionElementModel section = new SectionElementModel();
    section.setId("section-1");
    section.setType(FormBuilderElementTypeEnum.SECTION);
    section.setLabel("Valid Section Label");

    // Create row with nested elements
    RowElement row = new RowElement();
    row.setId("row-1");

    List<BaseFormBuilderInputElementModel> rowElements = new ArrayList<>();
    TextInputElementModel nestedTextElement = new TextInputElementModel();
    nestedTextElement.setId("nested-text-1");
    nestedTextElement.setType(FormBuilderElementTypeEnum.TEXT);
    nestedTextElement.setLabel("Valid Nested Label");
    rowElements.add(nestedTextElement);

    row.setElements(rowElements);
    section.setRows(List.of(row));
    formLayout.add(section);
    request.setFormLayout(formLayout);

    WorkflowTemplateEntity savedEntity = createTestWorkflowTemplateEntity();

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(workflowTemplateRepository.save(any(WorkflowTemplateEntity.class))).thenReturn(savedEntity);
    when(emailTemplateService.findEmailTemplateById(anyLong())).thenReturn(new vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailTemplateModel());

    // Mock workflow creation to return a workflow with proper ID
    WorkflowEntity mockWorkflow = new WorkflowEntity();
    mockWorkflow.setId(testId);
    when(workflowService.create(any(WorkflowModel.class))).thenReturn(mockWorkflow);

    // Act
    WorkflowTemplateEntity result = workflowTemplateService.createOrUpdate(request);

    // Assert
    assertNotNull(result);
    verify(workflowTemplateRepository, times(1)).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenNestedElementMissingLabel() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create section with nested element missing label
    List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();

    SectionElementModel section = new SectionElementModel();
    section.setId("section-1");
    section.setType(FormBuilderElementTypeEnum.SECTION);
    section.setLabel("Valid Section Label");

    RowElement row = new RowElement();
    row.setId("row-1");

    List<BaseFormBuilderInputElementModel> rowElements = new ArrayList<>();
    TextInputElementModel nestedTextElement = new TextInputElementModel();
    nestedTextElement.setId("nested-text-1");
    nestedTextElement.setType(FormBuilderElementTypeEnum.TEXT);
    nestedTextElement.setLabel(""); // Empty label
    rowElements.add(nestedTextElement);

    row.setElements(rowElements);
    section.setRows(List.of(row));
    formLayout.add(section);
    request.setFormLayout(formLayout);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_success_withEmptyFormLayout() throws BusinessException {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode
    request.setFormLayout(new ArrayList<>()); // Empty form layout should be allowed
    request.setWorkflow(createValidWorkflowWithEdges());

    WorkflowTemplateEntity savedEntity = createTestWorkflowTemplateEntity();

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(workflowTemplateRepository.save(any(WorkflowTemplateEntity.class))).thenReturn(savedEntity);

    // Mock workflow creation to return a workflow with proper ID
    WorkflowEntity mockWorkflow = new WorkflowEntity();
    mockWorkflow.setId(testId);
    when(workflowService.create(any(WorkflowModel.class))).thenReturn(mockWorkflow);

    // Act
    WorkflowTemplateEntity result = workflowTemplateService.createOrUpdate(request);

    // Assert
    assertNotNull(result);
    verify(workflowTemplateRepository, times(1)).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_success_withNullFormLayout() throws BusinessException {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode
    request.setFormLayout(null);// Null form layout should be allowed
    request.setWorkflow(createValidWorkflowWithEdges());

    WorkflowTemplateEntity savedEntity = createTestWorkflowTemplateEntity();

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(workflowTemplateRepository.save(any(WorkflowTemplateEntity.class))).thenReturn(savedEntity);

    // Mock workflow creation to return a workflow with proper ID
    WorkflowEntity mockWorkflow = new WorkflowEntity();
    mockWorkflow.setId(testId);
    when(workflowService.create(any(WorkflowModel.class))).thenReturn(mockWorkflow);

    // Act
    WorkflowTemplateEntity result = workflowTemplateService.createOrUpdate(request);

    // Assert
    assertNotNull(result);
    verify(workflowTemplateRepository, times(1)).save(any(WorkflowTemplateEntity.class));
  }

  // Placeholder Validation Tests

  @Test
  void createOrUpdate_failed_whenPlaceholderTooLong() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create form layout with placeholder exceeding 100 characters
    List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();
    TextInputElementModel textElement = new TextInputElementModel();
    textElement.setId("text-1");
    textElement.setType(FormBuilderElementTypeEnum.TEXT);
    textElement.setLabel("Valid Label");
    textElement.setPlaceholder("This is a very long placeholder that exceeds the maximum allowed length of 100 characters for form element placeholders");
    formLayout.add(textElement);
    request.setFormLayout(formLayout);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_success_withValidPlaceholder() throws BusinessException {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null);
    request.setWorkflow(createValidWorkflowWithEdges());

    // Create form layout with valid placeholder
    List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();
    NumberInputElementModel numberElement = new NumberInputElementModel();
    numberElement.setId("number-1");
    numberElement.setType(FormBuilderElementTypeEnum.NUMBER);
    numberElement.setLabel("Valid Label");
    numberElement.setPlaceholder("Enter a number"); // Valid placeholder
    formLayout.add(numberElement);
    request.setFormLayout(formLayout);

    WorkflowTemplateEntity savedEntity = createTestWorkflowTemplateEntity();

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(workflowTemplateRepository.save(any(WorkflowTemplateEntity.class))).thenReturn(savedEntity);

    // Mock workflow creation to return a workflow with proper ID
    WorkflowEntity mockWorkflow = new WorkflowEntity();
    mockWorkflow.setId(testId);
    when(workflowService.create(any(WorkflowModel.class))).thenReturn(mockWorkflow);

    // Act
    WorkflowTemplateEntity result = workflowTemplateService.createOrUpdate(request);

    // Assert
    assertNotNull(result);
    verify(workflowTemplateRepository, times(1)).save(any(WorkflowTemplateEntity.class));
  }

  // Options Validation Tests

  @Test
  void createOrUpdate_failed_whenCheckboxMissingOptions() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create checkbox element with no options
    List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();
    CheckboxElementModel checkboxElement = new CheckboxElementModel();
    checkboxElement.setId("checkbox-1");
    checkboxElement.setType(FormBuilderElementTypeEnum.CHECKBOX);
    checkboxElement.setLabel("Valid Label");
    checkboxElement.setOptions(new ArrayList<>()); // Empty options
    formLayout.add(checkboxElement);
    request.setFormLayout(formLayout);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenOptionMissingValue() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create radio element with option missing value
    List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();
    RadioElementModel radioElement = new RadioElementModel();
    radioElement.setId("radio-1");
    radioElement.setType(FormBuilderElementTypeEnum.RADIO);
    radioElement.setLabel("Valid Label");

    List<OptionModel> options = new ArrayList<>();
    OptionModel option1 = new OptionModel();
    option1.setValue(""); // Empty value
    option1.setLabel("Option 1");
    options.add(option1);
    radioElement.setOptions(options);

    formLayout.add(radioElement);
    request.setFormLayout(formLayout);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenOptionMissingLabel() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create select element with option missing label
    List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();
    SelectElementModel selectElement = new SelectElementModel();
    selectElement.setId("select-1");
    selectElement.setType(FormBuilderElementTypeEnum.SELECT);
    selectElement.setLabel("Valid Label");

    List<OptionModel> options = new ArrayList<>();
    OptionModel option1 = new OptionModel();
    option1.setValue("value1");
    option1.setLabel(null); // Missing label
    options.add(option1);
    selectElement.setOptions(options);

    formLayout.add(selectElement);
    request.setFormLayout(formLayout);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenDuplicateOptionValues() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create checkbox element with duplicate option values
    List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();
    CheckboxElementModel checkboxElement = new CheckboxElementModel();
    checkboxElement.setId("checkbox-1");
    checkboxElement.setType(FormBuilderElementTypeEnum.CHECKBOX);
    checkboxElement.setLabel("Valid Label");

    List<OptionModel> options = new ArrayList<>();
    OptionModel option1 = new OptionModel();
    option1.setValue("duplicate");
    option1.setLabel("Option 1");
    options.add(option1);

    OptionModel option2 = new OptionModel();
    option2.setValue("duplicate"); // Duplicate value
    option2.setLabel("Option 2");
    options.add(option2);

    checkboxElement.setOptions(options);
    formLayout.add(checkboxElement);
    request.setFormLayout(formLayout);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenDuplicateOptionLabels() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create radio element with duplicate option labels
    List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();
    RadioElementModel radioElement = new RadioElementModel();
    radioElement.setId("radio-1");
    radioElement.setType(FormBuilderElementTypeEnum.RADIO);
    radioElement.setLabel("Valid Label");

    List<OptionModel> options = new ArrayList<>();
    OptionModel option1 = new OptionModel();
    option1.setValue("value1");
    option1.setLabel("Duplicate Label");
    options.add(option1);

    OptionModel option2 = new OptionModel();
    option2.setValue("value2");
    option2.setLabel("Duplicate Label"); // Duplicate label
    options.add(option2);

    radioElement.setOptions(options);
    formLayout.add(radioElement);
    request.setFormLayout(formLayout);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_success_withValidOptions() throws BusinessException {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode
    request.setWorkflow(createValidWorkflowWithEdges());

    // Create select element with valid options
    List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();
    SelectElementModel selectElement = new SelectElementModel();
    selectElement.setId("select-1");
    selectElement.setType(FormBuilderElementTypeEnum.SELECT);
    selectElement.setLabel("Valid Label");

    List<OptionModel> options = new ArrayList<>();
    OptionModel option1 = new OptionModel();
    option1.setValue("value1");
    option1.setLabel("Option 1");
    options.add(option1);

    OptionModel option2 = new OptionModel();
    option2.setValue("value2");
    option2.setLabel("Option 2");
    options.add(option2);

    selectElement.setOptions(options);
    formLayout.add(selectElement);
    request.setFormLayout(formLayout);

    WorkflowTemplateEntity savedEntity = createTestWorkflowTemplateEntity();

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(workflowTemplateRepository.save(any(WorkflowTemplateEntity.class))).thenReturn(savedEntity);

    // Mock workflow creation to return a workflow with proper ID
    WorkflowEntity mockWorkflow = new WorkflowEntity();
    mockWorkflow.setId(testId);
    when(workflowService.create(any(WorkflowModel.class))).thenReturn(mockWorkflow);

    // Act
    WorkflowTemplateEntity result = workflowTemplateService.createOrUpdate(request);

    // Assert
    assertNotNull(result);
    verify(workflowTemplateRepository, times(1)).save(any(WorkflowTemplateEntity.class));
  }

  // Workflow Validation Tests

  @Test
  void createOrUpdate_failed_whenWorkflowEmpty() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode
    request.setWorkflow(null); // Null workflow

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenNoStartNode() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create workflow without START node
    WorkflowModel workflow = new WorkflowModel();
    List<WorkflowNodeModel> nodes = new ArrayList<>();

    WorkflowNodeModel endNode = new WorkflowNodeModel();
    endNode.setId("end-1");
    endNode.setType(WorkflowNodeTypeEnum.END);
    endNode.setPosition(new WorkflowNodePositionModel());
    endNode.setStatus(WorkflowNodeStatusEnum.WAITING);
    StartEndNodeConfigurationModel endConfig = new StartEndNodeConfigurationModel();
    endConfig.setName("End Node");
    endConfig.setType(WorkflowNodeTypeEnum.END);
    endNode.setConfiguration(endConfig);
    nodes.add(endNode);

    workflow.setNodes(nodes);
    workflow.setEdges(new ArrayList<>());
    request.setWorkflow(workflow);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenNoEndNode() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create workflow without END node
    WorkflowModel workflow = new WorkflowModel();
    List<WorkflowNodeModel> nodes = new ArrayList<>();

    WorkflowNodeModel startNode = new WorkflowNodeModel();
    startNode.setId("start-1");
    startNode.setType(WorkflowNodeTypeEnum.START);
    startNode.setPosition(new WorkflowNodePositionModel());
    startNode.setStatus(WorkflowNodeStatusEnum.WAITING);
    StartEndNodeConfigurationModel startConfig = new StartEndNodeConfigurationModel();
    startConfig.setName("Start Node");
    startConfig.setType(WorkflowNodeTypeEnum.START);
    startNode.setConfiguration(startConfig);
    nodes.add(startNode);

    workflow.setNodes(nodes);
    workflow.setEdges(new ArrayList<>());
    request.setWorkflow(workflow);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenStartNodeNotConnected() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create workflow with START node not connected to anything
    WorkflowModel workflow = new WorkflowModel();
    List<WorkflowNodeModel> nodes = new ArrayList<>();

    WorkflowNodeModel startNode = new WorkflowNodeModel();
    startNode.setId("start-1");
    startNode.setType(WorkflowNodeTypeEnum.START);
    startNode.setPosition(new WorkflowNodePositionModel());
    startNode.setStatus(WorkflowNodeStatusEnum.WAITING);
    StartEndNodeConfigurationModel startConfig = new StartEndNodeConfigurationModel();
    startConfig.setName("Start Node");
    startConfig.setType(WorkflowNodeTypeEnum.START);
    startNode.setConfiguration(startConfig);
    nodes.add(startNode);

    WorkflowNodeModel endNode = new WorkflowNodeModel();
    endNode.setId("end-1");
    endNode.setType(WorkflowNodeTypeEnum.END);
    endNode.setPosition(new WorkflowNodePositionModel());
    endNode.setStatus(WorkflowNodeStatusEnum.WAITING);
    StartEndNodeConfigurationModel endConfig = new StartEndNodeConfigurationModel();
    endConfig.setName("End Node");
    endConfig.setType(WorkflowNodeTypeEnum.END);
    endNode.setConfiguration(endConfig);
    nodes.add(endNode);

    workflow.setNodes(nodes);
    workflow.setEdges(new ArrayList<>());
    request.setWorkflow(workflow);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenEndNodeNotConnected() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create workflow with END node not connected from anything
    WorkflowModel workflow = new WorkflowModel();
    List<WorkflowNodeModel> nodes = new ArrayList<>();

    WorkflowNodeModel startNode = new WorkflowNodeModel();
    startNode.setId("start-1");
    startNode.setType(WorkflowNodeTypeEnum.START);
    startNode.setPosition(new WorkflowNodePositionModel());
    startNode.setStatus(WorkflowNodeStatusEnum.WAITING);
    StartEndNodeConfigurationModel startConfig = new StartEndNodeConfigurationModel();
    startConfig.setName("Start Node");
    startConfig.setType(WorkflowNodeTypeEnum.START);
    startNode.setConfiguration(startConfig);
    nodes.add(startNode);

    WorkflowNodeModel emailNode = new WorkflowNodeModel();
    emailNode.setId("email-1");
    emailNode.setType(WorkflowNodeTypeEnum.EMAIL);
    emailNode.setPosition(new WorkflowNodePositionModel());
    emailNode.setStatus(WorkflowNodeStatusEnum.WAITING);
    EmailNodeConfigurationModel emailConfig = new EmailNodeConfigurationModel();
    emailConfig.setName("Email Node");
    emailConfig.setType(WorkflowNodeTypeEnum.EMAIL);
    emailConfig.setEmailTemplateIds(List.of("template-1"));
    emailNode.setConfiguration(emailConfig);
    nodes.add(emailNode);

    WorkflowNodeModel endNode = new WorkflowNodeModel();
    endNode.setId("end-1");
    endNode.setType(WorkflowNodeTypeEnum.END);
    endNode.setPosition(new WorkflowNodePositionModel());
    endNode.setStatus(WorkflowNodeStatusEnum.WAITING);
    StartEndNodeConfigurationModel endConfig = new StartEndNodeConfigurationModel();
    endConfig.setName("End Node");
    endConfig.setType(WorkflowNodeTypeEnum.END);
    endNode.setConfiguration(endConfig);
    nodes.add(endNode);

    workflow.setNodes(nodes);
    workflow.setEdges(new ArrayList<>());
    request.setWorkflow(workflow);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenCycleDetected() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create workflow with cycle: start -> email1 -> email2 -> email1 (cycle)
    WorkflowModel workflow = new WorkflowModel();
    List<WorkflowNodeModel> nodes = new ArrayList<>();

    WorkflowNodeModel startNode = createStartNode("start-1", "Start Node");
    nodes.add(startNode);

    WorkflowNodeModel emailNode1 = createEmailNode("email-1", "Email Node 1", List.of("template-1"));
    nodes.add(emailNode1);

    WorkflowNodeModel emailNode2 = createEmailNode("email-2", "Email Node 2", List.of("template-2"));
    nodes.add(emailNode2);

    workflow.setNodes(nodes);
    workflow.setEdges(new ArrayList<>());
    request.setWorkflow(workflow);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenNodeMissingLabel() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create workflow with node missing label
    WorkflowModel workflow = new WorkflowModel();
    List<WorkflowNodeModel> nodes = new ArrayList<>();

    WorkflowNodeModel startNode = createStartNode("start-1", "Start Node");
    nodes.add(startNode);

    WorkflowNodeModel emailNode = createEmailNode("email-1", null, List.of("template-1")); // Missing name/label
    nodes.add(emailNode);

    WorkflowNodeModel endNode = createEndNode("end-1", "End Node");
    nodes.add(endNode);

    workflow.setNodes(nodes);
    workflow.setEdges(new ArrayList<>());
    request.setWorkflow(workflow);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenEmailNodeMissingTemplateId() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create workflow with email node missing template ID
    WorkflowModel workflow = new WorkflowModel();
    List<WorkflowNodeModel> nodes = new ArrayList<>();

    WorkflowNodeModel startNode = createStartNode("start-1", "Start Node");
    nodes.add(startNode);

    WorkflowNodeModel emailNode = createEmailNode("email-1", "Email Node", new ArrayList<>()); // Empty template IDs
    nodes.add(emailNode);

    WorkflowNodeModel endNode = createEndNode("end-1", "End Node");
    nodes.add(endNode);

    workflow.setNodes(nodes);
    workflow.setEdges(new ArrayList<>());
    request.setWorkflow(workflow);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenExecutionNodeMissingExecutionId() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create workflow with execution node missing execution ID
    WorkflowModel workflow = new WorkflowModel();
    List<WorkflowNodeModel> nodes = new ArrayList<>();

    WorkflowNodeModel startNode = createStartNode("start-1", "Start Node");
    nodes.add(startNode);

    WorkflowNodeModel executionNode = createExecutionNode("execution-1", "Execution Node", new ArrayList<>()); // Empty execution IDs
    nodes.add(executionNode);

    WorkflowNodeModel endNode = createEndNode("end-1", "End Node");
    nodes.add(endNode);

    workflow.setNodes(nodes);
    workflow.setEdges(new ArrayList<>());
    request.setWorkflow(workflow);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenTeamsNodeMissingContacts() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create workflow with TEAMS node missing contacts
    WorkflowModel workflow = new WorkflowModel();
    List<WorkflowNodeModel> nodes = new ArrayList<>();

    WorkflowNodeModel startNode = createStartNode("start-1", "Start Node");
    nodes.add(startNode);

    WorkflowNodeModel teamsNode = createTeamsNode("teams-1", "Teams Node", new ArrayList<>(), "Test message"); // Empty contacts
    nodes.add(teamsNode);

    WorkflowNodeModel endNode = createEndNode("end-1", "End Node");
    nodes.add(endNode);

    workflow.setNodes(nodes);
    workflow.setEdges(new ArrayList<>());
    request.setWorkflow(workflow);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenTeamsNodeMessageTooLong() {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode

    // Create workflow with TEAMS node message too long
    WorkflowModel workflow = new WorkflowModel();
    List<WorkflowNodeModel> nodes = new ArrayList<>();

    WorkflowNodeModel startNode = createStartNode("start-1", "Start Node");
    nodes.add(startNode);

    // Create message longer than 300 characters
    StringBuilder longMessage = new StringBuilder();
    for (int i = 0; i < 31; i++) {
      longMessage.append("0123456789"); // 10 chars each, 31 times = 310 chars
    }

    WorkflowNodeModel teamsNode = createTeamsNode("teams-1", "Teams Node", List.of("contact1"), longMessage.toString());
    nodes.add(teamsNode);

    WorkflowNodeModel endNode = createEndNode("end-1", "End Node");
    nodes.add(endNode);

    workflow.setNodes(nodes);
    workflow.setEdges(new ArrayList<>());
    request.setWorkflow(workflow);

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateService.createOrUpdate(request);
    });

    verify(workflowTemplateRepository, never()).save(any(WorkflowTemplateEntity.class));
  }

  @Test
  void createOrUpdate_success_withValidWorkflow() throws BusinessException {
    // Arrange
    WorkflowTemplateRequest request = createTestWorkflowTemplateRequest();
    request.setId(null); // Create mode
    request.setWorkflow(createValidWorkflowWithEdges());

    WorkflowTemplateEntity savedEntity = createTestWorkflowTemplateEntity();

    when(workflowTemplateRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(workflowTemplateRepository.save(any(WorkflowTemplateEntity.class))).thenReturn(savedEntity);
    when(emailTemplateService.findEmailTemplateById(anyLong())).thenReturn(new EmailTemplateModel());

    // Mock workflow creation to return a workflow with proper ID
    WorkflowEntity mockWorkflow = new WorkflowEntity();
    mockWorkflow.setId(testId);
    when(workflowService.create(any(WorkflowModel.class))).thenReturn(mockWorkflow);

    // Act
    WorkflowTemplateEntity result = workflowTemplateService.createOrUpdate(request);

    // Assert
    assertNotNull(result);
    verify(workflowTemplateRepository, times(1)).save(any(WorkflowTemplateEntity.class));
  }
}
