package vn.com.mbbank.kanban.mbmonitor.server.configs;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.ZoneId;
import java.util.TimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;

/**
 * Configuration class for JacksonConfiguration.
 */
@Configuration
public class JacksonConfiguration {

  /**
   * Creates a JacksonConfiguration bean to default.
   * Set timezone to DEFAULT_TIME_ZONE when use @JsonFormat.
   *
   * @param objectMapper a mapper
   */
  @Autowired
  public JacksonConfiguration(ObjectMapper objectMapper) {
    objectMapper.setTimeZone(TimeZone.getTimeZone(ZoneId.of(CommonConstants.DEFAULT_TIME_ZONE)));
  }
}