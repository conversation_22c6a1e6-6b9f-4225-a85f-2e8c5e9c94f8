package vn.com.mbbank.kanban.mbmonitor.server.configs.circuitbreaker;


import java.util.HashMap;

/**
 * This class manages a collection of CircuitBreakerState objects using a HashMap.
 * It provides methods to access and modify the collection.
 */
public class CircuitBreakerStates {

  /**
   * HashMap to store CircuitBreakerState objects, keyed by their name.
   */
  private HashMap<String, CircuitBreakerState> hashCircuitBreaker = new HashMap<>();

  /**
   * Constructs an empty CircuitBreakerStates object.
   */
  public CircuitBreakerStates() {
  }

  /**
   * Retrieves the HashMap containing CircuitBreakerState objects.
   *
   * @return The HashMap containing CircuitBreakerState objects.
   */
  public HashMap<String, CircuitBreakerState> getHashCircuitBreaker() {
    return this.hashCircuitBreaker;
  }

  /**
   * Sets the HashMap containing CircuitBreakerState objects.
   *
   * @param hashCircuitBreaker The HashMap containing CircuitBreakerState objects to set.
   */
  public void setHashCircuitBreaker(HashMap<String, CircuitBreakerState> hashCircuitBreaker) {
    this.hashCircuitBreaker = hashCircuitBreaker;
  }

  /**
   * Adds a CircuitBreakerState object to the collection.
   *
   * @param s The CircuitBreakerState object to add.
   */
  public void addCircuitBreakerState(CircuitBreakerState s) {
    this.hashCircuitBreaker.put(s.getName(), s);
  }
}
