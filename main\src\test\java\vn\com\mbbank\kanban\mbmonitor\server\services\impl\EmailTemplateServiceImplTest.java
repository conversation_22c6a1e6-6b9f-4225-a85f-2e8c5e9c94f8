package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang.reflect.FieldUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.web.multipart.MultipartFile;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailTemplateEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailTemplateReceiverEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.server.annotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailTemplateModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.FileStorageModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailTemplatePaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailTemplateRequest;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.EmailTemplateRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailTemplateDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailTemplateReceiverService;
import vn.com.mbbank.kanban.mbmonitor.server.services.FileStorageService;

@ExtendWith(MockitoExtension.class)
public class EmailTemplateServiceImplTest {
  EmailTemplateServiceImpl service;

  @Mock
  EmailTemplateRepository repository;

  @Mock
  EmailTemplateReceiverService receiverService;

  @Mock
  FileStorageService fileStorageService;
  @Mock
  EmailTemplateDependencyService emailTemplateDependencyService;
  @Mock
  SysLogKafkaProducerService sysLogKafkaProducerService;

  @BeforeEach
  void setup() throws IllegalAccessException {
    service = new EmailTemplateServiceImpl(repository, receiverService, sysLogKafkaProducerService);

    // inject mock vào field private bằng reflection
    FieldUtils.writeField(service, "fileStorageService", fileStorageService, true);
  }

  @Test
  void getRepository_success() {
    JpaCommonRepository<EmailTemplateEntity, Long> result = service.getRepository();
    assertEquals(repository, result);
  }


  @Test
  void findAll() {
    when(repository.findAll(any(EmailTemplatePaginationRequest.class))).thenReturn(Page.empty());
    when(receiverService.findAllByEmailTemplateIdIn(any())).thenReturn(List.of());
    var res = service.findAll(new EmailTemplatePaginationRequest());
    verify(repository, times(1)).findAll(any(EmailTemplatePaginationRequest.class));
    verify(receiverService, times(1)).findAllByEmailTemplateIdIn(any());
    assertEquals(res.getContent().size(), 0);
    assertEquals(res.getTotalElements(), 0);
  }

  @TestForDev
  void download_error() {
    when(fileStorageService.findById(any())).thenReturn(null);
    assertThrows(BusinessException.class, () -> service.download(1L));
  }

  @TestForDev
  void download_success() throws BusinessException {
    var file = new FileStorageEntity();
    file.setId(1L);
    file.setPath("path");
    when(fileStorageService.findById(any())).thenReturn(file);
    when(fileStorageService.readFile(any())).thenReturn(null);
    var res =  service.download(1L);
    assertNull(res);
  }
  @Test
  void findAll_MapsEntitiesCorrectly() {
    EmailTemplatePaginationRequest request = new EmailTemplatePaginationRequest();
    EmailTemplateEntity emailTemplateEntity = new EmailTemplateEntity();
    emailTemplateEntity.setId(1L);
    List<EmailTemplateEntity> entitiesList = List.of(emailTemplateEntity);
    Page<EmailTemplateEntity> entitiesPage = new PageImpl<>(entitiesList);
    when(repository.findAll(request)).thenReturn(entitiesPage);
    EmailTemplateReceiverEntity receiverEntity = new EmailTemplateReceiverEntity();
    receiverEntity.setEmailTemplateId(1L);
    when(receiverService.findAllByEmailTemplateIdIn(List.of(1L)))
        .thenReturn(List.of(receiverEntity));
    EmailTemplateModel expectedModel = new EmailTemplateModel();
    expectedModel.setId(1L);
    expectedModel.setTo(List.of());
    expectedModel.setCc(List.of());
    expectedModel.setFileStorages(List.of());
    Page<EmailTemplateModel> result = service.findAll(request);
    assertEquals(1, result.getContent().size());
    assertEquals(expectedModel, result.getContent().get(0));
  }

  @Test
  public void findResponseById_Success() throws BusinessException {
    Long id = 1L;
    EmailTemplateEntity mockEntity = new EmailTemplateEntity();
    mockEntity.setId(id);
    EmailTemplateModel mockResponse = new EmailTemplateModel();
    List<EmailTemplateReceiverEntity> mockReceiver = List.of(new EmailTemplateReceiverEntity());
    mockResponse.setId(id);
    mockResponse.setCc(List.of());
    mockResponse.setTo(List.of());
    mockResponse.setFileStorages(List.of());
    when(repository.findById(id)).thenReturn(Optional.of(mockEntity));
    when(receiverService.findAllByEmailTemplateId(id)).thenReturn(mockReceiver);
    when(fileStorageService.findAllFileNameByDependencyNameAndDependencyId(TableName.EMAIL_TEMPLATE,
        String.valueOf(id))).thenReturn(any());
    EmailTemplateModel result = service.findEmailTemplateById(id);
    assertNotNull(result);
    assertEquals(mockResponse, result);
    verify(repository).findById(id);
    verify(receiverService).findAllByEmailTemplateId(id);
  }


  @Test
  public void findResponseById_haveFileStorage_Success() throws BusinessException {
    Long id = 1L;
    EmailTemplateEntity mockEntity = new EmailTemplateEntity();
    mockEntity.setId(id);
    EmailTemplateModel mockResponse = new EmailTemplateModel();
    List<EmailTemplateReceiverEntity> mockReceiver = List.of(new EmailTemplateReceiverEntity());
    mockResponse.setId(id);
    mockResponse.setCc(List.of());
    mockResponse.setTo(List.of());
    FileStorageModel fileStorageModel = new FileStorageModel();
    fileStorageModel.setSize(0L);
    mockResponse.setFileStorages(List.of(fileStorageModel));
    when(repository.findById(id)).thenReturn(Optional.of(mockEntity));
    when(receiverService.findAllByEmailTemplateId(id)).thenReturn(mockReceiver);
    when(fileStorageService.findAllFileNameByDependencyNameAndDependencyId(TableName.EMAIL_TEMPLATE,
        String.valueOf(id))).thenReturn(List.of(new FileStorageEntity()));
    EmailTemplateModel result = service.findEmailTemplateById(id);
    assertNotNull(result);
    assertEquals(mockResponse, result);
    verify(repository).findById(id);
    verify(receiverService).findAllByEmailTemplateId(id);
  }

  @Test
  public void findResponseById_NotFound() {
    Long id = 1L;
    when(repository.findById(id)).thenReturn(Optional.empty());
    BusinessException thrown = assertThrows(BusinessException.class, () -> service.findEmailTemplateById(id));
    assertEquals(ErrorCode.EMAIL_TEMPLATE_NOT_FOUND
        .getMessage(), thrown.getMessage());
  }


  @Test
  void updateEmailTemplate_ThrowsExceptionWhenNameExistsForDifferentEntity() {
    EmailTemplateRequest request = new EmailTemplateRequest();
    request.setId(1L);
    request.setName("Duplicate");

    EmailTemplateEntity existingEntity = new EmailTemplateEntity();
    existingEntity.setId(2L);
    request.setName("Duplicate ");
    when(repository.findByIdNotAndNameIgnoreCase(any(), any())).thenReturn(Optional.of(existingEntity));
    BusinessException exception =
        assertThrows(BusinessException.class, () -> service.createOrUpdate(request, List.of()));
    assertEquals(ErrorCode.EMAIL_TEMPLATE_NAME_EXIST.getCode(), exception.getCode());
    verify(repository, never()).save(any(EmailTemplateEntity.class));
  }

  @Test
  void createOrUpdate_Create() throws BusinessException, IOException {
    EmailTemplateRequest request = new EmailTemplateRequest();
    FileStorageModel fileStorage = new FileStorageModel();
    fileStorage.setSize(null);
    request.setName("New Template");
    request.setFileStorages(List.of(fileStorage));
    request.setFileStorages(List.of());
    MultipartFile file = mock(MultipartFile.class);
    EmailTemplateEntity templateEntity = new EmailTemplateEntity();
    templateEntity.setName("New Template");
    when(repository.findByNameIgnoreCase(request.getName())).thenReturn(Optional.empty());
    EmailTemplateModel result = service.createOrUpdate(request, Collections.singletonList(file));
    assertNotNull(result);
    verify(repository).findByNameIgnoreCase(request.getName());
    verify(fileStorageService).uploadMultipleFiles(List.of(), Collections.singletonList(file), TableName.EMAIL_TEMPLATE,
        String.valueOf(templateEntity.getId()));
  }

  @Test
  void createOrUpdate_Update() throws BusinessException, IOException {
    EmailTemplateRequest request = new EmailTemplateRequest();
    request.setId(1L);
    request.setName("Updated Template");
    FileStorageModel fileStorage = new FileStorageModel();
    fileStorage.setSize(1L);
    request.setFileStorages(List.of(fileStorage));
    EmailTemplateEntity existingEntity = new EmailTemplateEntity();
    existingEntity.setId(1L);
    existingEntity.setName("Old Template");
    when(repository.findById(request.getId())).thenReturn(Optional.of(existingEntity));
    when(repository.save(existingEntity)).thenReturn(existingEntity);
    EmailTemplateModel result = service.createOrUpdate(request, List.of());
    assertNotNull(result);
    verify(receiverService).deleteAllByEmailTemplateId(request.getId());
    verify(repository).save(existingEntity);
  }

  @Test
  void createOrUpdate_NameExists() {
    EmailTemplateRequest request = new EmailTemplateRequest();
    request.setId(null);
    request.setName("Existing Template");
    EmailTemplateEntity existingEntity = new EmailTemplateEntity();
    existingEntity.setId(1L);
    existingEntity.setName("Existing Template");
    when(repository.findByNameIgnoreCase(request.getName())).thenReturn(
        Optional.of(existingEntity));
    BusinessException exception =
        assertThrows(BusinessException.class, () -> service.createOrUpdate(request, Collections.emptyList()));
    assertEquals(ErrorCode.EMAIL_TEMPLATE_NAME_EXIST.getCode(), exception.getCode());
  }

  @Test
  public void createOrUpdate_exception() {
    var request = new EmailTemplateRequest();
    request.setId(3123213L);
    request.setName("name");
    BusinessException empty =
        assertThrows(BusinessException.class, () -> service.createOrUpdate(request, Collections.emptyList()));
    assertEquals(ErrorCode.EMAIL_TEMPLATE_NOT_FOUND.getMessage(), empty.getMessage());
  }

  @Test
  void deleteById_success() throws BusinessException {
    Long id = 1L;
    when(repository.findById(id)).thenReturn(Optional.of(new EmailTemplateEntity()));
    emailTemplateDependencyService.deleteWithId(id);
  }

  @Test
  void deleteById_failed_caseNotFound() throws BusinessException {
    Long id = 1L;
    when(repository.findById(id)).thenReturn(Optional.empty());
    assertThrows(BusinessException.class, () -> emailTemplateDependencyService.deleteWithId(id));
  }
}
