package vn.com.mbbank.kanban.mbmonitor.server.services.impl;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.kafka.KafkaException;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CollectEmailConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.annotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CollectEmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OperatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.JobKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleCondition;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleConverterUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.CollectEmailConfigModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.CollectEmailConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.enums.CollectEmailAlertContentTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.CollectEmailConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.EmailConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;
import vn.com.mbbank.kanban.test.ApplicationTest;

@ExtendWith(MockitoExtension.class)
class CollectEmailConfigServiceImplTest extends ApplicationTest {

  @Mock
  CollectEmailConfigRepository collectEmailConfigRepository;
  @Mock
  EmailConfigRepository emailConfigRepository;
  @Mock
  JobKafkaProducerService jobKafkaProducerService;
  @Mock
  SysLogKafkaProducerService sysLogKafkaProducerService;
  @Mock
  ServiceService serviceService;
  @Mock
  ApplicationService applicationService;
  @Mock
  AlertPriorityConfigService alertPriorityConfigService;
  @InjectMocks
  CollectEmailConfigServiceImpl collectEmailConfigService;

  @Test
  void getRepository_success() {
    JpaCommonRepository<CollectEmailConfigEntity, Long> result =
        collectEmailConfigService.getRepository();
    assertEquals(collectEmailConfigRepository, result);
  }

  @Test
  void existByName_success() {
    when(collectEmailConfigRepository.existsByNameIgnoreCase(any())).thenReturn(true);
    var res = collectEmailConfigService.existByName("1");
    verify(collectEmailConfigRepository, times(1)).existsByNameIgnoreCase(any());
    assertEquals(true, res);
  }

  @Test
  void existByIdNotAndName_success() {
    when(collectEmailConfigRepository.existsByIdNotAndNameIgnoreCase(any(), any())).thenReturn(
        true);
    var res = collectEmailConfigService.existByIdNotAndName(1L, "1");
    verify(collectEmailConfigRepository, times(1)).existsByIdNotAndNameIgnoreCase(any(), any());
    assertEquals(true, res);
  }

  @Test
  void findAll_success() {
    when(collectEmailConfigRepository.findAll(any(PaginationRequestDTO.class))).thenReturn(
        new PageImpl<>(List.of(CollectEmailConfigModel.builder().build())));
    var res = collectEmailConfigService.findAll(new PaginationRequestDTO());
    assertNotNull(res);
  }

  @Test
  void findCollectEmailConfigById_error_json() {
    when(collectEmailConfigRepository.findCollectEmailConfigById(any())).thenReturn(
        new CollectEmailConfigModel());
    assertThrows(Exception.class, () -> {
      collectEmailConfigService.findCollectEmailConfigById(1L);
    });
  }

  @Test
  void findCollectEmailConfigById_success() throws Exception {
    when(collectEmailConfigRepository.findCollectEmailConfigById(any())).thenReturn(
        new CollectEmailConfigModel());
    try (MockedStatic<RuleConverterUtils> mockedSession = mockStatic(RuleConverterUtils.class)) {
      mockedSession.when(() -> RuleConverterUtils.convertStringToRuleGroupType(any()))
          .thenReturn(new RuleGroupType());
      var res = collectEmailConfigService.findCollectEmailConfigById(1L);
      assertNotNull(res);
    }
  }

  @Test
  void createOrUpdate_success() throws BusinessException {
    CollectEmailConfigRequest collectEmailConfigRequest = new CollectEmailConfigRequest();
    collectEmailConfigRequest.setName("adfa");
    collectEmailConfigRequest.setServiceId("1");
    collectEmailConfigRequest.setApplicationId("1");
    collectEmailConfigRequest.setPriorityConfigId(1L);
    collectEmailConfigRequest.setIntervalTime(10L);
    collectEmailConfigRequest.setAbsenceInterval(100L);
    collectEmailConfigRequest.setAlertRepeatInterval(100L);
    collectEmailConfigRequest.setContentValue("213");
    collectEmailConfigRequest.setType(CollectEmailConfigTypeEnum.ABSENCE_ALERT);
    var rule = new RuleGroupType();
    var ruleElement = new RuleCondition<>();
    rule.setCombinator(ConditionCombinatorEnum.AND);
    rule.setRules(List.of(ruleElement));
    ruleElement.setField("102");
    ruleElement.setValue("a");
    ruleElement.setOperator(OperatorEnum.IS);
    when(emailConfigRepository.findById(any())).thenReturn(Optional.of(new EmailConfigEntity()));
    when(collectEmailConfigRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
    when(emailConfigRepository.save(any())).thenReturn(new EmailConfigEntity());
    var config = new CollectEmailConfigEntity();
    config.setRuleGroup(rule);
    when(collectEmailConfigRepository.save(any())).thenReturn(config);
    var res = collectEmailConfigService.createOrUpdate(collectEmailConfigRequest);
    assertNotNull(res);
  }

  @Test
  void createOrUpdate_caseCannotSendKafka() throws BusinessException {
    when(emailConfigRepository.findById(any())).thenReturn(Optional.of(new EmailConfigEntity()));
    CollectEmailConfigRequest collectEmailConfigRequest = new CollectEmailConfigRequest();
    collectEmailConfigRequest.setName("adfa");
    collectEmailConfigRequest.setServiceId("1");
    collectEmailConfigRequest.setApplicationId("1");
    collectEmailConfigRequest.setPriorityConfigId(1L);
    collectEmailConfigRequest.setContentType(CollectEmailAlertContentTypeEnum.SAME_BODY);
    when(collectEmailConfigRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
    collectEmailConfigRequest.setName("");
    Mockito.doThrow(new KafkaException("Failed send message")).when(jobKafkaProducerService)
        .notifyJobUpdate(any(), any());
    assertThrows(BusinessException.class, () -> collectEmailConfigService.createOrUpdate(collectEmailConfigRequest));
  }

  @Test
  void updateStatus_success() throws BusinessException {
    when(collectEmailConfigRepository.findById(any())).thenReturn(
        Optional.of(new CollectEmailConfigEntity()));
    when(collectEmailConfigRepository.save(any())).thenReturn(new CollectEmailConfigEntity());
    var res = collectEmailConfigService.updateStatus(1L);
    assertNotNull(res);
  }

  @Test
  void updateStatus_success_active() throws BusinessException {
    var config = new CollectEmailConfigEntity();
    config.setActive(true);
    when(collectEmailConfigRepository.findById(any())).thenReturn(Optional.of(config));
    when(emailConfigRepository.findById(any())).thenReturn(Optional.of(new EmailConfigEntity()));
    when(collectEmailConfigRepository.save(any())).thenReturn(new CollectEmailConfigEntity());
    var res = collectEmailConfigService.updateStatus(1L);
    assertNotNull(res);
  }

  @Test
  void updateStatus_errror_notFoundCollectEmailConfig() {
    when(collectEmailConfigRepository.findById(any())).thenReturn(Optional.empty());
    assertThrows(BusinessException.class, () -> {
      collectEmailConfigService.updateStatus(1L);
    }, ErrorCode.COLLECT_EMAIL_CONFIG_NOT_FOUND.getMessage());
  }

  @Test
  void updateStatus_succeess() throws BusinessException {
    CollectEmailConfigEntity config = new CollectEmailConfigEntity();
    config.setEmailConfigId(2L);
    config.setActive(true);

    when(collectEmailConfigRepository.findById(any())).thenReturn(Optional.of(config));
    when(collectEmailConfigRepository.existsByIdNotAndEmailConfigIdAndActiveIsTrue(any(), any())).thenReturn(true);
    when(collectEmailConfigRepository.save(any())).thenReturn(new CollectEmailConfigEntity());
    assertNotNull(collectEmailConfigService.updateStatus(1L));
  }

  @Test
  void updateStatus_succeess2() throws BusinessException {
    CollectEmailConfigEntity config = new CollectEmailConfigEntity();
    config.setEmailConfigId(2L);
    config.setActive(true);

    when(collectEmailConfigRepository.findById(any())).thenReturn(Optional.of(config));
    when(emailConfigRepository.findById(any())).thenReturn(Optional.of(new EmailConfigEntity()));
    when(collectEmailConfigRepository.existsByIdNotAndEmailConfigIdAndActiveIsTrue(any(), any())).thenReturn(false);
    when(collectEmailConfigRepository.save(any())).thenReturn(new CollectEmailConfigEntity());
    assertNotNull(collectEmailConfigService.updateStatus(1L));
  }

  @Test
  void createOrUpdate_success_idIsZero() throws BusinessException {
    when(emailConfigRepository.findById(any())).thenReturn(Optional.of(new EmailConfigEntity()));
    CollectEmailConfigRequest collectEmailConfigRequest = new CollectEmailConfigRequest();
    collectEmailConfigRequest.setName("");
    collectEmailConfigRequest.setId(0L);
    collectEmailConfigRequest.setServiceId("1");
    collectEmailConfigRequest.setApplicationId("1");
    collectEmailConfigRequest.setPriorityConfigId(1L);
    collectEmailConfigRequest.setContentType(CollectEmailAlertContentTypeEnum.SAME_BODY);
    var rule = new RuleGroupType();
    var ruleElement = new RuleCondition<>();
    rule.setCombinator(ConditionCombinatorEnum.AND);
    rule.setRules(List.of(ruleElement));
    ruleElement.setField("102");
    ruleElement.setValue("a");
    ruleElement.setOperator(OperatorEnum.IS);
    collectEmailConfigRequest.setRuleGroup(rule);
    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
    var entity = new CollectEmailConfigEntity();
    entity.setRuleGroup(rule);
    when(collectEmailConfigRepository.save(any())).thenReturn(entity);
    var res = collectEmailConfigService.createOrUpdate(collectEmailConfigRequest);
    assertNotNull(res);
  }

  @Test
  void createOrUpdate_success_idIsNull() throws BusinessException {
    when(emailConfigRepository.findById(any())).thenReturn(Optional.of(new EmailConfigEntity()));
    CollectEmailConfigRequest collectEmailConfigRequest = new CollectEmailConfigRequest();
    collectEmailConfigRequest.setName("");
    collectEmailConfigRequest.setId(null);
    collectEmailConfigRequest.setServiceId("1");
    collectEmailConfigRequest.setApplicationId("1");
    collectEmailConfigRequest.setPriorityConfigId(1L);
    collectEmailConfigRequest.setContentType(CollectEmailAlertContentTypeEnum.SAME_BODY);
    var rule = new RuleGroupType();
    var ruleElement = new RuleCondition<>();
    rule.setCombinator(ConditionCombinatorEnum.AND);
    rule.setRules(List.of(ruleElement));
    ruleElement.setField("102");
    ruleElement.setValue("a");
    ruleElement.setOperator(OperatorEnum.IS);
    collectEmailConfigRequest.setRuleGroup(rule);
    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
    var entity = new CollectEmailConfigEntity();
    entity.setRuleGroup(rule);
    when(collectEmailConfigRepository.save(any())).thenReturn(entity);
    var res = collectEmailConfigService.createOrUpdate(collectEmailConfigRequest);
    assertNotNull(res);
  }

  @Test
  void createOrUpdate_success_notEmailConfig() throws BusinessException {
    when(emailConfigRepository.findById(any())).thenReturn(Optional.empty());
    CollectEmailConfigRequest collectEmailConfigRequest = new CollectEmailConfigRequest();
    collectEmailConfigRequest.setName("");
    collectEmailConfigRequest.setId(0L);
    assertThrows(BusinessException.class, () -> {
      collectEmailConfigService.createOrUpdate(collectEmailConfigRequest);
    }, ErrorCode.EMAIL_CONFIG_NOT_FOUND.getMessage());
  }


  @Test
  void validateSaveCustomObjectRequest_error_collectEmailConfigNotFound() {
    when(collectEmailConfigRepository.findById(any())).thenReturn(Optional.empty());
    CollectEmailConfigRequest collectEmailConfigRequest = new CollectEmailConfigRequest();
    collectEmailConfigRequest.setId(1L);
    collectEmailConfigRequest.setName(" 2");
    assertThrows(BusinessException.class, () -> {
      collectEmailConfigService.validateSaveRequest(collectEmailConfigRequest, true);
    }, ErrorCode.COLLECT_EMAIL_CONFIG_NAME_EXIST.getMessage());
  }

  @Test
  void validateSaveCustomObjectRequest_error_collectEmailConfigNameInactive() {
    CollectEmailConfigRequest collectEmailConfigRequest = new CollectEmailConfigRequest();
    collectEmailConfigRequest.setName("");
    collectEmailConfigRequest.setId(2L);
    collectEmailConfigRequest.setEmailConfigId(1L);
    CollectEmailConfigEntity config = new CollectEmailConfigEntity();
    config.setEmailConfigId(2L);
    when(collectEmailConfigRepository.findById(any())).thenReturn(Optional.of(config));
    assertThrows(BusinessException.class, () -> {
      collectEmailConfigService.validateSaveRequest(collectEmailConfigRequest, false);
    }, ErrorCode.EMAIL_CONFIG_INACTIVE.getMessage());
  }

  @Test
  void validateSaveCustomObjectRequest_error_collectEmailConfigNameExist() {
    when(collectEmailConfigRepository.existsByNameIgnoreCase(any())).thenReturn(true);
    CollectEmailConfigRequest collectEmailConfigRequest = new CollectEmailConfigRequest();
    collectEmailConfigRequest.setName("");
    assertThrows(BusinessException.class, () -> {
      collectEmailConfigService.validateSaveRequest(collectEmailConfigRequest, true);
    }, ErrorCode.COLLECT_EMAIL_CONFIG_NAME_EXIST.getMessage());
  }

  @Test
  void validateSaveCustomObjectRequest_error_collectEmailConfigFound() {
    var config = new CollectEmailConfigEntity();
    config.setActive(true);
    when(collectEmailConfigRepository.findById(any())).thenReturn(
        Optional.of(config));
    CollectEmailConfigRequest collectEmailConfigRequest = new CollectEmailConfigRequest();
    collectEmailConfigRequest.setId(1L);
    collectEmailConfigRequest.setName("");
    when(collectEmailConfigRepository.existsByIdNotAndNameIgnoreCase(any(), any())).thenReturn(
        true);
    assertThrows(BusinessException.class, () -> {
      collectEmailConfigService.validateSaveRequest(collectEmailConfigRequest, true);
    }, ErrorCode.COLLECT_EMAIL_CONFIG_NAME_EXIST.getMessage());
  }

  @Test
  void validateSaveCustomObjectRequest_error_serviceNotFound() {
    var config = new CollectEmailConfigEntity();
    config.setActive(true);
    when(collectEmailConfigRepository.findById(any())).thenReturn(
        Optional.of(config));
    CollectEmailConfigRequest collectEmailConfigRequest = new CollectEmailConfigRequest();
    collectEmailConfigRequest.setId(1L);
    collectEmailConfigRequest.setName("");
    collectEmailConfigRequest.setServiceId("1");
    when(serviceService.findById(anyString())).thenReturn(null);
    when(collectEmailConfigRepository.existsByIdNotAndNameIgnoreCase(any(), any())).thenReturn(
        false);
    assertThrows(BusinessException.class, () -> {
      collectEmailConfigService.validateSaveRequest(collectEmailConfigRequest, true);
    }, ErrorCode.COLLECT_EMAIL_CONFIG_NAME_EXIST.getMessage());
  }

  @Test
  void validateSaveCustomObjectRequest_error_applicationNotFound() {
    var config = new CollectEmailConfigEntity();
    config.setActive(true);
    when(collectEmailConfigRepository.findById(any())).thenReturn(
        Optional.of(config));
    CollectEmailConfigRequest collectEmailConfigRequest = new CollectEmailConfigRequest();
    collectEmailConfigRequest.setId(1L);
    collectEmailConfigRequest.setName("");
    collectEmailConfigRequest.setServiceId("1");
    collectEmailConfigRequest.setApplicationId("1");
    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(null);
    when(collectEmailConfigRepository.existsByIdNotAndNameIgnoreCase(any(), any())).thenReturn(
        false);
    assertThrows(BusinessException.class, () -> {
      collectEmailConfigService.validateSaveRequest(collectEmailConfigRequest, true);
    }, ErrorCode.COLLECT_EMAIL_CONFIG_NAME_EXIST.getMessage());
  }

  @Test
  void validateSaveCustomObjectRequest_error_priorityNotFound() {
    var config = new CollectEmailConfigEntity();
    config.setActive(true);

    CollectEmailConfigRequest collectEmailConfigRequest = new CollectEmailConfigRequest();
    collectEmailConfigRequest.setId(1L);
    collectEmailConfigRequest.setName("adfa");
    collectEmailConfigRequest.setServiceId("1");
    collectEmailConfigRequest.setApplicationId("1");
    collectEmailConfigRequest.setPriorityConfigId(1L);
    when(collectEmailConfigRepository.findById(any())).thenReturn(Optional.of(config));
    when(collectEmailConfigRepository.existsByIdNotAndNameIgnoreCase(anyLong(), anyString())).thenReturn(false);
    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(null);
    assertThrows(BusinessException.class, () -> {
      collectEmailConfigService.validateSaveRequest(collectEmailConfigRequest, true);
    }, ErrorCode.COLLECT_EMAIL_CONFIG_NAME_EXIST.getMessage());
  }

  @Test
  void findAllByEmailConfigId_success() {
    when(collectEmailConfigRepository.findAllByEmailConfigId(any())).thenReturn(List.of());
    var res = collectEmailConfigService.findAllByEmailConfigId(1L);
    assertNotNull(res);
  }

  @Test
  void findAllByCustomObjectId_success() {
    var config = new CollectEmailConfigEntity();
    config.setContentValue("fd @12");
    var rule = new RuleGroupType();
    var ruleElement = new RuleCondition<>();
    rule.setCombinator(ConditionCombinatorEnum.AND);
    rule.setRules(List.of(ruleElement));
    ruleElement.setField("102");
    ruleElement.setValue("a");
    ruleElement.setOperator(OperatorEnum.IS);
    config.setRuleGroup(rule);
    var config1 = new CollectEmailConfigEntity();
    config1.setContentValue(" 123 @102");
    config1.setRuleGroup(rule);
    var config2 = new CollectEmailConfigEntity();
    config2.setContentValue(null);
    config2.setRuleGroup(rule);

    when(collectEmailConfigRepository.findAll()).thenReturn(List.of(config,config1,config2));
    var res = collectEmailConfigService.findAllByCustomObjectId(102L);
    assertEquals(1, res.size());
  }

  @Test
  void findAllByCustomObjectId_success_checkCustomObject() {
    var config = new CollectEmailConfigEntity();
    config.setContentValue("fd @12");
    var rule = new RuleGroupType();
    var ruleElement = new RuleCondition<>();
    rule.setCombinator(ConditionCombinatorEnum.AND);
    rule.setRules(List.of(ruleElement));
    ruleElement.setField("12");
    ruleElement.setValue("a");
    ruleElement.setOperator(OperatorEnum.IS);
    config.setRuleGroup(rule);
    when(collectEmailConfigRepository.findAll()).thenReturn(List.of(config));
    var res = collectEmailConfigService.findAllByCustomObjectId(102L);
    assertEquals(0, res.size());
  }

  @Test
  void checkCustomObjectInCustomContent_exactMatch() {
    Long customObjectId = 12L;
    String content = "This is a test @12";
    assertTrue(collectEmailConfigService.checkCustomObjectInCustomContent(customObjectId, content));
  }

  @Test
  void checkCustomObjectInCustomContent_noPattern() {
    Long customObjectId = 12L;
    String content = "This is a test without any pattern";
    assertFalse(collectEmailConfigService.checkCustomObjectInCustomContent(customObjectId, content));
  }

  @Test
  void checkCustomObjectInCustomContent_partialMatchWithExtraDigits() {
    Long customObjectId = 12L;
    String content = "This is a test @123";
    assertFalse(collectEmailConfigService.checkCustomObjectInCustomContent(customObjectId, content));
  }

  @Test
  void checkCustomObjectInCustomContent_multipleOccurrencesOneExact() {
    Long customObjectId = 12L;
    String content = "Test @123 and then @12 followed by @124";
    assertTrue(collectEmailConfigService.checkCustomObjectInCustomContent(customObjectId, content));
  }

  @Test
  void checkCustomObjectInCustomContent_multipleOccurrencesNoneExact() {
    Long customObjectId = 12L;
    String content = "Test @123 and then @124";
    assertFalse(collectEmailConfigService.checkCustomObjectInCustomContent(customObjectId, content));
  }

  @Test
  void collectEmailConfigService_emptyContent() {
    Long customObjectId = 12L;
    String content = "";
    assertFalse(collectEmailConfigService.checkCustomObjectInCustomContent(customObjectId, content));
  }

  @Test
  void deleteByEmailConfigId_success() throws BusinessException {
    when(collectEmailConfigRepository.findById(anyLong())).thenReturn(Optional.of(new CollectEmailConfigEntity()));
    collectEmailConfigService.deleteWithId(1L);
  }

  @Test
  void deleteByEmailConfigId_failed_caseNotFound() throws BusinessException {
    when(collectEmailConfigRepository.findById(anyLong())).thenReturn(Optional.empty());
    assertThrows(BusinessException.class, () -> collectEmailConfigService.deleteWithId(1L));
  }
  @TestForDev
  void findAllByServiceId_success() {
    when(collectEmailConfigRepository.findAllByServiceId(any())).thenReturn(List.of(new CollectEmailConfigEntity()));
    var res = collectEmailConfigService.findAllByServiceId("1L");
    verify(collectEmailConfigRepository).findAllByServiceId(any());
    assertEquals(1, res.size());
  }
  @TestForDev
  void findAllByApplicationId_success() {
    when(collectEmailConfigRepository.findAllByApplicationId(any())).thenReturn(List.of(new CollectEmailConfigEntity()));
    var res = collectEmailConfigService.findAllByApplicationId("1L");
    verify(collectEmailConfigRepository).findAllByApplicationId(any());
    assertEquals(1, res.size());
  }
  @TestForDev
  void findAllByPriorityConfigId_success() {
    when(collectEmailConfigRepository.findAllByPriorityConfigId(any())).thenReturn(List.of(new CollectEmailConfigEntity()));
    var res = collectEmailConfigService.findAllByPriorityConfigId(1L);
    verify(collectEmailConfigRepository).findAllByPriorityConfigId(any());
    assertEquals(1, res.size());
  }
}

