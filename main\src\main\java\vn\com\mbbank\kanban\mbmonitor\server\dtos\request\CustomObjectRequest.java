package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.server.constants.MessageConstants;
import vn.com.mbbank.kanban.mbmonitor.server.enums.CustomObjectTypeEnum;

/**
 * Model request service to create or update custom object.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomObjectRequest {

  Long id;

  @NotBlank
  @Size(min = 1, max = CommonConstants.CUSTOM_OBJECT_NAME_MAX_LENGTH,
      message = MessageConstants.CUSTOM_OBJECT_NAME_MAX_LENGTH_MESSAGE_ERROR)
  String name;

  @Size(max = CommonConstants.CUSTOM_OBJECT_DESCRIPTION_MAX_LENGTH,
      message = MessageConstants.CUSTOM_OBJECT_DESCRIPTION_MAX_LENGTH_MESSAGE_ERROR)
  String description;

  CustomObjectTypeEnum type;

  @Size(max = CommonConstants.CUSTOM_OBJECT_REGEX_MAX_LENGTH,
      message = MessageConstants.CUSTOM_OBJECT_REGEX_MAX_LENGTH_MESSAGE_ERROR)
  String regex;

  @Max(value = CommonConstants.CUSTOM_OBJECT_INDEX_MAX_LENGTH,
      message = MessageConstants.CUSTOM_OBJECT_FROM_INDEX_MAX_LENGTH_MESSAGE_ERROR)
  Integer fromIndex;

  @Max(value = CommonConstants.CUSTOM_OBJECT_INDEX_MAX_LENGTH,
      message = MessageConstants.CUSTOM_OBJECT_TO_INDEX_MAX_LENGTH_MESSAGE_ERROR)
  Integer toIndex;

  @Size(max = CommonConstants.CUSTOM_OBJECT_KEYWORD_MAX_LENGTH,
      message = MessageConstants.CUSTOM_OBJECT_FROM_KEYWORD_MAX_LENGTH_MESSAGE_ERROR)
  String fromKeyword;

  @Size(max = CommonConstants.CUSTOM_OBJECT_KEYWORD_MAX_LENGTH,
      message = MessageConstants.CUSTOM_OBJECT_TO_KEYWORD_MAX_LENGTH_MESSAGE_ERROR)
  String toKeyword;

}
