package vn.com.mbbank.kanban.mbmonitor.server.configs;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;


/**
 * SQL Config for SQL execution.
 */
@Getter
@Setter
@Component
@PropertySource("classpath:application.properties")
public class SqlConfigProperties {

  @Value("${spring.datasource.url}")
  private String url;
  @Value("${spring.datasource.hikari.username}")
  private String username;
  @Value("${spring.datasource.hikari.password}")
  private String password;
  @Value("${spring.datasource.driver-class-name}")
  private String driver;

  @Value("${superiors.sql-execution.private-password}")
  private String privatePassword;

  /**
   * Get SQL Connection.

   * @return SQL Connection
   * @throws SQLException When cannot init SQL
   */
  public Connection getConnection() throws SQLException {
    Connection connection =
        DriverManager.getConnection(url, username, password);
    connection.setAutoCommit(false);
    return connection;
  }
}
