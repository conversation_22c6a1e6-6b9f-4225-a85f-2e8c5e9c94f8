package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.MaintenanceTimeConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.MaintenanceTimeConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.MaintenanceTimeConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.MaintenanceTimeConfigService;

/**
 * Controller logic AlertGroupConfigController.
 */
@RestController
@RequestMapping(ServerUrl.MAINTENANCE_TIME_CONFIG_URL)
@RequiredArgsConstructor
public class MaintenanceTimeConfigController extends BaseController {
  private final MaintenanceTimeConfigService maintenanceTimeConfigService;
  private final MaintenanceTimeConfigResponseMapper maintenanceTimeConfigResponseMapper =
      MaintenanceTimeConfigResponseMapper.INSTANCE;


  /**
   * Api find group config by ID.
   *
   * @param id id of priority config.
   * @return MaintenanceTimeConfigResponse.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping(value = "/{id}")
  ResponseData<MaintenanceTimeConfigResponse> findById(@PathVariable Long id)
      throws BusinessException {
    return ResponseUtils.success(maintenanceTimeConfigService.findByIdWithDetail(id));
  }

  /**
   * Api find a list of group config.
   *
   * @return List of AlertPriorityConfigResponse
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  ResponseData<List<MaintenanceTimeConfigResponse>> findAll() {
    return ResponseUtils.success(maintenanceTimeConfigService.findAllMaintenance());
  }

  /**
   * Api save group config.
   *
   * @param maintenanceTimeConfigRequest input data.
   * @return MaintenanceTimeConfigResponse.
   * @throws BusinessException exception
   */
  @PostMapping
  ResponseData<MaintenanceTimeConfigResponse> save(
      @Valid @RequestBody MaintenanceTimeConfigRequest maintenanceTimeConfigRequest)
      throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(maintenanceTimeConfigRequest.getId()));
    return ResponseUtils.success(
        maintenanceTimeConfigResponseMapper.map(maintenanceTimeConfigService.save(maintenanceTimeConfigRequest)));
  }

  /**
   * Api delete priority config.
   *
   * @param id priority config id.
   * @return String
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, action = PermissionActionEnum.DELETE)
  })
  @DeleteMapping(value = "/{id}")
  ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    maintenanceTimeConfigService.deleteConfigById(id);
    return ResponseUtils.success("OK");
  }


  /**
   * Api delete priority config.
   *
   * @param id     alert group id.
   * @param active active status
   * @return list of alert priority config
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, action = PermissionActionEnum.EDIT),
  })
  @PutMapping("/{id}")
  public ResponseData<MaintenanceTimeConfigResponse> updateActive(@PathVariable Long id,
                                                                  @RequestParam(value = "active") Boolean active)
      throws BusinessException {
    return ResponseUtils.success(
        maintenanceTimeConfigResponseMapper.map(maintenanceTimeConfigService.updateActive(id, active)));
  }
}
