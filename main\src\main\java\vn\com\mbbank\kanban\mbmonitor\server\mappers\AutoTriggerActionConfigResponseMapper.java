package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import java.util.List;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AutoTriggerActionConfigResponse;

/**
 * Mapper interface for mapping between `AutoTriggerActionConfigResponse` and `AutoTriggerActionConfigEntity`.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AutoTriggerActionConfigResponseMapper extends
    KanbanBaseMapper<AutoTriggerActionConfigResponse, AutoTriggerActionConfigEntity> {
  AutoTriggerActionConfigResponseMapper INSTANCE = Mappers.getMapper(AutoTriggerActionConfigResponseMapper.class);

  /**
   * map from AutoTriggerActionConfigEntity to AutoTriggerActionConfigResponse.
   *
   * @param entity       AutoTriggerActionConfigEntity.
   * @param services     list of service
   * @param applications list of application
   * @param executions     list of execution
   * @return AutoTriggerActionConfigResponse
   */
  default AutoTriggerActionConfigResponse map(AutoTriggerActionConfigEntity entity,
                                              List<ServiceEntity> services,
                                              List<ApplicationResponse> applications,
                                              List<ExecutionResponse> executions) {

    var autoTriggerActionConfigResponse = this.map(entity);
    autoTriggerActionConfigResponse.setServices(ServiceResponseMapper.INSTANCE.map(services));
    autoTriggerActionConfigResponse.setApplications(applications);
    autoTriggerActionConfigResponse.setExecutions(executions);
    return autoTriggerActionConfigResponse;
  }

  /**
   * map from AutoTriggerActionConfigEntity to AutoTriggerActionConfigResponse.
   *
   * @param entity           AutoTriggerActionConfigEntity.
   * @param services         list of service
   * @param applications     list of application
   * @param executions       list of execution
   * @param logRuleGroup     rule group for log
   * @return AutoTriggerActionConfigResponse
   */
  default AutoTriggerActionConfigResponse map(AutoTriggerActionConfigEntity entity,
                                              List<ServiceEntity> services,
                                              List<ApplicationResponse> applications,
                                              List<ExecutionResponse> executions,
                                              String logRuleGroup) {

    var autoTriggerActionConfigResponse = this.map(entity, services, applications, executions);
    autoTriggerActionConfigResponse.setRuleGroup(null);
    if (!KanbanCommonUtil.isNullOrEmpty(logRuleGroup)) {
      autoTriggerActionConfigResponse.setCondition(logRuleGroup);
    } else {
      autoTriggerActionConfigResponse.setCondition(Objects.nonNull(entity.getRuleGroup())
              ? entity.getRuleGroup().toString() : "");
    }
    return autoTriggerActionConfigResponse;
  }
}
