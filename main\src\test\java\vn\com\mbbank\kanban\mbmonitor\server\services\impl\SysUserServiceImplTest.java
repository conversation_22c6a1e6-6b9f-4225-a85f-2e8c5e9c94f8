package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import vn.com.mbbank.kanban.core.configs.configurations.KeycloakCentrailizedConfig;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.utils.KanbanApplicationUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysRoleWithPermissionsModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SysUserResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysPermissionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRedisService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.UserWithRolesDto;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.SysUserRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.SysRoleService;
import vn.com.mbbank.kanban.mbmonitor.server.services.SysUserRoleService;
import vn.com.mbbank.kanban.test.ApplicationTest;
import vn.com.mbbank.kanban.test.WithMockMbStaff;

class SysUserServiceImplTest extends ApplicationTest {
  @Mock
  SysUserRepository repository;
  @Spy
  @InjectMocks
  SysUserServiceImpl sysUserServiceImpl;
  @Mock
  SysRoleService sysRoleService;
  @Mock
  SysLogKafkaProducerService sysLogKafkaProducerService;

  @Mock
  SysUserRoleService sysUserRoleService;

  @Mock
  CommonRedisService commonRedisService;


  @Test
  @WithMockMbStaff
  void findCurrentUserWithCreateNew_success_Cache() {
    SysUserResponse response = new SysUserResponse();
    response.setId(1L);
    when(commonRedisService.isExistsByKey(any())).thenReturn(true);
    when(commonRedisService.get(any(), any(), any())).thenReturn(response);
    SysUserResponse result = sysUserServiceImpl.findOrCreateNew(anyString());
    assertEquals(1L, result.getId());
  }

  @Test
  @WithMockMbStaff
  void findCurrentUserWithCreateNew_success_caseUserExists() {
    when(commonRedisService.isExistsByKey(any())).thenReturn(false);
    var sysUserEntity = new SysUserEntity();
    sysUserEntity.setId(1L);
    sysUserEntity.setUserName("thangnv");
    sysUserEntity.setEmail("<EMAIL>");
    sysUserEntity.setIsActive(true);
    when(repository.findByUserName(anyString())).thenReturn(Optional.of(sysUserEntity));
    when(sysRoleService.findAllWithPermissionsByUserIdIn(any())).thenReturn(new ArrayList<>());
    SysUserEntity result = sysUserServiceImpl.findOrCreateNew(anyString());
    assertEquals(1L, result.getId());
    verify(repository).findByUserName(any());
  }

  @Test
  @WithMockMbStaff
  void findCurrentUserWithCreateNew_success_caseUserNotExists() {
    when(commonRedisService.isExistsByKey(any())).thenReturn(false);
    var sysUserEntity = new SysUserEntity();
    sysUserEntity.setId(1L);
    sysUserEntity.setUserName("thangnv");
    sysUserEntity.setEmail("<EMAIL>");
    sysUserEntity.setIsActive(true);

    when(repository.findByUserName(anyString())).thenReturn(Optional.empty());
    when(repository.save(any())).thenReturn(sysUserEntity);

    SysUserEntity result = sysUserServiceImpl.findOrCreateNew(anyString());
    assertEquals(1L, result.getId());
    verify(repository).save(any());
    verify(repository).findByUserName(any());
  }

  @Test
  @WithMockMbStaff
  void getRepository() {
    JpaCommonRepository<SysUserEntity, Long> result = sysUserServiceImpl.getRepository();
    assertEquals(repository, result);
  }


  @Test
  @WithMockMbStaff
  void findDetailById() {
    var sysUserEntity = new SysUserEntity();
    sysUserEntity.setId(1L);
    sysUserEntity.setUserName("thangnv");
    sysUserEntity.setEmail("<EMAIL>");
    sysUserEntity.setIsActive(true);
    doReturn(sysUserEntity).when(sysUserServiceImpl).findById(any());

    SysPermissionEntity sysPermissionEntity = new SysPermissionEntity();
    sysPermissionEntity.setRoleId(1L);
    sysPermissionEntity.setId(1L);
    sysPermissionEntity.setAction(PermissionActionEnum.VIEW);
    sysPermissionEntity.setModule(PermissionModuleEnum.WEBHOOK_CONFIG);
    List<SysPermissionEntity> permissions = List.of(sysPermissionEntity);
    SysRoleWithPermissionsModel sysRoleWithPermissionsModel = new SysRoleWithPermissionsModel();
    sysRoleWithPermissionsModel.setUserId(1L);
    sysRoleWithPermissionsModel.setId(1L);
    sysRoleWithPermissionsModel.setPermissions(permissions);

    when(sysRoleService.findAllWithPermissionsByUserIdIn(any())).thenReturn(
        List.of(sysRoleWithPermissionsModel));
    var result = sysUserServiceImpl.findWithRolesById(1L);
    assertEquals(result.getId(), 1);
  }

  @Test
  @WithMockMbStaff
  void findByUserName() {
    var sysUserEntity = new SysUserEntity();
    sysUserEntity.setId(1L);
    sysUserEntity.setUserName("thangnv");
    sysUserEntity.setEmail("<EMAIL>");
    sysUserEntity.setIsActive(true);
    doReturn(Optional.of(sysUserEntity)).when(repository).findByUserName(any());
    var result = sysUserServiceImpl.findByUserName("thangnv");
    assertEquals(result.get().getId(), 1);
  }

  @Test
  @WithMockMbStaff
  void settingUser_null() throws BusinessException {
    var sysUserEntity = new SysUserEntity();
    sysUserEntity.setId(1L);
    sysUserEntity.setUserName("thangnv");
    sysUserEntity.setEmail("<EMAIL>");
    sysUserEntity.setIsActive(true);
    doReturn(Optional.of(sysUserEntity)).when(repository).findById(any());
    var result = sysUserServiceImpl.settingUser(1L, null, null);
    assertEquals(result.getId(), 1);
  }

  @Test
  @WithMockMbStaff
  void settingUser_active_null() throws BusinessException {
    var sysUserEntity = new SysUserEntity();
    sysUserEntity.setId(1L);
    sysUserEntity.setUserName("thangnv");
    sysUserEntity.setEmail("<EMAIL>");
    sysUserEntity.setIsActive(true);
    doReturn(Optional.of(sysUserEntity)).when(repository).findById(any());
    doReturn(sysUserEntity).when(repository).save(any());
    var result = sysUserServiceImpl.settingUser(1L, null, true);
    assertEquals(result.getId(), 1);
  }

  @Test
  @WithMockMbStaff
  void settingUser_admin_null() throws BusinessException {
    var sysUserEntity = new SysUserEntity();
    sysUserEntity.setId(1L);
    sysUserEntity.setUserName("thangnv");
    sysUserEntity.setEmail("<EMAIL>");
    sysUserEntity.setIsActive(true);
    doReturn(Optional.of(sysUserEntity)).when(repository).findById(any());
    doReturn(sysUserEntity).when(repository).save(any());
    var result = sysUserServiceImpl.settingUser(1L, true, null);
    assertEquals(result.getId(), 1);
  }

  @Test
  @WithMockMbStaff
  void settingUser_success() throws BusinessException {
    var sysUserEntity = new SysUserEntity();
    sysUserEntity.setId(1L);
    sysUserEntity.setUserName("thangnv");
    sysUserEntity.setEmail("<EMAIL>");
    sysUserEntity.setIsActive(true);
    doReturn(Optional.of(sysUserEntity)).when(repository).findById(any());
    doReturn(sysUserEntity).when(repository).save(any());
    var result = sysUserServiceImpl.settingUser(1L, true, true);
    assertEquals(result.getId(), 1);
  }

  @Test
  @WithMockMbStaff
  void settingUser_exception() throws BusinessException {
    doReturn(Optional.empty()).when(repository).findById(any());
    Assertions.assertThrows(BusinessException.class, () -> {
      sysUserServiceImpl.settingUser(1L, true, true);
    });
  }

  @Test
  @WithMockMbStaff
  void settingUser_exception_yourself() throws BusinessException {
    var sysUserEntity = new SysUserEntity();
    sysUserEntity.setId(1L);
    sysUserEntity.setUserName("kanban");
    sysUserEntity.setEmail("<EMAIL>");
    sysUserEntity.setIsActive(true);
    doReturn(Optional.of(sysUserEntity)).when(repository).findById(any());
    KeycloakCentrailizedConfig mockConfig = mock(KeycloakCentrailizedConfig.class);
    when(mockConfig.getEnable()).thenReturn(false);
    new KanbanApplicationUtils(mockConfig);
    Assertions.assertThrows(BusinessException.class, () -> {
        sysUserServiceImpl.settingUser(1L, true, true);
      });
    }

  @Test
  @WithMockMbStaff
  void save_exception() throws BusinessException {
    doReturn(Optional.empty()).when(repository).findById(any());
    UserWithRolesDto userWithRolesDto = new UserWithRolesDto();
    userWithRolesDto.setId(1L);
    userWithRolesDto.setUserName("thangnv");
    Assertions.assertThrows(BusinessException.class, () -> {
      sysUserServiceImpl.saveWithRoles(userWithRolesDto);
    });
  }

  @Test
  @WithMockMbStaff
  void save_exception_users_exists() throws BusinessException {
    when(repository.existsByUserName(any())).thenReturn(true);
    UserWithRolesDto userWithRolesDto = new UserWithRolesDto();
    userWithRolesDto.setUserName("thangnv");
    Assertions.assertThrows(BusinessException.class, () -> {
      sysUserServiceImpl.saveWithRoles(userWithRolesDto);
    });
  }

  @Test
  @WithMockMbStaff
  void save_update() throws BusinessException {
    UserWithRolesDto userWithRolesDto = new UserWithRolesDto();
    userWithRolesDto.setId(1L);
    userWithRolesDto.setRoleIds(List.of(1L));
    userWithRolesDto.setUserName("thangnv");
    userWithRolesDto.setIsUserLocal(true);
    userWithRolesDto.setPassword("123456");
    SysUserEntity userEntity = new SysUserEntity();
    userEntity.setId(1L);
    doReturn(Optional.of(userEntity)).when(repository).findById(any());
    doReturn(userEntity).when(repository).save(any());
    var result = sysUserServiceImpl.saveWithRoles(userWithRolesDto);
    assertEquals(result.getId(), 1L);
  }

  @Test
  @WithMockMbStaff
  void save_create() throws BusinessException {
    UserWithRolesDto userWithRolesDto = new UserWithRolesDto();
    userWithRolesDto.setRoleIds(List.of(1L));
    userWithRolesDto.setUserName("thangnv");
    SysUserEntity userEntity = new SysUserEntity();
    userEntity.setId(1L);
    doReturn(userEntity).when(repository).save(any());
    var result = sysUserServiceImpl.saveWithRoles(userWithRolesDto);
    assertEquals(result.getId(), 1L);
  }

  @Test
  @WithMockMbStaff
  void save_Role_empty() throws BusinessException {
    UserWithRolesDto userWithRolesDto = new UserWithRolesDto();
    userWithRolesDto.setId(1L);
    userWithRolesDto.setUserName("thangnv");
    SysUserEntity userEntity = new SysUserEntity();
    userEntity.setId(1L);
    doReturn(Optional.of(userEntity)).when(repository).findById(any());
    doReturn(userEntity).when(repository).save(any());
    var result = sysUserServiceImpl.saveWithRoles(userWithRolesDto);
    assertEquals(result.getId(), 1L);
  }

  @Test
  @WithMockMbStaff
  void findAllByRoleId() throws BusinessException {
    Pageable pageable = PageRequest.of(0, 10);
    Page<SysUserResponse> page = new PageImpl<>(new ArrayList<>(),
        pageable, 0);
    doReturn(page).when(repository).findAllByRoleId(any(), any());

    var result = sysUserServiceImpl.findAllByRoleId(1L, new PaginationRequestDTO());
    assertEquals(result.getTotalElements(), 0);
  }

  @Test
  @WithMockMbStaff
  void deleteById_success() throws BusinessException {
    when(repository.findById(anyLong())).thenReturn(Optional.of(new SysUserEntity()));
    sysUserServiceImpl.deleteWithId(1L);
    verify(sysUserRoleService).deleteAllByUserId(anyLong());
    verify(repository).deleteById(anyLong());
  }

  @Test
  @WithMockMbStaff
  void deleteById_failed_caseNotFound() throws BusinessException {
    when(repository.findById(anyLong())).thenReturn(Optional.empty());
    assertThrows(BusinessException.class, () -> sysUserServiceImpl.deleteWithId(1L));
  }

  @Test
  @WithMockMbStaff
  void deleteByIdIn_success() {
    when(repository.deleteByIdIn(any())).thenReturn(1L);
    var result = sysUserServiceImpl.deleteByIdIn(List.of(1L));
    assertEquals(1L, result);
  }

  @Test
  @WithMockMbStaff
  void findAllByUserNameIn_success() {
    when(repository.findAllByUserNameIn(anyList())).thenReturn(List.of());
    var res = sysUserServiceImpl.findAllByUserNameIn(List.of());
    assertNotNull(res);
  }

  @Test
  @WithMockMbStaff
  void save_createLocal() throws BusinessException {
    UserWithRolesDto userWithRolesDto = new UserWithRolesDto();
    userWithRolesDto.setRoleIds(List.of(1L));
    userWithRolesDto.setUserName("thangnv");
    userWithRolesDto.setIsUserLocal(true);
    userWithRolesDto.setPassword("12345678");
    SysUserEntity userEntity = new SysUserEntity();
    userEntity.setId(1L);
    doReturn(userEntity).when(repository).save(any());
    var result = sysUserServiceImpl.saveWithRoles(userWithRolesDto);
    assertEquals(result.getId(), 1L);
  }

  @Test
  @WithMockMbStaff
  void save_createLocal_failure() throws BusinessException {
    UserWithRolesDto userWithRolesDto = new UserWithRolesDto();
    userWithRolesDto.setRoleIds(List.of(1L));
    userWithRolesDto.setUserName("thangnv");
    userWithRolesDto.setIsUserLocal(true);
    SysUserEntity userEntity = new SysUserEntity();
    userEntity.setId(1L);

    assertThrows(BusinessException.class, () ->
            sysUserServiceImpl.saveWithRoles(userWithRolesDto));
  }

  @Test
  @WithMockMbStaff
  void saveWithRoles_isUserLocalTrue_shouldEncodePassword() throws BusinessException {
    // Arrange
    UserWithRolesDto dto = new UserWithRolesDto();
    dto.setUserName("thangnv");
    dto.setIsUserLocal(true);
    dto.setPassword("12345678");
    dto.setRoleIds(List.of());

    SysUserEntity savedUser = new SysUserEntity();
    savedUser.setId(1L);
    savedUser.setUserName("thangnv");

    doReturn(false).when(repository).existsByUserName("thangnv");
    doReturn(savedUser).when(repository).save(any());

    // Act
    SysUserEntity result = sysUserServiceImpl.saveWithRoles(dto);

    // Assert
    assertEquals("thangnv", result.getUserName());
    verify(repository).save(argThat(user ->
            new BCryptPasswordEncoder().matches("12345678", user.getPassword())
    ));
  }

  @Test
  @WithMockMbStaff
  void saveWithRoles_roleIdsNotEmpty_butRolesEmpty_shouldReturnUserWithoutSavingRoles() throws BusinessException {
    // Arrange
    UserWithRolesDto dto = new UserWithRolesDto();
    dto.setUserName("john");
    dto.setIsUserLocal(false);
    dto.setRoleIds(List.of(1L)); // Có roleIds

    SysUserEntity savedUser = new SysUserEntity();
    savedUser.setId(2L);
    savedUser.setUserName("john");

    doReturn(false).when(repository).existsByUserName("john");
    doReturn(savedUser).when(repository).save(any());
    doReturn(Collections.emptyList()).when(sysRoleService).findAllById(List.of(1L));

    // Act
    SysUserEntity result = sysUserServiceImpl.saveWithRoles(dto);

    // Assert
    assertEquals("john", result.getUserName());
    verify(sysUserRoleService, never()).saveAll(any());
    verify(sysLogKafkaProducerService, never()).send(any(), any(), any());
  }
}