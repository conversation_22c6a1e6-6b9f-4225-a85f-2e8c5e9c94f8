package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.WorkflowTemplateRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.WorkflowTemplateResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowTemplateEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.WorkflowTemplateResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.WorkflowTemplateService;
import vn.com.mbbank.kanban.test.ControllerTest;

@ExtendWith(MockitoExtension.class)
class WorkflowTemplateControllerTest extends ControllerTest {

  @Mock
  private WorkflowTemplateService workflowTemplateService;

  @Mock
  private CommonAclPermissionService commonAclPermissionService;

  // Mapper is usually tested separately or assumed correct if using MapStruct INSTANCE
  private final WorkflowTemplateResponseMapper workflowTemplateResponseMapper = WorkflowTemplateResponseMapper.INSTANCE;

  @InjectMocks
  @Spy
  private WorkflowTemplateController workflowTemplateController;

  // Test data
  private final String testId = "test-id-123";
  private final String testName = "Test Workflow Template";
  private final String testDescription = "Test Description";

  @Test
  void findById_success() throws BusinessException {
    // Arrange
    verifyPermissions(List.of(
        new AclPermissionModel(PermissionModuleEnum.IT_SERVICE_MANAGEMENT, PermissionActionEnum.VIEW)
    ), "findById", WorkflowTemplateController.class);

    WorkflowTemplateEntity workflowTemplateEntity = createTestWorkflowTemplateEntity();
    when(workflowTemplateService.findWithId(testId)).thenReturn(new WorkflowTemplateResponse());

    // Act
    ResponseData<WorkflowTemplateResponse> response = workflowTemplateController.findById(testId);

    // Assert
    assertNotNull(response);
  }

  @Test
  void findById_failed_whenNotFound() throws BusinessException {
    // Arrange
    when(workflowTemplateService.findWithId(testId))
        .thenThrow(new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_NOT_FOUND));

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateController.findById(testId);
    });

    assertEquals(ErrorCode.WORKFLOW_TEMPLATE_NOT_FOUND.getCode(), exception.getCode());
    verify(workflowTemplateService, times(1)).findWithId(testId);
  }

  @Test
  void findAllWithPaging_success() throws BusinessException {
    // Arrange
    verifyPermissions(List.of(
        new AclPermissionModel(PermissionModuleEnum.IT_SERVICE_MANAGEMENT, PermissionActionEnum.VIEW)
    ), "findAllWithPaging", WorkflowTemplateController.class);

    PaginationRequestDTO requestDTO = new PaginationRequestDTO();
    WorkflowTemplateEntity workflowTemplateEntity = createTestWorkflowTemplateEntity();
    Page<WorkflowTemplateEntity> pageResponse = new PageImpl<>(Collections.singletonList(workflowTemplateEntity));
    when(workflowTemplateService.findAllWithPaging(any(PaginationRequestDTO.class))).thenReturn(pageResponse);

    // Act
    ResponseData<Page<WorkflowTemplateResponse>> response = workflowTemplateController.findAllWithPaging(requestDTO);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(1, response.getData().getTotalElements());
    assertEquals(testId, response.getData().getContent().get(0).getId());
    
    // Verify that the request was modified with search properties and sort
    assertEquals(List.of("name", "description"), requestDTO.getPropertiesSearch());
    assertEquals("createdDate", requestDTO.getSortBy());
    
    verify(workflowTemplateService, times(1)).findAllWithPaging(requestDTO);
  }

  @Test
  void save_success_whenCreateOrUpdate() throws BusinessException {
    // Arrange
    WorkflowTemplateRequest workflowTemplateRequest = createTestWorkflowTemplateRequest();
    WorkflowTemplateEntity workflowTemplateEntity = createTestWorkflowTemplateEntity();
    
    // Note: Permission for save is handled by makeSureCreateOrUpdate in BaseController
    doNothing().when(workflowTemplateController).makeSureCreateOrUpdate(any(), any(), any(), any());
    when(workflowTemplateService.createOrUpdate(any(WorkflowTemplateRequest.class))).thenReturn(workflowTemplateEntity);

    // Act
    ResponseData<WorkflowTemplateResponse> response = workflowTemplateController.save(workflowTemplateRequest);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(testId, response.getData().getId());
    verify(workflowTemplateService, times(1)).createOrUpdate(workflowTemplateRequest);
    verify(workflowTemplateController, times(1)).makeSureCreateOrUpdate(
        PermissionModuleEnum.IT_SERVICE_MANAGEMENT,
        PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT,
        Collections.singleton(workflowTemplateRequest.getId())
    );
  }

  @Test
  void save_failed_whenNameExists() throws BusinessException {
    // Arrange
    WorkflowTemplateRequest workflowTemplateRequest = createTestWorkflowTemplateRequest();
    
    doNothing().when(workflowTemplateController).makeSureCreateOrUpdate(any(), any(), any(), any());
    when(workflowTemplateService.createOrUpdate(any(WorkflowTemplateRequest.class)))
        .thenThrow(new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_NAME_IS_EXISTED));

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateController.save(workflowTemplateRequest);
    });

    assertEquals(ErrorCode.WORKFLOW_TEMPLATE_NAME_IS_EXISTED.getCode(), exception.getCode());
    verify(workflowTemplateService, times(1)).createOrUpdate(workflowTemplateRequest);
  }

  @Test
  void deleteById_success() throws BusinessException {
    // Arrange
    verifyPermissions(List.of(
        new AclPermissionModel(PermissionModuleEnum.IT_SERVICE_MANAGEMENT, PermissionActionEnum.DELETE)
    ), "deleteById", WorkflowTemplateController.class);

    doNothing().when(workflowTemplateService).deleteWithId(testId);

    // Act
    ResponseData<String> response = workflowTemplateController.deleteById(testId);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertEquals("OK", response.getData());
    verify(workflowTemplateService, times(1)).deleteWithId(testId);
  }

  @Test
  void deleteById_failed_whenNotFound() throws BusinessException {
    // Arrange
    doThrow(new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_NOT_FOUND))
        .when(workflowTemplateService).deleteWithId(testId);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      workflowTemplateController.deleteById(testId);
    });

    assertEquals(ErrorCode.WORKFLOW_TEMPLATE_NOT_FOUND.getCode(), exception.getCode());
    verify(workflowTemplateService, times(1)).deleteWithId(testId);
  }

  // Helper methods to create test data
  private WorkflowTemplateEntity createTestWorkflowTemplateEntity() {
    WorkflowTemplateEntity entity = new WorkflowTemplateEntity();
    entity.setId(testId);
    entity.setName(testName);
    entity.setDescription(testDescription);
    entity.setFormLayout(new ArrayList<>());
    return entity;
  }

  private WorkflowTemplateRequest createTestWorkflowTemplateRequest() {
    return WorkflowTemplateRequest.builder()
        .id(testId)
        .name(testName)
        .description(testDescription)
        .formLayout(new ArrayList<>())
        .workflow(new WorkflowModel())
        .build();
  }
}
