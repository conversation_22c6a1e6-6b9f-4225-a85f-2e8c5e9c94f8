package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;

/**
 * WorkflowTemplateNodePositionModel.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes({
    @JsonSubTypes.Type(value = StartEndNodeConfigurationModel.class, name = "START"),
    @JsonSubTypes.Type(value = StartEndNodeConfigurationModel.class, name = "END"),
    @JsonSubTypes.Type(value = TeamsNodeConfigurationModel.class, name = "TEAMS"),
    @JsonSubTypes.Type(value = EmailNodeConfigurationModel.class, name = "EMAIL"),
    @JsonSubTypes.Type(value = ExecutionNodeConfigurationModel.class, name = "EXECUTION"),
    @JsonSubTypes.Type(value = ExecutionNodeConfigurationModel.class, name = "API"),
    @JsonSubTypes.Type(value = ExecutionNodeConfigurationModel.class, name = "PYTHON"),
    @JsonSubTypes.Type(value = ExecutionNodeConfigurationModel.class, name = "SQL")
})
public class BaseNodeConfigurationModel {
  @NotNull
  String name;
  @NotNull
  WorkflowNodeTypeEnum type;
}
