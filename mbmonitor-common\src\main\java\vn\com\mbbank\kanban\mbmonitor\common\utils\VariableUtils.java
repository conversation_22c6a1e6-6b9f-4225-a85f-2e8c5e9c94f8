package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;

/**
 * Utility class for extracting and replacing template variables (e.g., {{variable}}) in strings.
 *
 * <AUTHOR>
 * @created_date 07/18/2025
 */
public class VariableUtils {
  
  // Regular expression to match {{variable}} patterns
  private static final String VARIABLE_REGEX = "\\{\\{(.*?)\\}\\}";
  
  /**
   * Extracts all unique variable names from the input string.
   * Variable names are expected to be wrapped in double curly braces, e.g. {{variable}}.
   *
   * @param body the input string that may contain template variables
   * @return a Set of unique variable names (without the curly braces)
   */
  public static Set<String> getVariableNames(String body) {
    Set<String> variables = new HashSet<>();
    if (KanbanCommonUtil.isEmpty(body)) {
      return variables;
    }
    
    Pattern pattern = Pattern.compile(VARIABLE_REGEX);
    Matcher matcher = pattern.matcher(body);
    
    while (matcher.find()) {
      variables.add(matcher.group(1).trim());
    }
    
    return variables;
  }
  
  /**
   * Replaces all template variables in the form {{variable}} using simple string replacement.
   * Assumes the input has already been scanned for variables.
   *
   * @param body         the input string containing template variables
   * @param replacements a map containing variable names as keys and replacement strings as values
   * @return the input string with all template variables replaced by their values
   */
  public static String replaceVariablesSimple(String body, Map<String, String> replacements) {
    if (KanbanCommonUtil.isEmpty(body) || KanbanCommonUtil.isEmpty(replacements)) {
      return body;
    }
    
    for (Map.Entry<String, String> entry : replacements.entrySet()) {
      String variable = "{{" + entry.getKey() + "}}";
      String value = entry.getValue();
      body = body.replaceAll(Pattern.quote(variable), Matcher.quoteReplacement(value));
    }
    return body;
  }
  
  /**
   * Returns the value of the given VariableEntity.
   * If the variable is marked as hidden, it will be decrypted before returning.
   * Otherwise, the raw value is returned as-is.
   *
   * @param variableEntity the VariableEntity object containing the value and visibility flag
   * @return the decrypted value if hidden, or the plain value otherwise
   */
  public static String getVariableValueByEntity(VariableEntity variableEntity) {
    return variableEntity.isHidden()
        ? KanbanEncryptorUtils.decrypt(variableEntity.getValue())
        : variableEntity.getValue();
  }
}