package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ExecutionGroupRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionGroupDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionService;
import vn.com.mbbank.kanban.mbmonitor.server.services.SysPermissionService;

@Service
@RequiredArgsConstructor
public class ExecutionGroupDependencyServiceImpl extends BaseServiceImpl<ExecutionGroupEntity, String>
    implements ExecutionGroupDependencyService {
  private final ExecutionGroupRepository executionGroupRepository;
  private final ExecutionService executionService;
  private final SysPermissionService sysPermissionService;

  @Override
  protected JpaCommonRepository<ExecutionGroupEntity, String> getRepository() {
    return executionGroupRepository;
  }


  @Override
  public void deleteWithId(String id) throws BusinessException {
    var executionGroup = executionGroupRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.EXECUTION_GROUP_NOT_FOUND));
    var executions = executionService.findAllByExecutionGroupId(id);
    if (CollectionUtils.isNotEmpty(executions)) {
      var ids = executions.stream()
              .map(ExecutionResponse::getId)
              .toList();
      executionService.deleteByIdIn(ids);
    }
    sysPermissionService.deleteAllByModuleAndActionAndTypeAndModuleParentIdAndModuleId(
        PermissionModuleEnum.RUN_EXECUTION,
        PermissionActionEnum.EXECUTE, PermissionTypeEnum.SUB_MODULE, null, id);
    sysPermissionService.deleteAllByModuleAndActionAndTypeAndModuleParentId(
        PermissionModuleEnum.RUN_EXECUTION,
        PermissionActionEnum.EXECUTE, PermissionTypeEnum.SUB_MODULE, id);
    delete(executionGroup);
  }
}
