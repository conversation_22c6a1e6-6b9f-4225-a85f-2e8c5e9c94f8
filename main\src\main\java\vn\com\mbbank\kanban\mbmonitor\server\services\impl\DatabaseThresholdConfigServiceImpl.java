package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.util.List;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.KafkaJobModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseThresholdConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaJobTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.JobKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;
import vn.com.mbbank.kanban.mbmonitor.common.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.DatabaseThresholdConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.DatabaseThresholdConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.DatabaseThresholdConfigEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.DatabaseThresholdConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.DatabaseThresholdConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.DatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.server.services.DatabaseThresholdConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DatabaseThresholdConfigServiceImpl extends BaseServiceImpl<DatabaseThresholdConfigEntity, String>
    implements DatabaseThresholdConfigService {
  DatabaseThresholdConfigRepository databaseThresholdConfigRepository;
  DatabaseConnectionService databaseConnectionService;
  SysLogKafkaProducerService sysLogKafkaProducerService;
  JobKafkaProducerService jobKafkaProducerService;
  ServiceService serviceService;
  ApplicationService applicationService;
  AlertPriorityConfigService alertPriorityConfigService;

  @Override
  public JpaCommonRepository<DatabaseThresholdConfigEntity, String> getRepository() {
    return databaseThresholdConfigRepository;
  }

  @Override
  @Transactional(rollbackFor = BusinessException.class)
  public DatabaseThresholdConfigResponse createOrUpdate(
      DatabaseThresholdConfigRequest request)
      throws BusinessException {
    var isCreateMode = KanbanCommonUtil.isEmpty(request.getId());
    validateSaveRequest(request);
    var databaseThresholdConfig =
        DatabaseThresholdConfigEntityMapper.INSTANCE.map(request);
    databaseThresholdConfig.setName(
        StringUtils.capitalizeFirstLetter(request.getName()));
    if (isCreateMode) {
      databaseThresholdConfig.setId(GeneratorUtil.generateId());
    }
    var databaseThresholdConfigEntity =
        databaseThresholdConfigRepository.save(databaseThresholdConfig);
    jobKafkaProducerService.notifyJobUpdate(KafkaTypeEnum.DATABASE_THRESHOLD,
          new KafkaJobModel<String>().configId(databaseThresholdConfig.getId()).type(
             databaseThresholdConfigEntity.getActive() ? KafkaJobTypeEnum.NEW_OR_UPDATE :
                          KafkaJobTypeEnum.DELETE).cronTime(databaseThresholdConfigEntity.getCronTime()));
    var res = DatabaseThresholdConfigResponseMapper.INSTANCE.map(
        databaseThresholdConfigEntity);
    sysLogKafkaProducerService.send(
            isCreateMode ? LogActionEnum.CREATE_DATABASE_THRESHOLD : LogActionEnum.EDIT_DATABASE_THRESHOLD,
            res.getName(), res);
    return res;
  }

  public void validateSaveRequest(DatabaseThresholdConfigRequest request)
      throws BusinessException {

    String id = request.getId();
    String name = StringUtils.capitalizeFirstLetter(request.getName());
    if (!isSelectQuery(request.getSqlCommand())) {
      throw new BusinessException(ErrorCode.DATABASE_THRESHOLD_SQL_COMMAND_INVALID);
    }
    var isCreate = KanbanCommonUtil.isEmpty(id);
    boolean isNameExists = isCreate ? existByName(name) : existByIdNotAndName(id, name);
    if (isNameExists) {
      throw new BusinessException(ErrorCode.DATABASE_THRESHOLD_CONFIG_NAME_IS_EXISTED);
    }
    var databaseConnection = databaseConnectionService.findById(request.getDatabaseConnectionId());
    if (Objects.isNull(databaseConnection)) {
      throw new BusinessException(ErrorCode.DATABASE_CONNECTION_NOT_FOUND);
    }
    if (!databaseConnection.getIsActive()) {
      throw new BusinessException(ErrorCode.DATABASE_CONNECTION_IS_INACTIVE);
    }
    var service = serviceService.findById(request.getServiceId());
    if (Objects.isNull(service)) {
      throw new BusinessException(ErrorCode.DATABASE_THRESHOLD_CONFIG_INVALID_SERVICE);
    }
    var application = applicationService.findById(request.getApplicationId());
    if (Objects.isNull(application)) {
      throw new BusinessException(ErrorCode. DATABASE_THRESHOLD_CONFIG_INVALID_APPLICATION);
    }
    var priority = alertPriorityConfigService.findById(request.getPriorityId());
    if (Objects.isNull(priority)) {
      throw new BusinessException(ErrorCode. DATABASE_THRESHOLD_CONFIG_INVALID_PRIORITY);
    }

  }

  @Override
  public boolean existByName(String name) {
    return databaseThresholdConfigRepository.existsByNameIgnoreCase(name);
  }

  @Override
  public boolean existByIdNotAndName(String id, String name) {
    return databaseThresholdConfigRepository.existsByIdNotAndNameIgnoreCase(id, name);
  }

  @Override
  public DatabaseThresholdConfigResponse findWithId(String id) {
    return databaseThresholdConfigRepository.findWithId(id);
  }

  @Override
  public Page<DatabaseThresholdConfigResponse> findAll(PaginationRequestDTO paginationRequest) {
    return databaseThresholdConfigRepository.findAll(paginationRequest);
  }

  @Override
  @Transactional
  public DatabaseThresholdConfigResponse updateStatus(String id) throws BusinessException {
    var configEntity = databaseThresholdConfigRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.DATABASE_THRESHOLD_CONFIG_NOT_FOUND));
    configEntity.setActive(!configEntity.getActive());
    var databaseThresholdConfig = databaseThresholdConfigRepository.save(configEntity);
    var res = DatabaseThresholdConfigResponseMapper.INSTANCE.map(
        databaseThresholdConfig);
    jobKafkaProducerService.notifyJobUpdate(KafkaTypeEnum.DATABASE_THRESHOLD,
        new KafkaJobModel<String>().cronTime(databaseThresholdConfig.getCronTime()).type(
           configEntity.getActive() ? KafkaJobTypeEnum.NEW_OR_UPDATE : KafkaJobTypeEnum.DELETE).configId(id));

    sysLogKafkaProducerService.send(
        configEntity.getActive() ? LogActionEnum.ACTIVE_DATABASE_THRESHOLD : LogActionEnum.INACTIVE_DATABASE_THRESHOLD,
        res.getName());
    return res;
  }

  @Override
  public List<DatabaseThresholdConfigResponse> findAllByDatabaseConnectionId(Long databaseConnectionId) {
    return DatabaseThresholdConfigResponseMapper.INSTANCE.map(
        databaseThresholdConfigRepository.findAllByDatabaseConnectionId(databaseConnectionId));
  }

  @Override
  public List<DatabaseThresholdConfigEntity> findAllByServiceId(String id) {
    return databaseThresholdConfigRepository.findAllByServiceId(id);
  }

  @Override
  public List<DatabaseThresholdConfigEntity> findAllByApplicationId(String id) {
    return databaseThresholdConfigRepository.findAllByApplicationId(id);
  }

  @Override
  public List<DatabaseThresholdConfigEntity> findAllByPriorityId(Long id) {
    return databaseThresholdConfigRepository.findAllByPriorityId(id);
  }


  @Override
  public void deleteWithId(String databaseConnectionId) throws BusinessException {
    var config = databaseThresholdConfigRepository.findById(databaseConnectionId)
        .orElseThrow(() -> new BusinessException(ErrorCode.DATABASE_THRESHOLD_CONFIG_NOT_FOUND));
    jobKafkaProducerService.notifyJobUpdate(KafkaTypeEnum.DATABASE_THRESHOLD,
        new KafkaJobModel<String>().type(
            KafkaJobTypeEnum.DELETE).configId(databaseConnectionId));
    delete(config);
    sysLogKafkaProducerService.send(LogActionEnum.DELETE_DATABASE_THRESHOLD, config.getName());
  }

  private boolean isSelectQuery(String sql) {
    try {
      Statement statement = CCJSqlParserUtil.parse(sql);
      return statement instanceof Select;
    } catch (JSQLParserException e) {
      return false;
    }
  }

}
