package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertRequestSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertRequestStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionOperatorEnum;

/**
 * Model request service to create or update alert request.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlertRequestRequest {
  String id;
  @NotNull
  AlertRequestSourceTypeEnum sourceType;
  @NotNull
  AlertRequestStatusEnum status;
  @NotNull
  String cronTime;
  @NotNull
  String serviceId;
  @NotNull
  String applicationId;
  @NotNull
  Long priorityId;
  @Size(min = 1, max = CommonConstants.COLLECT_EMAIL_CONFIG_RECIPIENT_ALERT_MAX_LENGTH)
  String recipient;
  @NotNull
  String content;
  @NotNull
  String contentJson;
  @NotNull
  ConditionOperatorEnum conditionOperator;
  @Min(CommonConstants.ALERT_REQUEST_OPERATOR_VALUE_MIN)
  @Max(CommonConstants.ALERT_REQUEST_OPERATOR_VALUE_MAX)
  Long conditionValue;
  String rejectedReason;
  AlertRequestDatabaseRequest alertRequestDatabase;
}