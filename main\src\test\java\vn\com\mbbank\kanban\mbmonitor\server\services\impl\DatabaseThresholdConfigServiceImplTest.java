package vn.com.mbbank.kanban.mbmonitor.server.services.impl;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.server.annotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.server.annotations.TestForUser;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseThresholdConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.JobKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.DatabaseThresholdConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.DatabaseThresholdConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.DatabaseThresholdConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.DatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;
import vn.com.mbbank.kanban.test.ApplicationTest;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DatabaseThresholdConfigServiceImplTest extends ApplicationTest {

  @Mock
  DatabaseThresholdConfigRepository databaseThresholdConfigRepository;
  @Mock
  DatabaseConnectionService databaseConnectionService;
  @Mock
  JobKafkaProducerService jobKafkaProducerService;
  @Mock
  SysLogKafkaProducerService sysLogKafkaProducerService;
  @Mock
  ServiceService serviceService;
  @Mock
  ApplicationService applicationService;
  @Mock
  AlertPriorityConfigService alertPriorityConfigService;
  @InjectMocks
  DatabaseThresholdConfigServiceImpl databaseThresholdConfigService;

  @Test
  void getRepository_success() {
    JpaCommonRepository<DatabaseThresholdConfigEntity, String> result =
        databaseThresholdConfigService.getRepository();
    assertEquals(databaseThresholdConfigRepository, result);
  }

  @TestForUser
  void existByName_success() {
    when(databaseThresholdConfigRepository.existsByNameIgnoreCase(any())).thenReturn(true);
    var res = databaseThresholdConfigService.existByName("1");
    verify(databaseThresholdConfigRepository, times(1)).existsByNameIgnoreCase(any());
    assertEquals(true, res);
  }

  @TestForUser
  void existByName_success_returnsFalse() {
    when(databaseThresholdConfigRepository.existsByNameIgnoreCase(any())).thenReturn(false);
    var res = databaseThresholdConfigService.existByName("1");
    verify(databaseThresholdConfigRepository, times(1)).existsByNameIgnoreCase(any());
    assertEquals(false, res);
  }

  @TestForUser
  void existByIdNotAndName_success() {
    when(databaseThresholdConfigRepository.existsByIdNotAndNameIgnoreCase(any(), any())).thenReturn(
        true);
    var res = databaseThresholdConfigService.existByIdNotAndName("1L", "1");
    verify(databaseThresholdConfigRepository, times(1)).existsByIdNotAndNameIgnoreCase(any(), any());
    assertEquals(true, res);
  }

  @TestForUser
  void existByIdNotAndName_success_returnsFalse() {
    when(databaseThresholdConfigRepository.existsByIdNotAndNameIgnoreCase(any(), any())).thenReturn(
        false);
    var res = databaseThresholdConfigService.existByIdNotAndName("1L", "1");
    verify(databaseThresholdConfigRepository, times(1)).existsByIdNotAndNameIgnoreCase(any(), any());
    assertEquals(false, res);
  }

  @TestForDev
  void findAll_success() {
    when(databaseThresholdConfigRepository.findAll(any(PaginationRequestDTO.class))).thenReturn(
        Page.empty());
    var res = databaseThresholdConfigService.findAll(new PaginationRequestDTO());
    assertNotNull(res);
  }

  @TestForDev
  void findWithId_error() {
    when(databaseThresholdConfigRepository.findWithId(any())).thenReturn(
        new DatabaseThresholdConfigResponse());
    var res =  databaseThresholdConfigService.findWithId("1L");
    assertNotNull(res);
  }

  @TestForDev
  void findWithId_success_returnsNull() {
    when(databaseThresholdConfigRepository.findWithId(any())).thenReturn(null);
    var res =  databaseThresholdConfigService.findWithId("1L");
    assertNull(res);
  }


  @Test
  void createOrUpdate_success() throws BusinessException {
    DatabaseThresholdConfigRequest databaseThresholdConfigRequest = new DatabaseThresholdConfigRequest();
    databaseThresholdConfigRequest.setName("adfa");
    databaseThresholdConfigRequest.setSqlCommand("select * from al");
    databaseThresholdConfigRequest.setServiceId("1");
    databaseThresholdConfigRequest.setApplicationId("1");
    databaseThresholdConfigRequest.setPriorityId(1L);
    var databaseConnection = new DatabaseConnectionEntity();
    databaseConnection.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnection);
    when(databaseThresholdConfigRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
    var config = new DatabaseThresholdConfigEntity();
    config.setActive(true);
    when(databaseThresholdConfigRepository.save(any())).thenReturn(config);
    var res = databaseThresholdConfigService.createOrUpdate(databaseThresholdConfigRequest);
    assertNotNull(res);
  }

  @Test
  void createOrUpdate_success_isCreate_logsSysLog() throws BusinessException {
    // Arrange
    DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
    request.setName("TestConfig");
    request.setSqlCommand("select * from test");
    request.setServiceId("1");
    request.setApplicationId("1");
    request.setPriorityId(1L);

    var databaseConnection = new DatabaseConnectionEntity();
    databaseConnection.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnection);
    when(databaseThresholdConfigRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());

    DatabaseThresholdConfigEntity savedEntity = new DatabaseThresholdConfigEntity();
    savedEntity.setId("generatedId");
    savedEntity.setName("TestConfig");
    savedEntity.setActive(true);
    when(databaseThresholdConfigRepository.save(any(DatabaseThresholdConfigEntity.class))).thenReturn(savedEntity);
    // Mock GeneratorUtil.generateId() if needed, or rely on the actual utility method
    // Mockito.mockStatic(GeneratorUtil.class).when(GeneratorUtil::generateId).thenReturn("generatedId");

    // Act
    databaseThresholdConfigService.createOrUpdate(request);

    // Assert
    verify(databaseThresholdConfigRepository, times(1)).save(any(DatabaseThresholdConfigEntity.class));
    verify(sysLogKafkaProducerService, times(1)).send(
        eq(LogActionEnum.CREATE_DATABASE_THRESHOLD),
        eq("TestConfig"), any()
    );
  }



  @Test
  void createOrUpdate_caseCannotSendKafka() throws BusinessException {
    var databaseConnection = new DatabaseConnectionEntity();
    databaseConnection.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnection);
    DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
    request.setName("adfa");
    request.setSqlCommand("select * from al");
    request.setServiceId("1");
    request.setApplicationId("1");
    request.setPriorityId(1L);
    when(databaseThresholdConfigRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
    var config = new DatabaseThresholdConfigEntity();
    config.setActive(true);
    when(databaseThresholdConfigRepository.save(any())).thenReturn(config);
    request.setName("");
    Mockito.doThrow(new BusinessException(ErrorCode.KAFKA_SEND_FAILED)).when(jobKafkaProducerService)
        .notifyJobUpdate(any(), any());
    assertThrows(BusinessException.class, () -> databaseThresholdConfigService.createOrUpdate(request));
  }

  @Test
  void updateStatus_success() throws BusinessException {
    var entity = new DatabaseThresholdConfigEntity();
    entity.setActive(true);
    when(databaseThresholdConfigRepository.findById(any())).thenReturn(
        Optional.of(entity));
    when(databaseThresholdConfigRepository.save(any())).thenReturn(new DatabaseThresholdConfigEntity());
    var res = databaseThresholdConfigService.updateStatus("1L");
    assertNotNull(res);
  }

  @Test
  void updateStatus_success_active_logsSysLog() throws BusinessException {
    // Arrange
    String configId = "testId";
    String configName = "TestConfig";
    var config = new DatabaseThresholdConfigEntity();
    config.setId(configId);
    config.setName(configName);
    config.setActive(false); // Initially inactive

    var updatedConfig = new DatabaseThresholdConfigEntity();
    updatedConfig.setId(configId);
    updatedConfig.setName(configName);
    updatedConfig.setActive(true); // Becomes active

    when(databaseThresholdConfigRepository.findById(eq(configId))).thenReturn(Optional.of(config));
    when(databaseThresholdConfigRepository.save(any(DatabaseThresholdConfigEntity.class))).thenReturn(updatedConfig);

    // Act
    databaseThresholdConfigService.updateStatus(configId);

    // Assert
    verify(databaseThresholdConfigRepository, times(1)).findById(configId);
    verify(databaseThresholdConfigRepository, times(1)).save(any(DatabaseThresholdConfigEntity.class));
    verify(sysLogKafkaProducerService, times(1)).send(
        eq(LogActionEnum.ACTIVE_DATABASE_THRESHOLD),
        eq(configName)
    );
  }

  @Test
  void updateStatus_success_inActive_logsSysLog() throws BusinessException {
    // Arrange
    String configId = "testId";
    String configName = "TestConfig";
    var config = new DatabaseThresholdConfigEntity();
    config.setId(configId);
    config.setName(configName);
    config.setActive(true); // Initially active

    var updatedConfig = new DatabaseThresholdConfigEntity();
    updatedConfig.setId(configId);
    updatedConfig.setName(configName);
    updatedConfig.setActive(false); // Becomes inactive

    when(databaseThresholdConfigRepository.findById(eq(configId))).thenReturn(Optional.of(config));
    when(databaseThresholdConfigRepository.save(any(DatabaseThresholdConfigEntity.class))).thenReturn(updatedConfig);

    // Act
    databaseThresholdConfigService.updateStatus(configId);

    // Assert
    verify(databaseThresholdConfigRepository, times(1)).findById(configId);
    verify(databaseThresholdConfigRepository, times(1)).save(any(DatabaseThresholdConfigEntity.class));
    verify(sysLogKafkaProducerService, times(1)).send(
        eq(LogActionEnum.INACTIVE_DATABASE_THRESHOLD),
        eq(configName)
    );
  }

  @Test
  void updateStatus_error_notFoundConfig() {
    when(databaseThresholdConfigRepository.findById(any())).thenReturn(Optional.empty());
    assertThrows(BusinessException.class, () -> {
      databaseThresholdConfigService.updateStatus("1L");
    }, ErrorCode.DATABASE_THRESHOLD_CONFIG_NOT_FOUND.getMessage());
  }




  @Test
  void createOrUpdate_success_idIsZero() throws BusinessException {
    var databaseConnection = new DatabaseConnectionEntity();
    databaseConnection.setIsActive(true);

    when(databaseConnectionService.findById(any())).thenReturn(databaseConnection);
    DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
    request.setName("");
    request.setId("0L");
    request.setServiceId("1");
    request.setSqlCommand("select * from al");
    request.setApplicationId("1");
    request.setPriorityId(1L);

    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
    var entity = new DatabaseThresholdConfigEntity();
    entity.setActive(false);
    when(databaseThresholdConfigRepository.save(any())).thenReturn(entity);
    var res = databaseThresholdConfigService.createOrUpdate(request);
    assertNotNull(res);
  }

  @Test
  void createOrUpdate_success_idIsNull() throws BusinessException {
    var databaseConnection = new DatabaseConnectionEntity();
    databaseConnection.setIsActive(true);

    when(databaseConnectionService.findById(any())).thenReturn(databaseConnection);
    DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
    request.setName("");
    request.setSqlCommand("select * from al");
    request.setId(null);
    request.setServiceId("1");
    request.setApplicationId("1");
    request.setPriorityId(1L);

    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
    var entity = new DatabaseThresholdConfigEntity();
    entity.setActive(true);
    when(databaseThresholdConfigRepository.save(any())).thenReturn(entity);
    var res = databaseThresholdConfigService.createOrUpdate(request);
    assertNotNull(res);
  }

  @Test
  void createOrUpdate_success_notEmailConfig() throws BusinessException {
    when(databaseConnectionService.findById(any())).thenReturn(null);
    var request = new DatabaseThresholdConfigRequest();
    request.setSqlCommand("select * from al");
    request.setName("");
    request.setId("0L");
    assertThrows(BusinessException.class, () -> {
      databaseThresholdConfigService.createOrUpdate(request);
    }, ErrorCode.EMAIL_CONFIG_NOT_FOUND.getMessage());
  }
  
    @Test
    void deleteWithId_success_logsSysLog() throws BusinessException {
      // Arrange
      String configId = "testId";
      String configName = "TestConfig";
      DatabaseThresholdConfigEntity configEntity = new DatabaseThresholdConfigEntity();
      configEntity.setId(configId);
      configEntity.setName(configName);
  
      when(databaseThresholdConfigRepository.findById(eq(configId))).thenReturn(Optional.of(configEntity));

      // Act
      databaseThresholdConfigService.deleteWithId(configId);
  
      // Assert
      verify(databaseThresholdConfigRepository, times(1)).findById(configId);
      verify(sysLogKafkaProducerService, times(1)).send(eq(LogActionEnum.DELETE_DATABASE_THRESHOLD), eq("TestConfig"));
    }
  
    @Test
    void createOrUpdate_success_isUpdate_logsSysLog() throws BusinessException {
      // Arrange
      String configId = "existingId";
      String oldName = "OldConfig";
      String newName = "NewConfig";
      DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
      request.setId(configId);
      request.setName(newName);
      request.setSqlCommand("select * from updated");
      request.setServiceId("1");
      request.setApplicationId("1");
      request.setPriorityId(1L);
      request.setDatabaseConnectionId(1L);
  
      var databaseConnection = new DatabaseConnectionEntity();
      databaseConnection.setIsActive(true);
      when(databaseConnectionService.findById(any())).thenReturn(databaseConnection);
      when(databaseThresholdConfigRepository.existsByIdNotAndNameIgnoreCase(eq(configId), eq(newName))).thenReturn(false);
      when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
      when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
      when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
  
      DatabaseThresholdConfigEntity existingEntity = new DatabaseThresholdConfigEntity();
      existingEntity.setId(configId);
      existingEntity.setName(oldName);
  
      DatabaseThresholdConfigEntity updatedEntity = new DatabaseThresholdConfigEntity();
      updatedEntity.setId(configId);
      updatedEntity.setName(newName);
      updatedEntity.setActive(true);
      when(databaseThresholdConfigRepository.save(any())).thenReturn(updatedEntity);

      // Act
      databaseThresholdConfigService.createOrUpdate(request);
  
      // Assert
      verify(databaseThresholdConfigRepository, times(1)).save(any(DatabaseThresholdConfigEntity.class));
      verify(sysLogKafkaProducerService, times(1)).send(
          eq(LogActionEnum.EDIT_DATABASE_THRESHOLD),
          eq(newName), any()
      );
    }
  

    @TestForDev
    void findAllByPriorityId_success() {
      // Arrange
      Long priorityId = 123L;
      List<DatabaseThresholdConfigEntity> expectedConfigs = Arrays.asList(new DatabaseThresholdConfigEntity(), new DatabaseThresholdConfigEntity());
      when(databaseThresholdConfigRepository.findAllByPriorityId(priorityId)).thenReturn(expectedConfigs);
  
      // Act
      List<DatabaseThresholdConfigEntity> actualConfigs = databaseThresholdConfigService.findAllByPriorityId(priorityId);
  
      // Assert
      assertEquals(expectedConfigs, actualConfigs);
      verify(databaseThresholdConfigRepository, times(1)).findAllByPriorityId(priorityId);
    }
  
    @TestForUser
    void validateSaveRequest_success() throws BusinessException {
      // Arrange
      DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
      request.setName("TestConfig");
      request.setSqlCommand("select * from test");
      request.setServiceId("1");
      request.setApplicationId("1");
      request.setPriorityId(1L);
      request.setDatabaseConnectionId(1L);
  
      var databaseConnection = new DatabaseConnectionEntity();
      databaseConnection.setIsActive(true);
      when(databaseConnectionService.findById(any())).thenReturn(databaseConnection);
      when(databaseThresholdConfigRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
      when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
      when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
      when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
  
      // Act & Assert
      assertDoesNotThrow(() -> databaseThresholdConfigService.validateSaveRequest(request));
    }


  @Test
  void validateSaveCustomObjectRequest_error_collectEmailConfigNotFound() {
    DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
    request.setId("1L");
    request.setSqlCommand("select * from al");
    request.setName(" 2");
    assertThrows(BusinessException.class, () -> {
      databaseThresholdConfigService.validateSaveRequest(request);
    }, vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode.DATABASE_THRESHOLD_CONFIG_NAME_IS_EXISTED.getMessage());
  }

  @Test
  void validateSaveCustomObjectRequest_error_collectEmailConfigNameInactive() {
    DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
    request.setSqlCommand("select * from al");
    request.setName("");
    request.setId("2L");
    request.setDatabaseConnectionId(1L);
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    assertThrows(BusinessException.class, () -> {
      databaseThresholdConfigService.validateSaveRequest(request);
    }, ErrorCode.DATABASE_CONNECTION_NOT_FOUND.getMessage());
  }

  @TestForUser
  void validateSaveCustomObjectRequest_error_nameOfConfigIsExists() {
    DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
    request.setName("");
    request.setSqlCommand("select * from al");
    request.setId("2L");
    request.setDatabaseConnectionId(1L);
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    when(databaseThresholdConfigRepository.existsByIdNotAndNameIgnoreCase(any(),any())).thenReturn(true);
    assertThrows(BusinessException.class, () -> {
      databaseThresholdConfigService.validateSaveRequest(request);
    }, ErrorCode.DATABASE_THRESHOLD_CONFIG_NAME_IS_EXISTED.getMessage());
  }
  @TestForUser
  void validateSaveRequest_error_databaseConnectionIsInActive() {
    DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
    request.setName("");
    request.setSqlCommand("select * from al");
    request.setId("2L");
    request.setDatabaseConnectionId(1L);
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    var connection = new DatabaseConnectionEntity();
    connection.setIsActive(false);
    when(databaseConnectionService.findById(any())).thenReturn(connection);
    assertThrows(BusinessException.class, () -> {
      databaseThresholdConfigService.validateSaveRequest(request);
    }, ErrorCode.DATABASE_CONNECTION_IS_INACTIVE.getMessage());
  }
  @TestForUser
  void validateSaveRequest_error_serviceIsNull() {
    DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
    request.setName("");
    request.setId("2L");
    request.setSqlCommand("select * from al");
    var connection = new DatabaseConnectionEntity();
    connection.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(connection);
    request.setDatabaseConnectionId(1L);
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    assertThrows(BusinessException.class, () -> {
      databaseThresholdConfigService.validateSaveRequest(request);
    }, ErrorCode.DATABASE_THRESHOLD_CONFIG_INVALID_SERVICE.getMessage());
  }
  @TestForUser
  void validateSaveRequest_error_applicationIsNull() {
    DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
    request.setName("");
    request.setId("2L");
    request.setSqlCommand("select * from al");
    var connection = new DatabaseConnectionEntity();
    connection.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(connection);
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());
    request.setDatabaseConnectionId(1L);
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    assertThrows(BusinessException.class, () -> {
      databaseThresholdConfigService.validateSaveRequest(request);
    }, ErrorCode.DATABASE_THRESHOLD_CONFIG_INVALID_APPLICATION.getMessage());
  }
  @TestForUser
  void validateSaveRequest_error_sqlCommand() {
    DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
    request.setName("");
    request.setId("2L");
    request.setSqlCommand("1232132");
    assertThrows(BusinessException.class, () -> {
      databaseThresholdConfigService.validateSaveRequest(request);
    }, ErrorCode.DATABASE_THRESHOLD_CONFIG_INVALID_APPLICATION.getMessage());
  }
  @TestForUser
  void validateSaveRequest_error_priorityConfigIsNull() {
    DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
    request.setName("");
    request.setId("2L");
    request.setSqlCommand("select * from al");
    request.setDatabaseConnectionId(1L);
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    var connection = new DatabaseConnectionEntity();
    connection.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(connection);
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    assertThrows(BusinessException.class, () -> {
      databaseThresholdConfigService.validateSaveRequest(request);
    }, ErrorCode.DATABASE_THRESHOLD_CONFIG_INVALID_PRIORITY.getMessage());
  }
  @TestForUser
  void deleteWithId_success() throws BusinessException {
    // Arrange
    String configId = "testId";
    String configName = "TestConfig";
    DatabaseThresholdConfigEntity configEntity = new DatabaseThresholdConfigEntity();
    configEntity.setId(configId);
    configEntity.setName(configName);

    when(databaseThresholdConfigRepository.findById(eq(configId))).thenReturn(Optional.of(configEntity));
    // Act
    databaseThresholdConfigService.deleteWithId(configId);

    // Assert
    verify(databaseThresholdConfigRepository, times(1)).findById(configId);
    verify(sysLogKafkaProducerService, times(1)).send(LogActionEnum.DELETE_DATABASE_THRESHOLD, configName);
  }
  @TestForDev
  void findAllByDatabaseConnectionId_success(){
    when(databaseThresholdConfigRepository.findAllByDatabaseConnectionId(anyLong())).thenReturn(List.of());
    assertEquals(0, databaseThresholdConfigService.findAllByDatabaseConnectionId(1L).size());
  }

  @TestForUser
  void deleteWithId_failed_caseNotFound() throws BusinessException {
    when(databaseThresholdConfigRepository.findById(any())).thenReturn(Optional.empty());
    assertThrows(BusinessException.class, () -> databaseThresholdConfigService.deleteWithId("1L"));
  }
  @TestForUser
  void isSelectQuery_InvalidSQL_ShouldReturnFalse() {
    String invalidSQL = "INVALID SQL QUERY";
    boolean result =
        (boolean) ReflectionTestUtils.invokeMethod(databaseThresholdConfigService, "isSelectQuery", invalidSQL);
    assertFalse(result);
  }

  @TestForUser
  void isSelectQuery_ValidSQL_ShouldReturnTrue() {
    String validSQL = "SELECT * FROM my_table";
    boolean result =
        (boolean) ReflectionTestUtils.invokeMethod(databaseThresholdConfigService, "isSelectQuery", validSQL);
    assertTrue(result);
  }

  @TestForDev
  void findAllByServiceId_success() {
    when(databaseThresholdConfigRepository.findAllByServiceId(any())).thenReturn(List.of(new DatabaseThresholdConfigEntity()));
    var res = databaseThresholdConfigService.findAllByServiceId("1L");
    verify(databaseThresholdConfigRepository).findAllByServiceId(any());
    assertEquals(1, res.size());
  }
  @TestForDev
  void findAllByApplicationId_success() {
    when(databaseThresholdConfigRepository.findAllByApplicationId(any())).thenReturn(List.of(new DatabaseThresholdConfigEntity()));
    var res = databaseThresholdConfigService.findAllByApplicationId("1L");
    verify(databaseThresholdConfigRepository).findAllByApplicationId(any());
    assertEquals(1, res.size());
  }
  @TestForDev
  void findAllByPriorityConfigId_success() {
    when(databaseThresholdConfigRepository.findAllByPriorityId(any())).thenReturn(List.of(new DatabaseThresholdConfigEntity()));
    var res = databaseThresholdConfigService.findAllByPriorityId(1L);
    verify(databaseThresholdConfigRepository).findAllByPriorityId(any());
    assertEquals(1, res.size());
  }
}
