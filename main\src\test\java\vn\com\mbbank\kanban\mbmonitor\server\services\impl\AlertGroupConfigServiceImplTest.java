package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Optional;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigConditionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupOutputEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.AlertGroupConditionDependenciesModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertGroupConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertGroupConfigUpdatePositionRequest;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AlertGroupConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertGroupConfigConditionService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertGroupConfigDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ConditionMapService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;

@ExtendWith({MockitoExtension.class})
class AlertGroupConfigServiceImplTest {
  @Mock
  AlertGroupConfigRepository alertGroupConfigRepository;
  @Mock
  AlertGroupConfigConditionService alertGroupConfigConditionService;
  @Mock
  ServiceService serviceService;
  @Mock
  ApplicationService applicationService;
  @Mock
  SysLogKafkaProducerService sysLogKafkaProducerService;

  @Mock
  AlertGroupConfigDependencyService alertGroupConfigDependencyService;
  @Mock
  ConditionMapService conditionMapService;
  @Mock
  ObjectMapper objectMapper;
  @InjectMocks
  @Spy
  AlertGroupConfigServiceImpl alertGroupConfigServiceImpl;

  @Test
  void getRepository_success() {
    BaseSoftRepository<AlertGroupConfigEntity, Long> result =
        alertGroupConfigServiceImpl.getRepository();
    Assertions.assertEquals(alertGroupConfigRepository, result);
  }

  @Test
  void findAllWithDeleted_success_caseWithDeleted() {
    Mockito.when(
            alertGroupConfigRepository.findAllByDeletedAndSearch(Mockito.isNull(), anyString()))
        .thenReturn(List.of());
    var res = alertGroupConfigServiceImpl.findAllWithDeletedAndSearch(true, "");
    Assertions.assertEquals(0, res.size());
  }

  @Test
  void findAllWithDeleted_success_caseWithoutDeleted() {
    Mockito.when(
            alertGroupConfigRepository.findAllByDeletedAndSearch(Mockito.anyBoolean(), anyString()))
        .thenReturn(List.of());
    var res = alertGroupConfigServiceImpl.findAllWithDeletedAndSearch(false, "");
    Assertions.assertEquals(0, res.size());
  }

  @Test
  void save_failed_updateNotFount() {
    var request = new AlertGroupConfigRequest();
    request.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    var ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.OR);
    request.setName("abc");
    request.setRuleGroups(List.of(ruleGroupType));
    request.setApplicationIds(List.of("1"));
    request.setServiceIds(List.of("2"));
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    request.setId(1L);
    Mockito.when(alertGroupConfigRepository.findById(Mockito.anyLong()))
        .thenReturn(Optional.empty());
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.save(request));
  }

  @Test
  void save_success_caseCreateMode() throws BusinessException {
    var request = new AlertGroupConfigRequest();
    request.setName("123");
    request.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    var ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.OR);
    ruleGroupType.setRules(List.of());
    request.setRuleGroups(List.of(ruleGroupType));
    request.setApplicationIds(List.of("1"));
    request.setServiceIds(List.of("2"));
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    Mockito.when(alertGroupConfigRepository.getNextPositionValue()).thenReturn(1);
    when(serviceService.findAllByIdInAndDeleted(any(), anyBoolean()))
        .thenReturn(List.of(ServiceEntity.builder().id("1L").build()));
    var res = alertGroupConfigServiceImpl.save(request);
    Assertions.assertNotNull(res);
  }

  @Test
  void save_success_caseUpdate() throws BusinessException {
    var request = new AlertGroupConfigRequest();
    request.setName("123");
    request.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    request.setApplicationIds(List.of("1"));
    var ruleGroupType = new RuleGroupType();
    request.setId(1L);
    ruleGroupType.setCombinator(ConditionCombinatorEnum.OR);
    ruleGroupType.setRules(List.of());
    request.setRuleGroups(List.of(ruleGroupType));
    request.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var entity = new AlertGroupConfigEntity();
    entity.setId(1L);
    entity.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    entity.setActive(true);
    var alertGroupConfigCondition = new AlertGroupConfigConditionEntity();
    alertGroupConfigCondition.setRuleGroup(ruleGroupType);
    var application = new ApplicationEntity();
    application.setServiceId("1");
    application.setId("1");
    var service = new ServiceEntity();
    service.setId("1");
    Mockito.when(alertGroupConfigRepository.findById(Mockito.anyLong()))
        .thenReturn(Optional.of(entity));
    Mockito.when(alertGroupConfigDependencyService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of());
    when(serviceService.findAllByIdInAndDeleted(any(), anyBoolean()))
        .thenReturn(List.of(ServiceEntity.builder().id("1L").build()));
    var res = alertGroupConfigServiceImpl.save(request);
    Assertions.assertNotNull(res);
  }

  @Test
  void save_success_caseUpdateToOldConfig() throws BusinessException {
    var request = new AlertGroupConfigRequest();
    request.setName("123");
    request.setId(1L);
    request.setServiceIds(List.of("1", "2"));
    var ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.OR);
    ruleGroupType.setRules(List.of());
    request.setRuleGroups(List.of(ruleGroupType));
    request.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var entity = new AlertGroupConfigEntity();
    entity.setId(1L);
    entity.setId(1L);
    entity.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    entity.setActive(true);
    var alertGroupConfigCondition = new AlertGroupConfigConditionEntity();
    alertGroupConfigCondition.setRuleGroup(ruleGroupType);
    Mockito.when(alertGroupConfigRepository.findById(anyLong())).thenReturn(Optional.of(entity));
    Mockito.when(alertGroupConfigConditionService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of(alertGroupConfigCondition));
    var application = new ApplicationEntity();
    application.setServiceId("1");
    application.setId("1");

    var service = new ServiceEntity();
    service.setId("1");
    var service2 = new ServiceEntity();
    service2.setId("2");

    var dependency = new AlertGroupConfigDependencyEntity();
    dependency.setId(1L);
    dependency.setDependencyId("1");
    dependency.setType(AlertGroupConfigDependencyTypeEnum.SERVICE);
    var dependency2 = new AlertGroupConfigDependencyEntity();
    dependency2.setId(2L);
    dependency2.setDependencyId("2");
    dependency2.setType(AlertGroupConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);

    var condition = new AlertGroupConfigConditionEntity();
    condition.setRuleGroup(ruleGroupType);
    condition.setId(1L);
    condition.setAlertGroupConfigId(1L);
    Mockito.when(alertGroupConfigDependencyService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of(dependency, dependency2));
    Mockito.when(alertGroupConfigConditionService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of(condition));
    Mockito.when(applicationService.findAllByIdInAndServiceIdInAndDeleted(anyList(), anyList(), anyBoolean()))
        .thenReturn(List.of(application));
    Mockito.when(serviceService.findAllByIdInAndDeleted(anyList(), anyBoolean()))
        .thenReturn(List.of(service, service2));
    var res = alertGroupConfigServiceImpl.save(request);
    Assertions.assertNotNull(res);

  }


  @Test
  void save_success_caseNeedCreate() throws BusinessException {
    var request = new AlertGroupConfigRequest();
    request.setName("123");
    request.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    var ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.OR);
    request.setRuleGroups(List.of(ruleGroupType));
    request.setApplicationIds(List.of("1"));
    request.setServiceIds(List.of("2"));
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    request.setId(1L);
    var entity = new AlertGroupConfigEntity();
    entity.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    entity.setActive(true);
    Mockito.when(alertGroupConfigRepository.findById(Mockito.anyLong()))
        .thenReturn(Optional.of(entity));
    when(serviceService.findAllByIdInAndDeleted(any(), anyBoolean()))
        .thenReturn(List.of(ServiceEntity.builder().id("1L").build()));
    var res = alertGroupConfigServiceImpl.save(request);
    Assertions.assertNotNull(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseTypeNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    var entity = new AlertGroupConfigEntity();
    entity.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseCustomServiceIdNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    request.setCustomServiceId("1L");
    var entity = new AlertGroupConfigEntity();
    entity.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    request.setCustomServiceId("2L");
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseCustomApplicationIdNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    request.setCustomServiceId("1L");
    request.setCustomApplicationId("1L");
    var entity = new AlertGroupConfigEntity();
    entity.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    entity.setCustomServiceId("1L");
    entity.setCustomApplicationId("2L");
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseCustomRecipientNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    request.setCustomServiceId("1L");
    request.setCustomApplicationId("1L");
    request.setCustomRecipient("1L");
    var entity = new AlertGroupConfigEntity();
    entity.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    entity.setCustomServiceId("1L");
    entity.setCustomApplicationId("1L");
    entity.setCustomRecipient("2L");
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseCustomContentNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    request.setCustomServiceId("1L");
    request.setCustomApplicationId("1L");
    request.setCustomRecipient("1L");
    request.setCustomContent("1L");
    var entity = new AlertGroupConfigEntity();
    entity.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    entity.setCustomServiceId("1L");
    entity.setCustomApplicationId("1L");
    entity.setCustomRecipient("1L");
    entity.setCustomContent("2l");
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseCustomPriorityConfigNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    request.setCustomServiceId("1L");
    request.setCustomApplicationId("1L");
    request.setCustomRecipient("1L");
    request.setCustomContent("1L");
    request.setCustomPriorityConfigId(1L);
    var entity = new AlertGroupConfigEntity();
    entity.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    entity.setCustomServiceId("1L");
    entity.setCustomApplicationId("1L");
    entity.setCustomRecipient("1L");
    entity.setCustomContent("1L");
    request.setCustomPriorityConfigId(2L);
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }


  @Test
  public void isNeedToCreateNewConfig_success_caseAlertOutputNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var entity = new AlertGroupConfigEntity();
    entity.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    entity.setAlertOutput(AlertGroupOutputEnum.HIGHEST_PRIORITY);
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseServiceIdSizeNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setServiceIds(List.of("1"));
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var entity = new AlertGroupConfigEntity();
    entity.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseServiceIdsNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setServiceIds(List.of("2"));
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var entity = new AlertGroupConfigEntity();
    entity.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseApplicationIdSizeNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setApplicationIds(List.of("1"));
    request.setServiceIds(List.of("2"));
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var entity = new AlertGroupConfigEntity();
    entity.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseApplicationIdNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setApplicationIds(List.of("2"));
    request.setServiceIds(List.of("2"));
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var entity = new AlertGroupConfigEntity();
    entity.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseSameObjectValueConditionSizeNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setCustomObjectIds(List.of());
    var entity = new AlertGroupConfigEntity();
    entity.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    entity.setId(1L);
    Mockito.when(alertGroupConfigConditionService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of(new AlertGroupConfigConditionEntity()));
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseSameObjectValueConditionNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setCustomObjectIds(List.of(1L));
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var entity = new AlertGroupConfigEntity();
    entity.setId(1L);
    entity.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var alertGroupConfigCondition = new AlertGroupConfigConditionEntity();
    alertGroupConfigCondition.setCustomObjectId(2L);
    Mockito.when(alertGroupConfigConditionService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of(alertGroupConfigCondition));
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseMultipleConditionSizeNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    request.setRuleGroups(List.of());
    request.setCustomObjectIds(List.of(1L));
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var entity = new AlertGroupConfigEntity();
    entity.setId(1L);
    entity.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    Mockito.when(alertGroupConfigConditionService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of(new AlertGroupConfigConditionEntity()));
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseMultipleObjectValueConditionNotEqual() {
    var request = new AlertGroupConfigRequest();
    request.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    var ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.AND);
    request.setRuleGroups(List.of(ruleGroupType));
    request.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    request.setRuleGroups(List.of());
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var entity = new AlertGroupConfigEntity();
    entity.setId(1L);
    entity.setId(1L);
    entity.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var ruleGroupType1 = new RuleGroupType();
    ruleGroupType1.setCombinator(ConditionCombinatorEnum.OR);
    var alertGroupConfigCondition = new AlertGroupConfigConditionEntity();
    alertGroupConfigCondition.setRuleGroup(ruleGroupType1);
    Mockito.when(alertGroupConfigConditionService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of(alertGroupConfigCondition));
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertTrue(res);
  }

  @Test
  public void isNeedToCreateNewConfig_success_caseReturnFalse() {
    var request = new AlertGroupConfigRequest();
    request.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    var ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.OR);
    request.setRuleGroups(List.of(ruleGroupType));
    request.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    request.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var entity = new AlertGroupConfigEntity();
    entity.setId(1L);
    entity.setId(1L);
    entity.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    entity.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var alertGroupConfigCondition = new AlertGroupConfigConditionEntity();
    alertGroupConfigCondition.setRuleGroup(ruleGroupType);
    Mockito.when(alertGroupConfigConditionService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of(alertGroupConfigCondition));
    var res =
        ReflectionTestUtils.<Boolean>invokeMethod(alertGroupConfigServiceImpl,
            "isNeedToCreateNewConfig", request,
            entity, List.of());
    Assertions.assertFalse(res);
  }


  @Test
  void validateAlertGroupRequest_failed_caseExisted() {
    var request = new AlertGroupConfigRequest();
    request.setId(1L);
    request.setName("abc");
    Mockito.when(alertGroupConfigRepository.existsByIdNotAndNameAndDeleted(Mockito.anyLong(),
        Mockito.anyString(),
        Mockito.anyBoolean())).thenReturn(true);
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.validateAlertGroupRequest(request, List.of()));
  }

  @Test
  void validateAlertGroupRequest_failed_caseCreateExisted() {
    var request = new AlertGroupConfigRequest();
    request.setName("abc");
    Mockito.when(alertGroupConfigRepository.existsByNameAndDeleted(Mockito.anyString(),
        Mockito.anyBoolean())).thenReturn(true);
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.validateAlertGroupRequest(request, List.of()));
  }

  @Test
  void validateAlertGroupRequest_failed_caseRuleTypeEmpty() {
    var request = new AlertGroupConfigRequest();
    request.setId(1L);
    request.setName("abc");
    request.setType(AlertGroupConfigTypeEnum.MULTIPLE_CONDITION);
    request.setRuleGroups(List.of());
    Mockito.when(alertGroupConfigRepository.existsByIdNotAndNameAndDeleted(Mockito.anyLong(),
        Mockito.anyString(),
        Mockito.anyBoolean())).thenReturn(false);
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.validateAlertGroupRequest(request, List.of()));
  }

  @Test
  void validateAlertGroupRequest_failed_caseCustomObjectEmpty() {
    var request = new AlertGroupConfigRequest();
    request.setId(1L);
    request.setName("abc");
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setRuleGroups(List.of(new RuleGroupType()));
    request.setCustomObjectIds(List.of());
    request.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    Mockito.when(alertGroupConfigRepository.existsByIdNotAndNameAndDeleted(Mockito.anyLong(),
        Mockito.anyString(),
        Mockito.anyBoolean())).thenReturn(false);
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.validateAlertGroupRequest(request, List.of()));
  }

  @Test
  void validateAlertGroupRequest_failed_caseCustomServiceIdNull() {
    var request = new AlertGroupConfigRequest();
    request.setId(1L);
    request.setName("abc");
    request.setRuleGroups(List.of(new RuleGroupType()));
    request.setCustomObjectIds(List.of(1L));
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    Mockito.when(alertGroupConfigRepository.existsByIdNotAndNameAndDeleted(Mockito.anyLong(),
        Mockito.anyString(),
        Mockito.anyBoolean())).thenReturn(false);
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.validateAlertGroupRequest(request, List.of(new CustomObjectEntity())));
  }

  @Test
  void validateAlertGroupRequest_failed_caseCustomApplicationIdNull() {
    var request = new AlertGroupConfigRequest();
    request.setId(1L);
    request.setName("abc");
    request.setRuleGroups(List.of(new RuleGroupType()));
    request.setCustomObjectIds(List.of(1L));
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    request.setCustomServiceId("1L");
    Mockito.when(alertGroupConfigRepository.existsByIdNotAndNameAndDeleted(Mockito.anyLong(),
        Mockito.anyString(),
        Mockito.anyBoolean())).thenReturn(false);
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.validateAlertGroupRequest(request, List.of(new CustomObjectEntity())));
  }

  @Test
  void validateAlertGroupRequest_failed_caseCustomContentNull() {
    var request = new AlertGroupConfigRequest();
    request.setId(1L);
    request.setName("abc");
    request.setRuleGroups(List.of(new RuleGroupType()));
    request.setCustomObjectIds(List.of(1L));
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    request.setCustomServiceId("1L");
    request.setCustomApplicationId("1L");
    Mockito.when(alertGroupConfigRepository.existsByIdNotAndNameAndDeleted(Mockito.anyLong(),
        Mockito.anyString(),
        Mockito.anyBoolean())).thenReturn(false);
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.validateAlertGroupRequest(request, List.of(new CustomObjectEntity())));
  }

  @Test
  void validateAlertGroupRequest_failed_caseCustomRecipientNull() {
    var request = new AlertGroupConfigRequest();
    request.setId(1L);
    request.setName("abc");
    request.setRuleGroups(List.of(new RuleGroupType()));
    request.setCustomObjectIds(List.of(1L));
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    request.setCustomServiceId("1L");
    request.setCustomApplicationId("1L");
    request.setCustomContent("abc");
    Mockito.when(alertGroupConfigRepository.existsByIdNotAndNameAndDeleted(Mockito.anyLong(),
        Mockito.anyString(),
        Mockito.anyBoolean())).thenReturn(false);
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.validateAlertGroupRequest(request, List.of(new CustomObjectEntity())));
  }

  @Test
  void validateAlertGroupRequest_failed_caseCustomPriorityConfigNull() {
    var request = new AlertGroupConfigRequest();
    request.setId(1L);
    request.setName("abc");
    request.setRuleGroups(List.of(new RuleGroupType()));
    request.setCustomObjectIds(List.of(1L));
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    request.setCustomServiceId("1L");
    request.setCustomApplicationId("1L");
    request.setCustomContent("abc");
    request.setCustomRecipient("abc");
    Mockito.when(alertGroupConfigRepository.existsByIdNotAndNameAndDeleted(Mockito.anyLong(),
        Mockito.anyString(),
        Mockito.anyBoolean())).thenReturn(false);
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.validateAlertGroupRequest(request, List.of(new CustomObjectEntity())));
  }

  @Test
  void validateAlertGroupRequest_failed_caseNotFoundService() {
    var request = new AlertGroupConfigRequest();
    request.setId(1L);
    request.setName("abc");
    request.setRuleGroups(List.of(new RuleGroupType()));
    request.setCustomObjectIds(List.of(1L));
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    request.setCustomServiceId("1L");
    request.setCustomApplicationId("1L");
    request.setCustomContent("abc");
    request.setCustomRecipient("abc");
    request.setCustomPriorityConfigId(1L);
    Mockito.when(alertGroupConfigRepository.existsByIdNotAndNameAndDeleted(Mockito.anyLong(),
        Mockito.anyString(),
        Mockito.anyBoolean())).thenReturn(false);

    Mockito.when(serviceService.findById(anyString())).thenReturn(null);
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.validateAlertGroupRequest(request, List.of(new CustomObjectEntity())));
  }

  @Test
  void validateAlertGroupRequest_failed_caseServiceDeleted() {
    var request = new AlertGroupConfigRequest();
    request.setId(1L);
    request.setName("abc");
    request.setRuleGroups(List.of(new RuleGroupType()));
    request.setCustomObjectIds(List.of(1L));
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    request.setCustomServiceId("1L");
    request.setCustomApplicationId("1L");
    request.setCustomContent("abc");
    request.setCustomRecipient("abc");
    request.setCustomPriorityConfigId(1L);
    Mockito.when(alertGroupConfigRepository.existsByIdNotAndNameAndDeleted(Mockito.anyLong(),
        Mockito.anyString(),
        Mockito.anyBoolean())).thenReturn(false);

    var service = new ServiceEntity();
    service.setDeleted(true);
    Mockito.when(serviceService.findById(anyString())).thenReturn(service);
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.validateAlertGroupRequest(request, List.of(new CustomObjectEntity())));
  }

  @Test
  void validateAlertGroupRequest_failed_caseNotFoundApplication() {
    var request = new AlertGroupConfigRequest();
    request.setId(1L);
    request.setName("abc");
    request.setRuleGroups(List.of(new RuleGroupType()));
    request.setCustomObjectIds(List.of(1L));
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    request.setCustomServiceId("1L");
    request.setCustomApplicationId("1L");
    request.setCustomContent("abc");
    request.setCustomRecipient("abc");
    request.setCustomPriorityConfigId(1L);
    Mockito.when(alertGroupConfigRepository.existsByIdNotAndNameAndDeleted(Mockito.anyLong(),
        Mockito.anyString(),
        Mockito.anyBoolean())).thenReturn(false);

    var service = new ServiceEntity();
    service.setDeleted(false);
    Mockito.when(serviceService.findById(anyString())).thenReturn(service);
    Mockito.when(applicationService.findById(anyString())).thenReturn(null);
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.validateAlertGroupRequest(request, List.of(new CustomObjectEntity())));
  }

  @Test
  void validateAlertGroupRequest_failed_caseApplicationDeleted() {
    var request = new AlertGroupConfigRequest();
    request.setId(1L);
    request.setName("abc");
    request.setRuleGroups(List.of(new RuleGroupType()));
    request.setCustomObjectIds(List.of(1L));
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    request.setCustomServiceId("1L");
    request.setCustomApplicationId("1L");
    request.setCustomContent("abc");
    request.setCustomRecipient("abc");
    request.setCustomPriorityConfigId(1L);
    Mockito.when(alertGroupConfigRepository.existsByIdNotAndNameAndDeleted(Mockito.anyLong(),
        Mockito.anyString(),
        Mockito.anyBoolean())).thenReturn(false);

    var service = new ServiceEntity();
    service.setDeleted(false);
    var app = new ApplicationEntity();
    app.setDeleted(true);
    Mockito.when(serviceService.findById(anyString())).thenReturn(service);
    Mockito.when(applicationService.findById(anyString())).thenReturn(app);
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.validateAlertGroupRequest(request, List.of(new CustomObjectEntity())));
  }

  @Test
  void validateAlertGroupRequest_failed_caseAlertOutputCustomInfoEmpty() {
    var request = new AlertGroupConfigRequest();
    request.setId(1L);
    request.setName("abc");
    request.setType(AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE);
    request.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    request.setCustomObjectIds(List.of());
    Mockito.when(alertGroupConfigRepository.existsByIdNotAndNameAndDeleted(Mockito.anyLong(),
        Mockito.anyString(),
        Mockito.anyBoolean())).thenReturn(false);
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.validateAlertGroupRequest(request, List.of(new CustomObjectEntity())));
  }

  @Test
  void findByIdWithDetail_false() {
    Mockito.when(alertGroupConfigRepository.findById(Mockito.anyLong()))
        .thenReturn(Optional.empty());
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.findByIdWithDetail(1L));
  }

  @ParameterizedTest
  @ValueSource(strings = {"1"})
  @NullSource
  void findByIdWithDetail_success(String id) throws BusinessException {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    alertGroupConfig.setCustomApplicationId("a");
    alertGroupConfig.setCustomServiceId("s");
    Mockito.when(alertGroupConfigRepository.findById(Mockito.anyLong()))
        .thenReturn(Optional.of(alertGroupConfig));
    Mockito.when(alertGroupConfigConditionService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of());
    if (id != null) {
      var dependency = new AlertGroupConfigDependencyEntity();
      dependency.setDependencyId("1");
      dependency.setType(AlertGroupConfigDependencyTypeEnum.SERVICE);
      dependency.setId(1L);
      var dependency1 = new AlertGroupConfigDependencyEntity();
      dependency1.setDependencyId("2");
      dependency1.setType(AlertGroupConfigDependencyTypeEnum.APPLICATION);
      dependency1.setId(2L);

      Mockito.when(serviceService.findAllByIdIn(Mockito.anyList())).thenReturn(List.of());
      Mockito.when(applicationService.findAllByIdIn(Mockito.anyList())).thenReturn(List.of());
      Mockito.when(alertGroupConfigDependencyService.findAllByAlertGroupConfigId(Mockito.anyLong()))
          .thenReturn(List.of(dependency, dependency1));
    }
    var res = alertGroupConfigServiceImpl.findByIdWithDetail(1L);
    Assertions.assertNotNull(res);
  }

  @ParameterizedTest
  @ValueSource(strings = {"1"})
  @NullSource
  void findByIdWithDetail_success_caseLastedAlertOutput(String id) throws BusinessException {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    alertGroupConfig.setCustomApplicationId("a");
    alertGroupConfig.setCustomServiceId("s");
    Mockito.when(alertGroupConfigRepository.findById(Mockito.anyLong()))
        .thenReturn(Optional.of(alertGroupConfig));
    Mockito.when(alertGroupConfigConditionService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of());
    if (id != null) {
      var dependency = new AlertGroupConfigDependencyEntity();
      dependency.setDependencyId("1");
      dependency.setId(1L);
      var dependency1 = new AlertGroupConfigDependencyEntity();
      dependency1.setDependencyId("2");
      dependency1.setId(2L);
      Mockito.when(alertGroupConfigDependencyService.findAllByAlertGroupConfigId(Mockito.anyLong()))
          .thenReturn(List.of(dependency, dependency1));
    }
    var res = alertGroupConfigServiceImpl.findByIdWithDetail(1L);
    Assertions.assertNotNull(res);
  }

  @Test
  void findAllByServiceIdAndApplicationIdAndDeletedAndActive_success() {
    Mockito.when(alertGroupConfigRepository.findAllByServiceIdAndApplicationIdAndDeletedAndActive(
        Mockito.anyString(),
        Mockito.anyString(), Mockito.anyBoolean(), Mockito.anyBoolean())).thenReturn(List.of());
    var res =
        alertGroupConfigServiceImpl.findAllByServiceIdAndApplicationIdAndDeletedAndActive("a", "a",
            true, true);
    Assertions.assertEquals(0, res.size());
  }

  @Test
  void updateActive_success() throws BusinessException {
    Mockito.when(alertGroupConfigRepository.findById(Mockito.anyLong()))
        .thenReturn(Optional.of(new AlertGroupConfigEntity()));
    Mockito.when(alertGroupConfigRepository.save(Mockito.any(AlertGroupConfigEntity.class)))
        .thenReturn(new AlertGroupConfigEntity());
    var res = alertGroupConfigServiceImpl.updateActive(1L, false);
    Assertions.assertNotNull(res);
  }

  @Test
  void updateActive_failed_caseNotFound() {
    Mockito.when(alertGroupConfigRepository.findById(Mockito.anyLong()))
        .thenReturn(Optional.empty());
    Assertions.assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.updateActive(1L, false));
  }

  @Test
  void findAllByDeletedAndActive_success() {
    Mockito.when(alertGroupConfigRepository.findAllByDeletedAndActive(Mockito.anyBoolean(),
            Mockito.anyBoolean()))
        .thenReturn(List.of());
    var res = alertGroupConfigServiceImpl.findAllByDeletedAndActive(true, true);
    Assertions.assertEquals(0, res.size());
  }

  @ParameterizedTest
  @CsvSource(quoteCharacter = '"', textBlock = """
      # fromId,toId,fromPosition,toPosition
      1,1,2,3
      1,2,2,2
      ,,,
      """)
  public void updatePosition_failed_caseInvalidRequest(Long fromId, Long toId, Integer fromPosition,
                                                       Integer toPosition) {
    var request = new AlertGroupConfigUpdatePositionRequest();
    request.setAlertGroupConfigToId(toId);
    request.setAlertGroupConfigFromId(fromId);
    request.setFromPosition(fromPosition);
    request.setToPosition(toPosition);

    assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.updatePosition(request));
  }

  @Test
  public void updatePosition_failed_caseNotFound() {
    var request = new AlertGroupConfigUpdatePositionRequest();
    request.setAlertGroupConfigToId(1L);
    request.setAlertGroupConfigFromId(2L);
    request.setToPosition(1);
    request.setFromPosition(2);
    when(alertGroupConfigRepository.findAllByPositionBetween(anyInt(), anyInt())).thenReturn(
        List.of());

    assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.updatePosition(request));
  }

  @Test
  public void updatePosition_failed_caseFound1Item() {
    var request = new AlertGroupConfigUpdatePositionRequest();
    request.setAlertGroupConfigToId(1L);
    request.setAlertGroupConfigFromId(2L);
    request.setToPosition(1);
    request.setFromPosition(2);
    when(alertGroupConfigRepository.findAllByPositionBetween(anyInt(), anyInt())).thenReturn(
        List.of(new AlertGroupConfigEntity()));

    assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.updatePosition(request));
  }

  @Test
  public void updatePosition_failed_caseWrongPosition1() {
    var request = new AlertGroupConfigUpdatePositionRequest();
    request.setAlertGroupConfigToId(1L);
    request.setAlertGroupConfigFromId(2L);
    request.setToPosition(1);
    request.setFromPosition(2);

    var fromEntity = new AlertGroupConfigEntity();
    fromEntity.setId(2L);
    fromEntity.setPosition(2);
    var toEntity = new AlertGroupConfigEntity();
    toEntity.setId(1L);
    toEntity.setPosition(3);
    when(alertGroupConfigRepository.findAllByPositionBetween(anyInt(), anyInt())).thenReturn(
        List.of(fromEntity, toEntity));

    assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.updatePosition(request));
  }

  @Test
  public void updatePosition_failed_caseWrongPosition2() {
    var request = new AlertGroupConfigUpdatePositionRequest();
    request.setAlertGroupConfigToId(1L);
    request.setAlertGroupConfigFromId(2L);
    request.setToPosition(1);
    request.setFromPosition(2);

    var fromEntity = new AlertGroupConfigEntity();
    fromEntity.setId(2L);
    fromEntity.setPosition(3);
    var toEntity = new AlertGroupConfigEntity();
    toEntity.setId(1L);
    toEntity.setPosition(1);
    when(alertGroupConfigRepository.findAllByPositionBetween(anyInt(), anyInt())).thenReturn(
        List.of(fromEntity, toEntity));

    assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.updatePosition(request));
  }

  @Test
  public void updatePosition_failed_caseWrongId1() {
    var request = new AlertGroupConfigUpdatePositionRequest();
    request.setAlertGroupConfigToId(1L);
    request.setAlertGroupConfigFromId(2L);
    request.setToPosition(1);
    request.setFromPosition(2);

    var fromEntity = new AlertGroupConfigEntity();
    fromEntity.setId(2L);
    fromEntity.setPosition(2);
    var toEntity = new AlertGroupConfigEntity();
    toEntity.setId(5L);
    toEntity.setPosition(1);
    when(alertGroupConfigRepository.findAllByPositionBetween(anyInt(), anyInt())).thenReturn(
        List.of(fromEntity, toEntity));

    assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.updatePosition(request));
  }

  @Test
  public void updatePosition_failed_caseWrongId2() {
    var request = new AlertGroupConfigUpdatePositionRequest();
    request.setAlertGroupConfigToId(1L);
    request.setAlertGroupConfigFromId(2L);
    request.setToPosition(1);
    request.setFromPosition(2);

    var fromEntity = new AlertGroupConfigEntity();
    fromEntity.setId(5L);
    fromEntity.setPosition(2);
    var toEntity = new AlertGroupConfigEntity();
    toEntity.setId(1L);
    toEntity.setPosition(1);
    when(alertGroupConfigRepository.findAllByPositionBetween(anyInt(), anyInt())).thenReturn(
        List.of(fromEntity, toEntity));

    assertThrows(BusinessException.class,
        () -> alertGroupConfigServiceImpl.updatePosition(request));
  }

  @ParameterizedTest
  @CsvSource(quoteCharacter = '"', textBlock = """
      # fromId,toId,fromPosition,toPosition
      1,2,1,2
      1,2,2,1
      """)
  public void updatePosition_success(Long fromId, Long toId, Integer fromPosition,
                                     Integer toPosition) throws BusinessException {
    var request = new AlertGroupConfigUpdatePositionRequest();
    request.setAlertGroupConfigToId(toId);
    request.setAlertGroupConfigFromId(fromId);
    request.setToPosition(toPosition);
    request.setFromPosition(fromPosition);

    var fromEntity = new AlertGroupConfigEntity();
    fromEntity.setId(fromId);
    fromEntity.setPosition(fromPosition);
    var toEntity = new AlertGroupConfigEntity();
    toEntity.setId(toId);
    toEntity.setPosition(toPosition);
    when(alertGroupConfigRepository.findAllByPositionBetween(anyInt(), anyInt())).thenReturn(
        List.of(fromEntity, toEntity));
    when(alertGroupConfigRepository.saveAll(anyList())).thenReturn(
        List.of(fromEntity, toEntity));

    var result = alertGroupConfigServiceImpl.updatePosition(request);

    assertNotNull(result);
  }

  @Test
  void findAllByPriorityConfigId_success() {
    var alertGroupConfig = new AlertGroupConditionDependenciesModel();
    alertGroupConfig.setName("a");
    alertGroupConfig.setRules(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"priority\",\"operator\":\"IS_ONE_OF\",\"value\":[\"2001\"]}]}");
    when(alertGroupConfigRepository.findAllByNotDeletedAndCustomObjectNull()).thenReturn(
        List.of(alertGroupConfig));
    var res = alertGroupConfigServiceImpl.findAllByPriorityConfigId(2001L);
    assertEquals(1, res.size());
  }

  @Test
  void findAllByPriorityConfigId_success_caseHasPriorityCheck() {
    var alertGroupConfig = new AlertGroupConditionDependenciesModel();
    alertGroupConfig.setName("a");
    alertGroupConfig.setRules(
        "{combinator: \"AND\", rules: [{field: \"priority\", operator: \"IS_ONE_OF\", value: [\"3508\"]}]}");
    when(alertGroupConfigRepository.findAllByNotDeletedAndCustomObjectNull()).thenReturn(
        List.of(alertGroupConfig));
    var res = alertGroupConfigServiceImpl.findAllByPriorityConfigId(3508L);
    assertEquals(0, res.size());
  }

  @Test
  void findAllByPriorityConfigId_success_caseRuleEmpty() {
    var alertGroupConfig = new AlertGroupConditionDependenciesModel();
    alertGroupConfig.setName("a");
    alertGroupConfig.setRules(
        "{\"combinator\":\"AND\",\"rules\":[]}");
    when(alertGroupConfigRepository.findAllByNotDeletedAndCustomObjectNull()).thenReturn(
        List.of(alertGroupConfig));
    var res = alertGroupConfigServiceImpl.findAllByPriorityConfigId(2001L);
    assertEquals(0, res.size());
  }

  @Test
  void findAllByCustomObjectId_success() {
    var alertGroupConfig = new AlertGroupConditionDependenciesModel();
    alertGroupConfig.setName("a");
    alertGroupConfig.setCustomObjectId(102L);
    var alertGroupConfig2 = new AlertGroupConditionDependenciesModel();
    alertGroupConfig2.setName("b");
    alertGroupConfig2.setRules(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"102\",\"operator\":\"IS\",\"value\":\"a\"}]}");
    when(alertGroupConfigRepository.findAllByNotDeleted()).thenReturn(
        List.of(alertGroupConfig, alertGroupConfig2));
    var res = alertGroupConfigServiceImpl.findAllByCustomObjectId(102L);
    assertEquals(2, res.size());
  }
}
