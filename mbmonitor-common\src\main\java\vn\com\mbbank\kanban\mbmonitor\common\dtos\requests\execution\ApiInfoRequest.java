package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecutionKeyValue;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionApiMethodEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.HttpVersionEnum;

/**
 * ApiInfoModel.
 */
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ApiInfoRequest {
  @Size(max = CommonConstants.EXECUTION_API_URL_MAX_LENGTH)
  @NotEmpty
  String url;
  @NotEmpty
  ExecutionApiMethodEnum method;
  HttpVersionEnum httpVersion;
  boolean enableSsl;
  List<String> executionParams;
  @Builder.Default
  List<ExecutionKeyValue> params = new ArrayList<>();
  @Builder.Default
  List<ExecutionKeyValue> headers = new ArrayList<>();
  AuthenticationApiRequest authentication;
  BodyApiRequest body;
}