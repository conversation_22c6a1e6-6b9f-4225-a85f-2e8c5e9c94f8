package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.server.annotations.ValidEnum;
import vn.com.mbbank.kanban.mbmonitor.server.enums.ExportFileTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 09/26/2024
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExportFileDto {
  @NotNull(message = "List attribute info cannot be empty.!")
  private List<AttributeInfoDto> attributes;

  @ValidEnum(enumClass = ExportFileTypeEnum.class, message = "Invalid file type")
  private ExportFileTypeEnum typeFile;

  private List<String> title;
}
