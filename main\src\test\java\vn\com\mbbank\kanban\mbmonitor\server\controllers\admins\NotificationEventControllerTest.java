package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationEventRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationEventResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationEventScheduleTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.services.NotificationEventService;
import vn.com.mbbank.kanban.test.ControllerTest;

@ExtendWith(MockitoExtension.class)
public class NotificationEventControllerTest extends ControllerTest {

  @Mock
  private NotificationEventService notificationEventService;

  @InjectMocks
  @Spy
  private NotificationEventController notificationEventController;

  @Test
  void findAll_success_withValidPaginationRequest() {
    // Arrange
    PaginationRequest paginationRequest = new PaginationRequest();
    paginationRequest.setPage(0);
    paginationRequest.setSize(10);

    NotificationEventResponse mockResponse = createMockNotificationEventResponse();
    List<NotificationEventResponse> responseList = Collections.singletonList(mockResponse);
    Page<NotificationEventResponse> page = new PageImpl<>(responseList);

    when(notificationEventService.findAll(any(PaginationRequest.class))).thenReturn(page);

    // Act
    ResponseData<Page<NotificationEventResponse>> result =
        notificationEventController.findAll(paginationRequest);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getData());
    assertEquals(1, result.getData().getContent().size());
    assertEquals("test-id", result.getData().getContent().get(0).getId());

    verify(notificationEventService).findAll(any(PaginationRequest.class));
  }

  @Test
  void findById_success_withValidId() throws BusinessException {
    // Arrange
    String id = "test-id";
    NotificationEventResponse mockResponse = createMockNotificationEventResponse();

    when(notificationEventService.findById(eq(id))).thenReturn(mockResponse);

    // Act
    ResponseData<NotificationEventResponse> result =
        notificationEventController.findById(id);

    // Assert
    assertNotNull(result);

    assertNotNull(result.getData());
    assertEquals("test-id", result.getData().getId());
    assertEquals("Test Notification", result.getData().getTitle());

    verify(notificationEventService).findById(eq(id));
  }

  @Test
  void createOrUpdate_success_withValidRequest() throws BusinessException {
    doNothing().when(notificationEventController)
        .makeSureCreateOrUpdate(any(), any(), any(), any());
    // Arrange
    NotificationEventRequest request = createMockNotificationEventRequest();
    NotificationEventResponse mockResponse = createMockNotificationEventResponse();

    when(notificationEventService.createOrUpdate(any(NotificationEventRequest.class))).thenReturn(mockResponse);

    // Act
    ResponseData<NotificationEventResponse> result =
        notificationEventController.createOrUpdate(request);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getData());
    assertEquals("test-id", result.getData().getId());
    assertEquals("Test Notification", result.getData().getTitle());

    verify(notificationEventService).createOrUpdate(any(NotificationEventRequest.class));
  }

  @Test
  void deleteById_success_withValidId() throws BusinessException {
    // Arrange
    String id = "test-id";
    doNothing().when(notificationEventService).deleteById(eq(id));

    // Act
    ResponseData<String> result = notificationEventController.deleteById(id);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getData());

    verify(notificationEventService).deleteById(eq(id));
  }

  @Test
  void toggleStatus_success_withValidId() throws BusinessException {
    // Arrange
    String id = "test-id";
    doNothing().when(notificationEventService).toggleStatus(eq(id));

    // Act
    ResponseData<String> result = notificationEventController.updateActive(id);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getData());
    assertEquals("OK", result.getData());

    verify(notificationEventService).toggleStatus(eq(id));
  }

  private NotificationEventResponse createMockNotificationEventResponse() {
    NotificationEventResponse response = new NotificationEventResponse();
    response.setId("test-id");
    response.setTitle("Test Notification");
    response.setContent("Test Content");
    response.setNotificationType(NotificationTypeEnum.INFO);
    response.setScheduleType("ONE_TIME");
    response.setTriggeredDate(new Date());
    response.setActive(true);
    return response;
  }

  private NotificationEventRequest createMockNotificationEventRequest() {
    NotificationEventRequest request = new NotificationEventRequest();
    request.setTitle("Test Notification");
    request.setContent("Test Content");
    request.setNotificationType(NotificationTypeEnum.INFO);
    request.setScheduleType(NotificationEventScheduleTypeEnum.ONE_TIME);
    request.setTriggeredDate(new Date());
    request.setUserNames(new ArrayList<>());
    request.setRoleIds(new ArrayList<>());
    return request;
  }

  @Test
  void findAll_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.NOTIFICATION_EVENT, PermissionActionEnum.VIEW)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "findAll", NotificationEventController.class);
  }

  @Test
  void findById_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.NOTIFICATION_EVENT, PermissionActionEnum.VIEW)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "findById", NotificationEventController.class);
  }

  @Test
  void deleteById_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.NOTIFICATION_EVENT, PermissionActionEnum.DELETE)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "deleteById", NotificationEventController.class);
  }

  @Test
  void updateActive_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.NOTIFICATION_EVENT, PermissionActionEnum.EDIT)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "updateActive", NotificationEventController.class);
  }
}