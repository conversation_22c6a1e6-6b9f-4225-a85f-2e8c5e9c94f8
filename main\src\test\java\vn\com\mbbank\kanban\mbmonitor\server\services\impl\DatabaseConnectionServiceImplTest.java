package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.zaxxer.hikari.HikariConfig;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.common.configs.QueryHikariDataSourceConfig;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.DatabaseConnectionLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DatabaseConnectionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OracleDatabaseConnectType;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.DatabaseConnectionRequestToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.log.DatabaseConnectionLogModelMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.DatabaseThresholdConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.DatabaseConnectionRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertRequestDatabaseService;
import vn.com.mbbank.kanban.mbmonitor.server.services.DatabaseCollectService;
import vn.com.mbbank.kanban.mbmonitor.server.services.DatabaseThresholdConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionService;

@ExtendWith(MockitoExtension.class)
public class DatabaseConnectionServiceImplTest {
  @Mock
  DatabaseConnectionRepository databaseConnectionRepository;
  @Mock
  QueryHikariDataSourceConfig queryHikariDataSourceConfig;
  @Mock
  KanbanEncryptorUtils kanbanEncryptorUtils;
  @Mock
  DatabaseConnectionRequestToEntityMapper databaseConnectionRequestToEntityMapper;
  @Mock
  SysLogKafkaProducerService sysLogKafkaProducerService;
  @Mock
  DatabaseConnectionLogModelMapper databaseConnectionLogModelMapper; // Mock the mapper
  @Mock
  DatabaseCollectService databaseCollectService;
  @Mock
  DatabaseThresholdConfigService databaseThresholdConfigService;
  @Mock
  ExecutionService executionService;
  @Mock
  AlertRequestDatabaseService alertRequestDatabaseService;

  @InjectMocks
  DatabaseConnectionServiceImpl databaseConnectionService;

  @BeforeEach
  void setUp(){
    ReflectionTestUtils.setField(databaseConnectionService, "initializationFailTimeout", 1000L);
  }

  @TestForDev
  void getRepository_success() {
    assertEquals(databaseConnectionRepository, databaseConnectionService.getRepository());
  }

  @Test
  void testConnection_success_returnsTrue() throws BusinessException {
    // Arrange
    DatabaseConnectionRequest connectInfo = new DatabaseConnectionRequest();
    connectInfo.setHost("localhost");
    connectInfo.setPort(1521);
    connectInfo.setSid("XE");
    connectInfo.setOracleConnectType(OracleDatabaseConnectType.SID);
    connectInfo.setUserName("user");
    connectInfo.setPassword("password");
    connectInfo.setType(DatabaseConnectionTypeEnum.ORACLE);

    when(queryHikariDataSourceConfig.getDataSource(anyString())).thenReturn(null); // Mock successful connection

    // Act
    boolean result = databaseConnectionService.testConnection(connectInfo);

    // Assert
    assertTrue(result);
  }

  @Test
  void testConnection_failed_returnsFalse() throws BusinessException {
    // Arrange
    DatabaseConnectionRequest connectInfo = new DatabaseConnectionRequest();
    connectInfo.setHost("localhost");
    connectInfo.setPort(1521);
    connectInfo.setSid("XE");
    connectInfo.setOracleConnectType(OracleDatabaseConnectType.SID);
    connectInfo.setUserName("user");
    connectInfo.setPassword("password");
    connectInfo.setType(DatabaseConnectionTypeEnum.ORACLE);

    when(queryHikariDataSourceConfig.getDataSource(anyString())).thenThrow(
        new RuntimeException("Connection failed")); // Mock failed connection

    // Act
    boolean result = databaseConnectionService.testConnection(connectInfo);

    // Assert
    assertFalse(result);
  }

  @Test
  void checkConnection_withPasswordInRequest_success() throws BusinessException {
    // Arrange
    DatabaseConnectionRequest connectInfo = new DatabaseConnectionRequest();
    connectInfo.setHost("localhost");
    connectInfo.setPort(1521);
    connectInfo.setSid("XE");
    connectInfo.setOracleConnectType(OracleDatabaseConnectType.SID);
    connectInfo.setUserName("user");
    connectInfo.setPassword("password"); // Password provided

    // Spy on the service to mock the internal testConnection call
    DatabaseConnectionServiceImpl spyService = spy(databaseConnectionService);
    doReturn(true).when(spyService).testConnection(any(DatabaseConnectionRequest.class));

    // Act
    spyService.checkConnection(connectInfo);

    // Assert - No exception means success
  }

  @Test
  void checkConnection_withPasswordInRequest_failed() throws BusinessException {
    // Arrange
    DatabaseConnectionRequest connectInfo = new DatabaseConnectionRequest();
    connectInfo.setHost("localhost");
    connectInfo.setPort(1521);
    connectInfo.setSid("XE");
    connectInfo.setOracleConnectType(OracleDatabaseConnectType.SID);
    connectInfo.setUserName("user");
    connectInfo.setPassword("password"); // Password provided

    // Spy on the service to mock the internal testConnection call
    DatabaseConnectionServiceImpl spyService = spy(databaseConnectionService);
    doReturn(false).when(spyService).testConnection(any(DatabaseConnectionRequest.class));

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      spyService.checkConnection(connectInfo);
    });
    assertEquals(ErrorCode.DATABASE_CONNECT_FALSE.getCode(), exception.getCode());
  }

  @Test
  void checkConnection_withIdAndEmptyPassword_success() throws BusinessException {
    // Arrange
    Long connectionId = 1L;
    String encryptedPassword = "encryptedPassword";
    String decryptedPassword = "decryptedPassword";

    DatabaseConnectionRequest connectInfo = new DatabaseConnectionRequest();
    connectInfo.setId(connectionId);
    connectInfo.setPassword(""); // Empty password

    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(connectionId);
    connectionEntity.setPassword(encryptedPassword);
    // Set other necessary fields on connectionEntity if checkConnection uses them

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.of(connectionEntity));

    // Spy on the service to mock the internal testConnection call
    DatabaseConnectionServiceImpl spyService = spy(databaseConnectionService);
    doReturn(true).when(spyService).testConnection(any(DatabaseConnectionRequest.class));

    // Act
    spyService.checkConnection(connectInfo);

    // Assert - No exception means success
  }

  @Test
  void checkConnection_withIdAndEmptyPassword_failed() throws BusinessException {
    // Arrange
    Long connectionId = 1L;
    String encryptedPassword = "encryptedPassword";
    String decryptedPassword = "decryptedPassword";

    DatabaseConnectionRequest connectInfo = new DatabaseConnectionRequest();
    connectInfo.setId(connectionId);
    connectInfo.setPassword(""); // Empty password

    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(connectionId);
    connectionEntity.setPassword(encryptedPassword);
    // Set other necessary fields on connectionEntity if checkConnection uses them

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.of(connectionEntity));

    // Spy on the service to mock the internal testConnection call
    DatabaseConnectionServiceImpl spyService = spy(databaseConnectionService);
    doReturn(false).when(spyService).testConnection(any(DatabaseConnectionRequest.class));

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      spyService.checkConnection(connectInfo);
    });
    assertEquals(ErrorCode.DATABASE_CONNECT_FALSE.getCode(), exception.getCode());
  }

  @Test
  void checkConnection_withIdAndEmptyPassword_notFound() {
    // Arrange
    Long connectionId = 1L;

    DatabaseConnectionRequest connectInfo = new DatabaseConnectionRequest();
    connectInfo.setId(connectionId);
    connectInfo.setPassword(""); // Empty password

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      databaseConnectionService.checkConnection(connectInfo);
    });
    assertEquals(ErrorCode.DATABASE_CONNECTION_NOT_EXISTS.getCode(), exception.getCode());
  }

  @Test
  void checkConnection_byId_success() throws BusinessException {
    // Arrange
    Long connectionId = 1L;
    String encryptedPassword = "encryptedPassword";
    String decryptedPassword = "decryptedPassword";

    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(connectionId);
    connectionEntity.setPassword(encryptedPassword);
    // Set other necessary fields on connectionEntity

    DatabaseConnectionRequest connectionRequest = new DatabaseConnectionRequest();
    connectionRequest.setId(connectionId);
    // Set other necessary fields on connectionRequest

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.of(connectionEntity));

    // Spy on the service to mock the internal checkConnection(DatabaseConnectionRequest) call
    DatabaseConnectionServiceImpl spyService = spy(databaseConnectionService);
    doNothing().when(spyService).checkConnection(any(DatabaseConnectionRequest.class));

    // Act
    spyService.checkConnection(connectionId);

    // Assert - No exception means success
  }

  @Test
  void checkConnection_byId_notFound() {
    // Arrange
    Long connectionId = 1L;

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      databaseConnectionService.checkConnection(connectionId);
    });
    assertEquals(ErrorCode.DATABASE_CONNECTION_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void checkConnection_byId_internalCheckFailed() throws BusinessException {
    // Arrange
    Long connectionId = 1L;
    String encryptedPassword = "encryptedPassword";
    String decryptedPassword = "decryptedPassword";

    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(connectionId);
    connectionEntity.setPassword(encryptedPassword);
    // Set other necessary fields on connectionEntity

    DatabaseConnectionRequest connectionRequest = new DatabaseConnectionRequest();
    connectionRequest.setId(connectionId);
    // Set other necessary fields on connectionRequest

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.of(connectionEntity));

    // Spy on the service to mock the internal checkConnection(DatabaseConnectionRequest) call
    DatabaseConnectionServiceImpl spyService = spy(databaseConnectionService);
    doThrow(new BusinessException(ErrorCode.DATABASE_CONNECT_FALSE)).when(spyService)
        .checkConnection(any(DatabaseConnectionRequest.class));

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      spyService.checkConnection(connectionId);
    });
    assertEquals(ErrorCode.DATABASE_CONNECT_FALSE.getCode(), exception.getCode());
  }

  @Test
  void saveDataValid_create_success() throws BusinessException {
    // Arrange
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setName("TestConnection");
    request.setHost("localhost");
    request.setPort(1521);
    request.setSid("XE");
    request.setOracleConnectType(OracleDatabaseConnectType.SID);
    request.setUserName("user");
    request.setPassword("password");
    request.setType(DatabaseConnectionTypeEnum.ORACLE);

    DatabaseConnectionEntity savedEntity = new DatabaseConnectionEntity();
    savedEntity.setId(1L);
    savedEntity.setName("TestConnection");
    // Set other fields on savedEntity

    when(databaseConnectionRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(databaseConnectionRepository.save(any(DatabaseConnectionEntity.class))).thenReturn(savedEntity);

    // Spy on the service to mock the internal testConnection call
    DatabaseConnectionServiceImpl spyService = spy(databaseConnectionService);
    doReturn(true).when(spyService).testConnection(any(DatabaseConnectionRequest.class));

    // Act
    DatabaseConnectionEntity result = spyService.saveDataValid(request);

    // Assert
    assertEquals(savedEntity, result);
    verify(databaseConnectionRepository, times(1)).existsByNameIgnoreCase(eq("TestConnection"));
    verify(databaseConnectionRepository, times(1)).save(any(DatabaseConnectionEntity.class));
    verify(sysLogKafkaProducerService, times(1)).send(eq(LogActionEnum.CREATE_DATABASE_CONNECT), eq("TestConnection"),
        any());
  }

  @Test
  void saveDataValid_create_connectionFailed() throws BusinessException {
    // Arrange
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setName("TestConnection");
    request.setType(DatabaseConnectionTypeEnum.ORACLE);
    request.setOracleConnectType(OracleDatabaseConnectType.SID);
    request.setSid("123");
    // Set other necessary fields

    // Spy on the service to mock the internal testConnection call
    DatabaseConnectionServiceImpl spyService = spy(databaseConnectionService);
    doReturn(false).when(spyService).testConnection(any(DatabaseConnectionRequest.class));

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      spyService.saveDataValid(request);
    });
    assertEquals(ErrorCode.DATABASE_CONNECT_FALSE.getCode(), exception.getCode());
  }

  @Test
  void saveDataValid_create_nameExists() throws BusinessException {
    // Arrange
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setName("ExistingConnection");
    request.setDatabaseName("123");
    // Set other necessary fields

    // Spy on the service to mock the internal testConnection call
    DatabaseConnectionServiceImpl spyService = spy(databaseConnectionService);
    doReturn(true).when(spyService).testConnection(any(DatabaseConnectionRequest.class));

    when(databaseConnectionRepository.existsByNameIgnoreCase(anyString())).thenReturn(true);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      spyService.saveDataValid(request);
    });
    assertEquals(ErrorCode.DATABASE_CONNECTION_NAME_EXISTS.getCode(), exception.getCode());
  }

  @Test
  void saveDataValid_update_success() throws BusinessException {
    // Arrange
    Long connectionId = 1L;
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setId(connectionId);
    request.setName("UpdatedConnection");
    request.setHost("localhost");
    request.setPort(1521);
    request.setSid("XE");
    request.setOracleConnectType(OracleDatabaseConnectType.SID);
    request.setUserName("user");
    request.setPassword("password");
    request.setType(DatabaseConnectionTypeEnum.ORACLE);

    DatabaseConnectionEntity existingEntity = new DatabaseConnectionEntity();
    existingEntity.setId(connectionId);
    existingEntity.setName("OldConnection");
    // Set other fields on existingEntity

    DatabaseConnectionEntity updatedEntity = new DatabaseConnectionEntity();
    updatedEntity.setId(connectionId);
    updatedEntity.setName("UpdatedConnection");
    // Set other fields on updatedEntity

    when(databaseConnectionRepository.existsByNameIgnoreCaseAndIdNot(anyString(), eq(connectionId))).thenReturn(false);
    when(databaseConnectionRepository.save(any(DatabaseConnectionEntity.class))).thenReturn(updatedEntity);

    // Spy on the service to mock the internal testConnection call
    DatabaseConnectionServiceImpl spyService = spy(databaseConnectionService);
    doReturn(true).when(spyService).testConnection(any(DatabaseConnectionRequest.class));
    doReturn(existingEntity).when(spyService).findById(eq(connectionId)); // Mock findById for logging

    // Act
    DatabaseConnectionEntity result = spyService.saveDataValid(request);

    // Assert
    assertEquals(updatedEntity, result);
    verify(databaseConnectionRepository, times(1)).save(any(DatabaseConnectionEntity.class));
    verify(sysLogKafkaProducerService, times(1)).send(eq(LogActionEnum.EDIT_DATABASE_CONNECT), eq("OldConnection"),
        any());
  }

  @Test
  void saveDataValid_update_connectionFailed() throws BusinessException {
    // Arrange
    Long connectionId = 1L;
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setId(connectionId);
    request.setName("UpdatedConnection");
    request.setType(DatabaseConnectionTypeEnum.ORACLE);
    request.setOracleConnectType(OracleDatabaseConnectType.SID);
    request.setSid("123");
    // Set other necessary fields

    DatabaseConnectionEntity existingEntity = new DatabaseConnectionEntity();
    existingEntity.setId(connectionId);
    // Set other fields on existingEntity

    // Spy on the service to mock the internal testConnection call
    DatabaseConnectionServiceImpl spyService = spy(databaseConnectionService);
    doReturn(false).when(spyService).testConnection(any(DatabaseConnectionRequest.class));

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      spyService.saveDataValid(request);
    });
    assertEquals(ErrorCode.DATABASE_CONNECT_FALSE.getCode(), exception.getCode());
  }

  @Test
  void saveDataValid_update_nameExists() throws BusinessException {
    // Arrange
    Long connectionId = 1L;
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setId(connectionId);
    request.setDatabaseName("123");
    request.setName("ExistingConnection");
    // Set other necessary fields

    DatabaseConnectionEntity existingEntity = new DatabaseConnectionEntity();
    existingEntity.setId(connectionId);
    // Set other fields on existingEntity

    // Spy on the service to mock the internal testConnection call
    DatabaseConnectionServiceImpl spyService = spy(databaseConnectionService);
    doReturn(true).when(spyService).testConnection(any(DatabaseConnectionRequest.class));
    doReturn(existingEntity).when(spyService).findById(eq(connectionId)); // Mock findById for the check

    when(databaseConnectionRepository.existsByNameIgnoreCaseAndIdNot(anyString(), eq(connectionId))).thenReturn(true);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      spyService.saveDataValid(request);
    });
    assertEquals(ErrorCode.DATABASE_CONNECTION_NAME_EXISTS.getCode(), exception.getCode());
  }

  @Test
  void saveDataValid_update_notFound() throws BusinessException {
    // Arrange
    Long connectionId = 1L;
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setId(connectionId);
    request.setName("UpdatedConnection");
    request.setDatabaseName("123");
    // Set other necessary fields

    // Spy on the service to mock the internal testConnection call
    DatabaseConnectionServiceImpl spyService = spy(databaseConnectionService);
    doReturn(true).when(spyService).testConnection(any(DatabaseConnectionRequest.class));
    doReturn(null).when(spyService).findById(eq(connectionId)); // Mock findById to return null

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      spyService.saveDataValid(request);
    });
    assertEquals(ErrorCode.DATABASE_CONNECTION_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void setActiveById_success_activeTrue() throws BusinessException {
    // Arrange
    Long connectionId = 1L;
    boolean active = true;
    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(connectionId);
    connectionEntity.setHost("localhost"); // Set host for logging

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.of(connectionEntity));
    when(databaseConnectionRepository.setActiveById(eq(connectionId), eq(active))).thenReturn(
        1); // Simulate 1 row updated

    // Act
    int result = databaseConnectionService.setActiveById(connectionId, active);

    // Assert
    assertEquals(1, result);
    verify(databaseConnectionRepository, times(1)).findById(eq(connectionId));
    verify(databaseConnectionRepository, times(1)).setActiveById(eq(connectionId), eq(active));
    verify(sysLogKafkaProducerService, times(1)).send(eq(LogActionEnum.ACTIVE_DATABASE_CONNECT), eq("localhost"));
  }

  @Test
  void setActiveById_success_activeFalse() throws BusinessException {
    // Arrange
    Long connectionId = 1L;
    boolean active = false;
    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(connectionId);
    connectionEntity.setHost("localhost"); // Set host for logging

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.of(connectionEntity));
    when(databaseConnectionRepository.setActiveById(eq(connectionId), eq(active))).thenReturn(
        1); // Simulate 1 row updated

    // Act
    int result = databaseConnectionService.setActiveById(connectionId, active);

    // Assert
    assertEquals(1, result);
    verify(databaseConnectionRepository, times(1)).findById(eq(connectionId));
    verify(databaseConnectionRepository, times(1)).setActiveById(eq(connectionId), eq(active));
    verify(sysLogKafkaProducerService, times(1)).send(eq(LogActionEnum.INACTIVE_DATABASE_CONNECT), eq("localhost"));
  }

  @Test
  void setActiveById_notFound() {
    // Arrange
    Long connectionId = 1L;
    boolean active = true;

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      databaseConnectionService.setActiveById(connectionId, active);
    });
    assertEquals(ErrorCode.DATABASE_CONNECTION_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void createHikariConfig_sidType() {
    // Arrange
    DatabaseConnectionRequest connectInfo = new DatabaseConnectionRequest();
    connectInfo.setHost("localhost");
    connectInfo.setPort(1521);
    connectInfo.setSid("XE");
    connectInfo.setOracleConnectType(OracleDatabaseConnectType.SID);
    connectInfo.setUserName("user");
    connectInfo.setPassword("password");
    connectInfo.setType(DatabaseConnectionTypeEnum.ORACLE);

    String expectedUrl = "************************************************";

    // Act
    HikariConfig config = databaseConnectionService.createHikariConfig(connectInfo);

    // Assert
    assertEquals(expectedUrl, config.getJdbcUrl());
    assertEquals("user", config.getUsername());
    assertEquals("password", config.getPassword());
    assertEquals("oracle.jdbc.OracleDriver", config.getDriverClassName());
  }

  @Test
  void createHikariConfig_serviceNameType() {
    // Arrange
    DatabaseConnectionRequest connectInfo = new DatabaseConnectionRequest();
    connectInfo.setHost("localhost");
    connectInfo.setPort(1521);
    connectInfo.setServiceName("ORCL");
    connectInfo.setOracleConnectType(OracleDatabaseConnectType.SERVICE_NAME);
    connectInfo.setUserName("user");
    connectInfo.setPassword("password");
    connectInfo.setType(DatabaseConnectionTypeEnum.ORACLE);

    String expectedUrl = "****************************************************";

    // Act
    HikariConfig config = databaseConnectionService.createHikariConfig(connectInfo);

    // Assert
    assertEquals(expectedUrl, config.getJdbcUrl());
    assertEquals("user", config.getUsername());
    assertEquals("password", config.getPassword());
    assertEquals("oracle.jdbc.OracleDriver", config.getDriverClassName());
  }

  @Test
  void createHikariConfig_otherType() {
    // Arrange
    DatabaseConnectionRequest connectInfo = new DatabaseConnectionRequest();
    connectInfo.setHost("localhost");
    connectInfo.setPort(5432);
    // Assuming a different type not handled by Oracle logic
    connectInfo.setOracleConnectType(null);
    connectInfo.setUserName("user");
    connectInfo.setPassword("password");
    connectInfo.setType(DatabaseConnectionTypeEnum.ORACLE);

    // Act
    HikariConfig config = databaseConnectionService.createHikariConfig(connectInfo);

    // Assert
    assertEquals("****************************************************", config.getJdbcUrl()); // URL should be null for unhandled types
    assertEquals("user", config.getUsername());
    assertEquals("password", config.getPassword());
    assertEquals("oracle.jdbc.OracleDriver", config.getDriverClassName()); // Driver is hardcoded
  }

  @Test
  void deleteWithId_success_noDependencies() throws BusinessException {
    // Arrange
    Long connectionId = 1L;
    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(connectionId);
    connectionEntity.setHost("localhost"); // Set host for logging

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.of(connectionEntity));
    when(databaseCollectService.findAllByConnectionId(connectionId)).thenReturn(Collections.emptyList());
    when(databaseThresholdConfigService.findAllByDatabaseConnectionId(connectionId)).thenReturn(
        Collections.emptyList());
    when(executionService.existsByDatabaseConnectionId(connectionId)).thenReturn(false);

    // Spy on the service to mock the super.delete call
    DatabaseConnectionServiceImpl spyService = spy(databaseConnectionService);
    doNothing().when(spyService).delete(any(DatabaseConnectionEntity.class));

    // Act
    spyService.deleteWithId(connectionId);

    // Assert
    verify(databaseConnectionRepository, times(1)).findById(eq(connectionId));
    verify(databaseCollectService, times(1)).findAllByConnectionId(eq(connectionId));
    verify(databaseThresholdConfigService, times(1)).findAllByDatabaseConnectionId(eq(connectionId));
    verify(executionService, times(1)).existsByDatabaseConnectionId(eq(connectionId));
    verify(spyService, times(1)).delete(eq(connectionEntity)); // Verify super.delete was called
    verify(sysLogKafkaProducerService, times(1)).send(eq(LogActionEnum.DELETE_DATABASE_CONNECT), eq("localhost"));
  }

  @Test
  void deleteWithId_notFound() {
    // Arrange
    Long connectionId = 1L;

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      databaseConnectionService.deleteWithId(connectionId);
    });
    assertEquals(ErrorCode.DATABASE_CONNECTION_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void deleteWithId_hasCollectDependencies() {
    // Arrange
    Long connectionId = 1L;
    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(connectionId);

    List<DatabaseCollectEntity> collectDependencies = new ArrayList<>();
    DatabaseCollectEntity collect1 = new DatabaseCollectEntity();
    collect1.setName("Collect1");
    collectDependencies.add(collect1);
    DatabaseCollectEntity collect2 = new DatabaseCollectEntity();
    collect2.setName("Collect2");
    collectDependencies.add(collect2);

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.of(connectionEntity));
    when(databaseCollectService.findAllByConnectionId(connectionId)).thenReturn(collectDependencies);
    when(databaseThresholdConfigService.findAllByDatabaseConnectionId(connectionId)).thenReturn(
        Collections.emptyList());
    when(executionService.existsByDatabaseConnectionId(connectionId)).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      databaseConnectionService.deleteWithId(connectionId);
    });
    assertEquals(ErrorCode.DATABASE_CONNECTION_NOT_DELETE.getCode(), exception.getCode());
    assertTrue(exception.getMessage().contains("Database collect: Collect1, Collect2"));
  }

  @Test
  void deleteWithId_hasThresholdDependencies() {
    // Arrange
    Long connectionId = 1L;
    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(connectionId);

    List<DatabaseThresholdConfigResponse> thresholdDependencies = new ArrayList<>();
    DatabaseThresholdConfigResponse threshold1 = new DatabaseThresholdConfigResponse();
    threshold1.setName("Threshold1");
    thresholdDependencies.add(threshold1);
    DatabaseThresholdConfigResponse threshold2 = new DatabaseThresholdConfigResponse();
    threshold2.setName("Threshold2");
    thresholdDependencies.add(threshold2);

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.of(connectionEntity));
    when(databaseCollectService.findAllByConnectionId(connectionId)).thenReturn(Collections.emptyList());
    when(databaseThresholdConfigService.findAllByDatabaseConnectionId(connectionId)).thenReturn(thresholdDependencies);
    when(executionService.existsByDatabaseConnectionId(connectionId)).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      databaseConnectionService.deleteWithId(connectionId);
    });
    assertEquals(ErrorCode.DATABASE_CONNECTION_NOT_DELETE.getCode(), exception.getCode());
    assertTrue(exception.getMessage().contains("Database threshold: Threshold1, Threshold2"));
  }

  @Test
  void deleteWithId_hasExecutionDependencies() {
    // Arrange
    Long connectionId = 1L;
    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(connectionId);

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.of(connectionEntity));
    when(databaseCollectService.findAllByConnectionId(connectionId)).thenReturn(Collections.emptyList());
    when(databaseThresholdConfigService.findAllByDatabaseConnectionId(connectionId)).thenReturn(
        Collections.emptyList());
    when(executionService.existsByDatabaseConnectionId(connectionId)).thenReturn(true);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      databaseConnectionService.deleteWithId(connectionId);
    });
    assertEquals(ErrorCode.DATABASE_CONNECTION_NOT_DELETE.getCode(), exception.getCode());
    assertTrue(exception.getMessage().contains("executions"));
  }

  @Test
  void deleteWithId_hasMultipleDependencies() {
    // Arrange
    Long connectionId = 1L;
    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(connectionId);

    List<DatabaseCollectEntity> collectDependencies = new ArrayList<>();
    DatabaseCollectEntity collect1 = new DatabaseCollectEntity();
    collect1.setName("Collect1");
    collectDependencies.add(collect1);

    List<DatabaseThresholdConfigResponse> thresholdDependencies = new ArrayList<>();
    DatabaseThresholdConfigResponse threshold1 = new DatabaseThresholdConfigResponse();
    threshold1.setName("Threshold1");
    thresholdDependencies.add(threshold1);

    when(databaseConnectionRepository.findById(connectionId)).thenReturn(Optional.of(connectionEntity));
    when(databaseCollectService.findAllByConnectionId(connectionId)).thenReturn(collectDependencies);
    when(databaseThresholdConfigService.findAllByDatabaseConnectionId(connectionId)).thenReturn(thresholdDependencies);
    when(executionService.existsByDatabaseConnectionId(connectionId)).thenReturn(true);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      databaseConnectionService.deleteWithId(connectionId);
    });
    assertEquals(ErrorCode.DATABASE_CONNECTION_NOT_DELETE.getCode(), exception.getCode());
    assertTrue(
        exception.getMessage().contains("Database collect: Collect1; Database threshold: Threshold1; executions"));
  }
}

