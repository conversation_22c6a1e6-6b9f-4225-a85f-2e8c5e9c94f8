package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailTemplateModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailTemplatePaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailTemplateRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.EmailTemplateDependenciesResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.EmailTemplateResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailTemplateDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailTemplateService;

/**
 * Controller logic email template .
 *
 * <AUTHOR>
 * @created_date 11/04/2024
 */
@RestController
@RequestMapping(ServerUrl.EMAIL_TEMPLATE_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EmailTemplateController extends BaseController {
  private final EmailTemplateService emailTemplateService;
  private final EmailTemplateDependencyService emailTemplateDependencyService;
  final EmailTemplateResponseMapper responseMapper = EmailTemplateResponseMapper.INSTANCE;

  /**
   * Finds all email template.
   *
   * @param paginationRequest the pagination request containing paging details
   *                          and the list of service IDs to filter template
   * @return a paginated list of EmailTemplateModel
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_TEMPLATE_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.SEND_EMAIL, action = PermissionActionEnum.SEND_EMAIL)
  })
  @GetMapping("/with-paging")
  public ResponseData<Page<EmailTemplateModel>> findAllWithPaging(
      @ModelAttribute EmailTemplatePaginationRequest paginationRequest) {
    return ResponseUtils.success(this.emailTemplateService.findAll(paginationRequest));
  }

  /**
   * Finds all email template.
   *
   * @return a list of EmailTemplateModel
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_TEMPLATE_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.SEND_EMAIL, action = PermissionActionEnum.SEND_EMAIL),
      @AclPermission(module = PermissionModuleEnum.IT_SERVICE_MANAGEMENT, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  public ResponseData<List<EmailTemplateModel>> findAll() {
    return ResponseUtils.success(responseMapper.map(emailTemplateService.findAll()));
  }

  /**
   * Find By id.
   *
   * @param id id
   * @return data
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_TEMPLATE_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.SEND_EMAIL, action = PermissionActionEnum.SEND_EMAIL)
  })
  @GetMapping("/{id}")
  public ResponseData<EmailTemplateModel> findById(@PathVariable Long id) throws BusinessException {
    return ResponseUtils.success(this.emailTemplateService.findEmailTemplateById(id));
  }

  /**
   * Create or update template.
   *
   * @param request request
   * @param files   files
   * @return EmailTemplateModel
   */
  @PostMapping(consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE})
  public ResponseData<EmailTemplateModel> createOrUpdate(
      @RequestPart @Valid EmailTemplateRequest request,
      @RequestPart(value = "files", required = false)
      List<MultipartFile> files
  ) throws BusinessException, IOException {
    makeSureCreateOrUpdate(PermissionModuleEnum.EMAIL_TEMPLATE_CONFIG, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(request.getId()));
    return ResponseUtils.success(this.emailTemplateService.createOrUpdate(request, files));
  }

  /**
   * Delete by Id.
   *
   * @param id id
   * @return Object
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_TEMPLATE_CONFIG, action = PermissionActionEnum.DELETE)
  })
  @Operation(summary = "Delete email partner by id")
  @DeleteMapping("/{id}")
  public ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    this.emailTemplateDependencyService.deleteWithId(id);
    return ResponseUtils.success(HttpStatus.OK.getReasonPhrase());
  }

  /**
   * Delete by Id.
   *
   * @param id id
   * @return Object
   */
  @Operation(summary = "Delete email partner by id")
  @GetMapping("/download/{id}")
  public ResponseEntity<StreamingResponseBody> download(@PathVariable Long id) throws BusinessException, IOException {
    return ResponseEntity.ok()
        .contentType(MediaType.APPLICATION_OCTET_STREAM)
        .header(HttpHeaders.CONTENT_DISPOSITION, CommonConstants.CONTENT_DISPOSITION_ATTACHMENT)
        .body(this.emailTemplateService.download(id));
  }

  /**
   * find all dependency.
   *
   * @param id emailTemplate
   * @return list dependencies
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_TEMPLATE_CONFIG, action = PermissionActionEnum.DELETE)
  })
  @GetMapping("{id}/dependencies")
  public ResponseData<EmailTemplateDependenciesResponse> findAllDependenciesById(
      @PathVariable Long id) {
    return ResponseUtils.success(emailTemplateDependencyService.findAllDependenciesById(id));
  }
}
