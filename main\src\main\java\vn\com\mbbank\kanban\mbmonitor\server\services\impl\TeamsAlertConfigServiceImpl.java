package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static vn.com.mbbank.kanban.mbmonitor.server.constants.AlertConstants.TEAMS_ALERT_CONTENT_TYPE_HTML;
import static vn.com.mbbank.kanban.mbmonitor.server.constants.AlertConstants.TEAMS_ALERT_JSON_PATH_CONTENT_TYPE;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.KanbanRedisService;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.core.utils.KanbanJsonUtils;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsConfigModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsSendModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TeamsConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonTeamsService;
import vn.com.mbbank.kanban.mbmonitor.common.services.builder.CommonTeamsServiceBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.RedisUtils;
import vn.com.mbbank.kanban.mbmonitor.server.constants.AlertConstants;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TeamsConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.TeamsConfigModelToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.TeamsConfigRequestToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.TeamsConfigRequestToModelMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.TeamsConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.TeamsAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.TeamsGroupConfigService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/16/2025
 */
@Service
@RequiredArgsConstructor
public class TeamsAlertConfigServiceImpl extends BaseServiceImpl<TeamsConfigEntity, String>
    implements
    TeamsAlertConfigService {

  private final TeamsConfigRepository teamsConfigRepository;
  private final TeamsGroupConfigService teamsGroupConfigService;
  private final CommonKafkaProducerService commonKafkaProducerService;
  private final KanbanRedisService kanbanRedisService;
  protected static Logger logger = LoggerFactory.getLogger(TeamsAlertConfigServiceImpl.class);
  private static String teamsErrorJson = "$.error.innerError.message";

  @Override
  public TeamsConfigModel findTeamsAlertConfig() {
    var teamsConfigEntity =
        teamsConfigRepository.findFirstByType(TeamsConfigTypeEnum.ALERT).orElse(null);
    if (KanbanCommonUtil.isEmpty(teamsConfigEntity)) {
      return null;
    }
    var result = TeamsConfigModelToEntityMapper.INSTANCE.map(teamsConfigEntity);
    result.setClientSecretPlaceholder("***********"
        +
        StringUtils.right(KanbanEncryptorUtils.decrypt(result.getClientSecret()), 3));
    return result;
  }

  @Override
  public TeamsConfigModel saveConfig(TeamsConfigRequest request) throws BusinessException {
    boolean isBasicUpdate =
        Stream.of(request.getClientId(), request.getTenantId(), request.getClientSecret(),
                request.getPassword())
            .anyMatch(Objects::isNull);

    var configEntity = TeamsConfigRequestToEntityMapper.INSTANCE.mapTo(request);
    // check update or create new
    TeamsConfigEntity configDb =
        teamsConfigRepository.findFirstByType(TeamsConfigTypeEnum.ALERT).orElse(null);
    if (isBasicUpdate && KanbanCommonUtil.isEmpty(configDb)) {
      throw new BusinessException(ErrorCode.TEAMS_CONFIG_DATA_INVALID);
    }
    configEntity.setPassword(KanbanEncryptorUtils.encrypt(configEntity.getPassword()));
    configEntity.setClientSecret(KanbanEncryptorUtils.encrypt(configEntity.getClientSecret()));

    if (isBasicUpdate && !KanbanCommonUtil.isEmpty(configDb)) {
      configEntity = configDb;
      configEntity.setInterval(request.getInterval());
      configEntity.setIntervalType(request.getIntervalType());
      configEntity.setMessageTemplate(request.getMessageTemplate());
    }

    if (!KanbanCommonUtil.isEmpty(configDb)) {
      configEntity.setId(configDb.getId());
    }

    configEntity.setType(TeamsConfigTypeEnum.ALERT);
    // check connect teams
    if (!isBasicUpdate) {
      TeamsConfigModel configModel = TeamsConfigRequestToModelMapper.INSTANCE.mapTo(request);
      var teams = new CommonTeamsServiceBuilder(configModel);
      if (KanbanCommonUtil.isEmpty(teams.getInstance().getTokenFromTeams())) {
        throw new BusinessException(ErrorCode.TEAMS_CONFIG_DATA_INVALID);
      }
    }
    var result = save(configEntity);

    var data = new BaseKafkaModel<>();
    data.setType(KafkaTypeEnum.TEAMS_CONFIG_ALERT);
    commonKafkaProducerService.send(data);
    kanbanRedisService.delete(
        RedisUtils.getTeamsTokenRedisKey(configEntity.getClientId(), configEntity.getEmail()));
    return TeamsConfigModelToEntityMapper.INSTANCE.map(result);
  }

  /**
   * Sends message to Teams users or groups.
   * Validates contacts, checks existing groups and handles message delivery.
   *
   * @param model The model containing message content and recipient information
   * @return TeamsSendModel with sending status and details
   * @throws BusinessException If configuration not found or contacts are invalid
   */
  @Override
  public TeamsSendModel sendMessage(TeamsSendModel model) throws BusinessException {
    // Find Teams configuration
    var teamsConfig = findTeamsAlertConfig();
    if (KanbanCommonUtil.isEmpty(teamsConfig)) {
      throw new BusinessException(ErrorCode.TEAMS_CONFIG_NOT_FOUND);
    }
    if (KanbanCommonUtil.isEmpty(teamsConfig.getMessageTemplate())) {
      throw new BusinessException(ErrorCode.TEAMS_SEND_MESSAGE_ERROR);
    }
    String formattedMessage = model.getMessage();

    if (TEAMS_ALERT_CONTENT_TYPE_HTML.equalsIgnoreCase(
        KanbanJsonUtils.getValueByPath(teamsConfig.getMessageTemplate(),
            TEAMS_ALERT_JSON_PATH_CONTENT_TYPE))) {
      formattedMessage = model.getMessage().replace("\n", "<br>");
    }
    String messageBuilder = KanbanJsonUtils.replacePlaceholders(teamsConfig.getMessageTemplate(),
        AlertConstants.TEAMS_ALERT_CONTENT_KEY, formattedMessage);
    // Validate contact list
    List<String> contacts = Optional.ofNullable(model.getContacts()).orElse(new ArrayList<>());
    if (KanbanCommonUtil.isEmpty(contacts)) {
      throw new BusinessException(ErrorCode.TEAMS_CONTACT_INVALID);
    }

    // Check if contacts contain a group
    boolean isExistGroup = contacts.stream()
        .anyMatch(contact -> !contact.toLowerCase().endsWith(CommonConstants.EMAIL_MB_SUFFIX));

    if (isExistGroup && contacts.size() > 1) {
      throw new BusinessException(ErrorCode.TEAMS_CONTACT_INVALID);
    }

    // Prepare result model
    TeamsSendModel teamsSendModel = new TeamsSendModel();
    teamsSendModel.setContacts(contacts);

    // Decrypt sensitive information
    teamsConfig.setPassword(KanbanEncryptorUtils.decrypt(teamsConfig.getPassword()));
    teamsConfig.setClientSecret(KanbanEncryptorUtils.decrypt(teamsConfig.getClientSecret()));

    // Initialize Teams service
    CommonTeamsService teamsService = new CommonTeamsServiceBuilder(teamsConfig).getInstance();

    if (isExistGroup) {
      return sendMessageToGroup(messageBuilder, contacts.get(0), teamsService, teamsSendModel);
    } else {
      return sendMessageToUsers(messageBuilder, contacts, teamsConfig, teamsService,
          teamsSendModel);
    }
  }

  @Override
  public void triggerJob() {
    var data = new BaseKafkaModel<>();
    data.setType(KafkaTypeEnum.TEAMS_CONFIG_ALERT_TRIGGER);
    commonKafkaProducerService.send(data);
  }

  /**
   * Sends message to an existing Teams group.
   *
   * @param body           Message content to send
   * @param groupName      Name of the group to send message to
   * @param teamsService   Teams service instance for API communication
   * @param teamsSendModel Model to update with result information
   * @return Updated TeamsSendModel with sending status
   * @throws BusinessException If group not found
   */
  TeamsSendModel sendMessageToGroup(String body, String groupName,
                                    CommonTeamsService teamsService,
                                    TeamsSendModel teamsSendModel)
      throws BusinessException {
    var groupFromDb = teamsGroupConfigService.findFirstByTeamsGroupNameIgnoreCase(groupName)
        .orElseThrow(() -> new BusinessException(ErrorCode.TEAMS_GROUP_NOT_FOUND));

    sendMessageWithBody(teamsService, body, groupFromDb.getTeamsGroupId());
    teamsSendModel.setIsSend(true);
    return teamsSendModel;
  }

  /**
   * Sends message to individual users or creates a new group if needed.
   * First tries to find an existing group with all participants.
   * Creates a new group if none exists or if sending to existing group fails.
   *
   * @param body           Message content to send
   * @param contacts       List of recipient email addresses
   * @param teamsConfig    Teams configuration settings
   * @param teamsService   Teams service instance for API communication
   * @param teamsSendModel Model to update with result information
   * @return Updated TeamsSendModel with sending status and any invalid emails
   * @throws BusinessException If all contacts are invalid
   */
  TeamsSendModel sendMessageToUsers(String body, List<String> contacts,
                                    TeamsConfigModel teamsConfig,
                                    CommonTeamsService teamsService,
                                    TeamsSendModel teamsSendModel)
      throws BusinessException {

    // Add sender's email to the group search list
    var emailsForGroupSearch = new HashSet<>(contacts);
    emailsForGroupSearch.add(teamsConfig.getEmail());
    // Find existing group
    var existingGroup = emailsForGroupSearch.size() > 2 ? teamsGroupConfigService.findGroupByEmails(
        emailsForGroupSearch.stream().map(String::toLowerCase).toList()) : null;

    if (!KanbanCommonUtil.isEmpty(existingGroup)) {
      // Try sending message to existing group
      var resultSend = teamsService.sendMessageWithBody(body, existingGroup.getTeamsGroupId());

      if (HttpStatus.CREATED.equals(resultSend.getStatusCode())) {
        teamsSendModel.setIsSend(true);
        return teamsSendModel;
      } else {
        throw new BusinessException(ErrorCode.TEAMS_SEND_MESSAGE_ERROR);
      }
    }

    // Create new group if none found or message sending failed
    var newGroup = teamsService.createGroupChat(contacts, false);

    if (!KanbanCommonUtil.isEmpty(newGroup)
        &&
        !KanbanCommonUtil.isEmpty(newGroup.getGroupChatId())) {
      sendMessageWithBody(teamsService, body, newGroup.getGroupChatId());
      newGroup.setTeamsConfigId(teamsConfig.getId());
      if (emailsForGroupSearch.size() > 2) {
        teamsGroupConfigService.saveGroupChat(newGroup);
      }

      teamsSendModel.setIsSend(true);
    } else {
      teamsSendModel.setIsSend(false);
      teamsSendModel.setInvalidEmails(newGroup.getEmailInvalid());
      // Check if all emails are invalid
      if (KanbanCommonUtil.isEmpty(newGroup.getEmailValid())) {
        throw new BusinessException(ErrorCode.TEAMS_CONTACT_INVALID);
      }
    }

    return teamsSendModel;
  }

  private void sendMessageWithBody(CommonTeamsService teamsService, String body, String chatId)
      throws BusinessException {
    var result = teamsService.sendMessageWithBody(body, chatId);
    if (!result.getStatusCode().is2xxSuccessful()) {
      logger.error("send teams error", result);
      String messageError = KanbanJsonUtils.getValueByPath(result.getBody(), teamsErrorJson,
          ErrorCode.TEAMS_SEND_MESSAGE_ERROR.getMessage());
      throw new BusinessException(ErrorCode.MESSAGE_ERROR_CUSTOM, messageError);
    }
  }

  @Override
  protected JpaCommonRepository<TeamsConfigEntity, String> getRepository() {
    return teamsConfigRepository;
  }
}
