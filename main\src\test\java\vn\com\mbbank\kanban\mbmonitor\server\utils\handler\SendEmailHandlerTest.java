package vn.com.mbbank.kanban.mbmonitor.server.utils.handler;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import jakarta.activation.DataSource;
import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailPartnerAddressEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailPartnerEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailComposedModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailConfigModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.FileStorageModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailPartnerRequest;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailPartnerAddressService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailPartnerService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailService;
import vn.com.mbbank.kanban.mbmonitor.server.services.FileStorageService;

@ExtendWith(MockitoExtension.class)
public class SendEmailHandlerTest {
  @InjectMocks
  private SendEmailHandler sendEmailHandler;
  @Mock
  private EmailPartnerService emailPartnerService;
  @Mock
  private FileStorageService fileStorageService;
  @Mock
  private EmailPartnerAddressService emailPartnerAddressService;
  @Mock
  private EmailConfigService emailConfigService;
  @Mock
  private EmailService emailService;
  @Mock
  private JavaMailSender javaMailSender;
  @Mock
  private MimeMessage mimeMessage;

  @BeforeEach
  void setUp() {
    ReflectionTestUtils.setField(sendEmailHandler, "emailMaxTotalSize", 8000L);
    ReflectionTestUtils.setField(sendEmailHandler, "corePoolSize", 5);
  }

  @Test
  void shouldThrowExceptionWhenRecipientsAreEmpty() {
    EmailComposedModel emailComposed = new EmailComposedModel();
    BusinessException exception = assertThrows(
        BusinessException.class,
        () -> sendEmailHandler.validateEmailRecipients(emailComposed)
    );
    assertEquals(ErrorCode.EMAIL_RECEIVER_IS_EMPTY.getCode(), exception.getCode());
  }

  @Test
  void shouldThrowExceptionWhenPartnersAreNonExistent() {
    EmailPartnerRequest partner = new EmailPartnerRequest();
    partner.setId(1L);
    partner.setName("Partner1");
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setPartners(Collections.singletonList(partner));
    BusinessException exception = assertThrows(
        BusinessException.class,
        () -> sendEmailHandler.validateEmailRecipients(emailComposed)
    );
    assertEquals(ErrorCode.EMAIL_PARTNER_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void shouldThrowExceptionWhenAddressesAreNonExistent() {
    EmailPartnerRequest partner = new EmailPartnerRequest();
    partner.setId(1L);
    partner.setName("Partner1");
    partner.setAddresses(List.of("<EMAIL>"));
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setPartners(Collections.singletonList(partner));
    emailComposed.setCc(Arrays.asList("<EMAIL>", "<EMAIL>"));
    EmailPartnerEntity emailPartnerEntity = new EmailPartnerEntity();
    emailPartnerEntity.setId(1L);
    emailPartnerEntity.setName("Partner1");
    when(emailPartnerService.findAllById(anyList())).thenReturn(
        List.of(emailPartnerEntity)
    );
    when(emailPartnerAddressService.findAllByEmailPartnerId(1L)).thenReturn(
        Collections.emptyList());
    BusinessException exception = assertThrows(
        BusinessException.class,
        () -> sendEmailHandler.validateEmailRecipients(emailComposed)
    );

    assertEquals(ErrorCode.EMAIL_ADDRESS_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void findValidFileResources_scuess() throws IOException, BusinessException {
    Path tempFile1 = Files.createTempFile("testFile1", ".txt");
    Path tempFile2 = Files.createTempFile("testFile2", ".txt");
    FileStorageModel fileStorage1 =
        new FileStorageModel(1L, tempFile1.getFileName().toString(), tempFile1.toFile()
            .length());
    FileStorageModel fileStorage2 =
        new FileStorageModel(2L, tempFile2.getFileName().toString(), tempFile2.toFile()
            .length());
    FileStorageEntity fileStorageEntity1 = new FileStorageEntity();
    fileStorageEntity1.setPath(tempFile1.toString());
    FileStorageEntity fileStorageEntity2 = new FileStorageEntity();
    fileStorageEntity2.setPath(tempFile2.toString());

    when(fileStorageService.findAllById(
        List.of(fileStorage1.getId(), fileStorage2.getId())))
        .thenReturn(List.of(
            fileStorageEntity1, fileStorageEntity2
        ));
    // Act
    List<File> validFiles =
        sendEmailHandler.findValidFileStorages(List.of(fileStorage1, fileStorage2));
    // Assert
    assertEquals(2, validFiles.size());
    assertEquals(tempFile1.toFile(), validFiles.get(0));
    assertEquals(tempFile2.toFile(), validFiles.get(1));
    // Cleanup
    Files.deleteIfExists(tempFile1);
    Files.deleteIfExists(tempFile2);
  }

  @Test
  void findValidFileResources_error() {
    Path tempFileNonExisting = Path.of("nonExistingFile.txt");
    FileStorageModel fileStorage3 =
        new FileStorageModel(3L, tempFileNonExisting.getFileName().toString(), 0L);
    FileStorageEntity fileStorageEntity3 = new FileStorageEntity();
    fileStorageEntity3.setPath(tempFileNonExisting.toString());
    when(fileStorageService.findAllById(
        List.of(fileStorage3.getId())))
        .thenReturn(List.of(fileStorageEntity3));
    // Act
    BusinessException exception = assertThrows(
        BusinessException.class,
        () -> sendEmailHandler.findValidFileStorages(List.of(fileStorage3))
    );
    assertEquals(ErrorCode.FILE_PATH_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void findValidFileResources_errorNotIsFile() throws IOException {
    Path tempDirectory = Files.createTempDirectory("testDirectory");
    FileStorageModel fileStorage3 =
        new FileStorageModel(3L, tempDirectory.getFileName().toString(), 0L);
    FileStorageEntity fileStorageEntity3 = new FileStorageEntity();
    fileStorageEntity3.setPath(tempDirectory.toString());
    when(fileStorageService.findAllById(
        List.of(fileStorage3.getId())))
        .thenReturn(List.of(fileStorageEntity3));
    // Act
    BusinessException exception = assertThrows(
        BusinessException.class,
        () -> sendEmailHandler.findValidFileStorages(List.of(fileStorage3))
    );
    assertEquals(ErrorCode.FILE_PATH_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void shouldHandleNonExistentFileStorage() {
    // Arrange
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setFileStorages(List.of(new FileStorageModel(1L, "invalidPath", null)));
    FileStorageEntity fileStorageEntity = new FileStorageEntity();
    fileStorageEntity.setPath("validPath1");
    Mockito.when(fileStorageService.findAllById(List.of(1L)))
        .thenReturn(List.of(fileStorageEntity));
    // Act
    List<File> validFileResources = new ArrayList<>();
    List<FileStorageEntity> fileStorages = fileStorageService.findAllById(
        emailComposed.getFileStorages().stream().map(FileStorageModel::getId).toList());
    for (FileStorageEntity fileStorage : fileStorages) {
      File file = new File(fileStorage.getPath());
      if (file.exists() && file.isFile()) {
        validFileResources.add(file);
      }
    }
    // Assert
    Assertions.assertTrue(validFileResources.isEmpty(), "No valid files should be added.");
  }

  @Test
  void shouldThrowExceptionWhenTooManyRecipients() {
    EmailPartnerRequest partner = new EmailPartnerRequest();
    partner.setId(1L);
    partner.setName("Partner1");
    partner.setAddresses(Arrays.asList("<EMAIL>", "<EMAIL>"));
    EmailComposedModel emailComposed = new EmailComposedModel();
    List<String> emailAddresses = new ArrayList<>();
    for (int i = 1; i <= 100; i++) {
      emailAddresses.add("receiver" + i + "@example.com");
    }
    emailComposed.setCc(Arrays.asList("<EMAIL>", "<EMAIL>"));
    emailComposed.setTo(emailAddresses);
    emailComposed.setPartners(Collections.singletonList(partner));
    EmailPartnerEntity emailPartnerEntity = new EmailPartnerEntity();
    emailPartnerEntity.setId(1L);
    emailPartnerEntity.setName("Partner1");
    when(emailPartnerService.findAllById(anyList())).thenReturn(
        List.of(emailPartnerEntity)
    );
    when(emailPartnerAddressService.findAllByEmailPartnerId(1L)).thenReturn(
        List.of(new EmailPartnerAddressEntity(1L, "<EMAIL>", 1L),
            new EmailPartnerAddressEntity(2L, "<EMAIL>", 1L))
    );

    BusinessException exception = assertThrows(
        BusinessException.class,
        () -> sendEmailHandler.validateEmailRecipients(emailComposed)
    );
    assertEquals(ErrorCode.EMAIL_RECEIVER_TOO_MUCH.getCode(), exception.getCode());
  }

  @Test
  void shouldPassValidationWithValidRecipients() {
    EmailPartnerRequest partner = new EmailPartnerRequest();
    partner.setId(1L);
    partner.setName("Partner1");
    partner.setAddresses(Arrays.asList("<EMAIL>", "<EMAIL>", null));
    EmailPartnerRequest partner2 = new EmailPartnerRequest();
    partner2.setId(1L);
    partner2.setName("Partner1");
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setTo(Arrays.asList("<EMAIL>", "<EMAIL>"));
    emailComposed.setPartners(List.of(partner, partner2));
    EmailPartnerEntity emailPartnerEntity = new EmailPartnerEntity();
    emailPartnerEntity.setId(1L);
    emailPartnerEntity.setName("Partner1");
    when(emailPartnerService.findAllById(anyList())).thenReturn(
        List.of(emailPartnerEntity)
    );
    when(emailPartnerAddressService.findAllByEmailPartnerId(1L)).thenReturn(
        List.of(new EmailPartnerAddressEntity(1L, "<EMAIL>", 1L),
            new EmailPartnerAddressEntity(2L, "<EMAIL>", 1L))
    );
    assertDoesNotThrow(() -> sendEmailHandler.validateEmailRecipients(emailComposed));
  }

  @Test
  void sendEmails_whenNoRecipients_shouldThrowBusinessException() {
    // Arrange
    EmailComposedModel emailComposed = new EmailComposedModel();
    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () ->
        sendEmailHandler.sendEmails(emailComposed, List.of())
    );
    // Verify
    verify(emailConfigService, never()).findByEmailAndProtocolType(any(), any());
    assertEquals(exception.getCode(), ErrorCode.EMAIL_RECEIVER_IS_EMPTY.getCode());
  }

  @Test
  void sendEmails_whenEmailConfigNotFound_shouldThrowBusinessException() {
    // Arrange
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setEmailSender("<EMAIL>");
    emailComposed.setTo(List.of("<EMAIL>"));
    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () ->
        sendEmailHandler.sendEmails(emailComposed, List.of())
    );
    // Verify
    assertEquals(exception.getCode(), ErrorCode.EMAIL_SENDER_NOT_FOUND.getCode());
  }


  @Test
  void sendEmails_FileSizeTooLarge_ThrowsException() {
    // Arrange
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setContent("Test Content");
    emailComposed.setTo(List.of("<EMAIL>"));
    MultipartFile mockFile = mock(MultipartFile.class);
    EmailConfigEntity emailConfig = new EmailConfigEntity();
    emailConfig.setEmail("<EMAIL>");
    emailConfig.setActive(true);
    when(emailConfigService.findByEmailAndProtocolType(any(), any()))
        .thenReturn(Optional.of(emailConfig));
    when(emailService.configSessionMailSender(any())).thenReturn(javaMailSender);
    BusinessException exception = assertThrows(BusinessException.class,
        () -> sendEmailHandler.sendEmails(emailComposed, List.of(mockFile)));
    assertEquals(exception.getCode(), ErrorCode.EMAIL_SENDING_ERROR.getCode());

  }


  @Test
  void sendEmails_withTooLargeEmailContent_shouldThrowBusinessException() {
    // Arrange
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setContent("A".repeat(1_000_000)); // Simulate a large email body
    emailComposed.setTo(List.of("<EMAIL>"));
    EmailConfigEntity emailConfig = new EmailConfigEntity();
    emailConfig.setEmail("<EMAIL>");
    emailConfig.setActive(true);
    when(emailConfigService.findByEmailAndProtocolType(any(), any()))
        .thenReturn(Optional.of(emailConfig));
    when(emailService.configSessionMailSender(any())).thenReturn(javaMailSender);
    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () ->
        sendEmailHandler.sendEmails(emailComposed, List.of())
    );
    // Verify
    assertEquals(exception.getCode(), ErrorCode.EMAIL_SENDING_ERROR.getCode());
  }

  @Test
  void sendEmails_emailConfigInactive_shouldThrowBusinessException() {
    // Arrange
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setContent("A".repeat(1_000_000)); // Simulate a large email body
    emailComposed.setTo(List.of("<EMAIL>"));
    EmailConfigEntity emailConfig = new EmailConfigEntity();
    emailConfig.setEmail("<EMAIL>");
    when(emailConfigService.findByEmailAndProtocolType(any(), any()))
        .thenReturn(Optional.of(emailConfig));
    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () ->
        sendEmailHandler.sendEmails(emailComposed, List.of())
    );
    // Verify
    assertEquals(exception.getCode(), ErrorCode.EMAIL_CONFIG_INACTIVE.getCode());
  }

  @Test
  void sendEmails_whenAttachmentNotFound_shouldSkipAttachment() throws Exception {
    // Arrange
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setEmailSender("<EMAIL>");
    emailComposed.setTo(List.of("<EMAIL>"));
    emailComposed.setSubject("Test Subject");
    emailComposed.setContent("Test Content");
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setActive(true);
    when(emailConfigService.findByEmailAndProtocolType(any(), eq(EmailProtocolTypeEnum.SMTP)))
        .thenReturn(Optional.of(emailConfigEntity));
    when(emailService.configSessionMailSender(any())).thenReturn(javaMailSender);
    when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
    MultipartFile mockFile = mock(MultipartFile.class);
    when(mockFile.getOriginalFilename()).thenReturn("mockFile.txt");
    when(mockFile.getBytes()).thenReturn("".getBytes());
    emailConfigEntity.setActive(true);
    // Act
    sendEmailHandler.sendEmails(emailComposed, List.of(mockFile));
    verify(javaMailSender).send(mimeMessage);
  }

  @Test
  void sendSingleEmail_allRecipientsPresent_sendsEmail()
      throws MessagingException, IOException, BusinessException {
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setTo(List.of("<EMAIL>", "<EMAIL>"));
    EmailPartnerRequest partner1 = new EmailPartnerRequest();
    partner1.setAddresses(List.of("<EMAIL>"));
    EmailPartnerRequest partner2 = new EmailPartnerRequest();
    partner2.setAddresses(List.of("<EMAIL>", "<EMAIL>"));
    EmailPartnerRequest partner3 = new EmailPartnerRequest();
    partner3.setAddresses(List.of());
    emailComposed.setPartners(List.of(partner1, partner2, partner3));
    List<MultipartFile> validFilesUpload = Collections.emptyList();
    List<File> validFileResources = Collections.emptyList();
    MimeMessageHelper helper = mock(MimeMessageHelper.class);
    when(helper.getMimeMessage()).thenReturn(mimeMessage);
    doNothing().when(javaMailSender).send(mimeMessage);
    SendEmailHandler spyService = spy(sendEmailHandler);
    doReturn(helper).when(spyService).builderHelper(
        any(EmailComposedModel.class),
        any(JavaMailSender.class),
        any(List.class),
        any(List.class)
    );
    assertDoesNotThrow(
        () -> spyService.sendSingleEmail(emailComposed, javaMailSender, validFilesUpload,
            validFileResources));
    // Verify the email was sent
    verify(javaMailSender).send(mimeMessage);
  }

  @Test
  void sendSingleEmail_noRecipients_doesNotSendEmail()
      throws MessagingException, IOException, BusinessException {
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setTo(List.of());
    emailComposed.setPartners(List.of());
    emailComposed.setIsOneEmail(true);
    List<MultipartFile> validFilesUpload = Collections.emptyList();
    List<File> validFileResources = List.of();
    MimeMessageHelper helper = mock(MimeMessageHelper.class);
    SendEmailHandler spyService = spy(sendEmailHandler);
    doReturn(helper).when(spyService).builderHelper(
        any(EmailComposedModel.class),
        any(JavaMailSender.class),
        any(List.class),
        any(List.class)
    );
    assertDoesNotThrow(
        () -> spyService.sendSingleEmail(emailComposed, javaMailSender, validFilesUpload,
            validFileResources));
    // Since no recipients are present, setTo should not be called
    verify(javaMailSender, never()).send(any(MimeMessage.class));
  }

  @Test
  void sendSingleEmail_emptyPartnersAndTo_sendsEmptyEmail()
      throws MessagingException, IOException, BusinessException {
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setTo(List.of());
    emailComposed.setPartners(Collections.emptyList());
    emailComposed.setIsOneEmail(true);
    List<MultipartFile> validFilesUpload = Collections.emptyList();
    List<File> validFileResources = Collections.emptyList();
    MimeMessageHelper helper = mock(MimeMessageHelper.class);
    SendEmailHandler spyService = spy(sendEmailHandler);
    doReturn(helper).when(spyService).builderHelper(
        any(EmailComposedModel.class),
        any(JavaMailSender.class),
        any(List.class),
        any(List.class)
    );
    assertDoesNotThrow(
        () -> spyService.sendSingleEmail(emailComposed, javaMailSender, validFilesUpload,
            validFileResources));
    verify(javaMailSender, never()).send(any(MimeMessage.class));
  }

  @Test
  void sendSeparateEmails_validPartners_sendsEmails()
      throws MessagingException, IOException, BusinessException {
    Path tempFile1 = Files.createTempFile("testFile1", ".txt");
    Path tempFile2 = Files.createTempFile("testFile2", ".txt");
    Path tempFileNonExisting = Path.of("nonExistingFile.txt"); // Non-existing file

    FileStorageModel fileStorage1 =
        new FileStorageModel(1L, tempFile1.getFileName().toString(), tempFile1.toFile()
            .length());
    FileStorageModel fileStorage2 =
        new FileStorageModel(2L, tempFile2.getFileName().toString(), tempFile2.toFile()
            .length());
    FileStorageModel fileStorage3 =
        new FileStorageModel(3L, tempFileNonExisting.getFileName().toString(), 0L);

    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setTo(List.of("<EMAIL>"));
    emailComposed.setIsOneEmail(false);
    emailComposed.setFileStorages(List.of(fileStorage1, fileStorage2, fileStorage3));
    EmailPartnerRequest partner1 = new EmailPartnerRequest();
    partner1.setAddresses(List.of("<EMAIL>"));
    EmailPartnerRequest partner2 = new EmailPartnerRequest();
    partner2.setAddresses(List.of("<EMAIL>"));
    emailComposed.setPartners(List.of(partner1, partner2));
    MimeMessageHelper helper = mock(MimeMessageHelper.class);
    SendEmailHandler spyService = spy(sendEmailHandler);
    doReturn(helper).when(spyService).builderHelper(
        any(EmailComposedModel.class),
        any(JavaMailSender.class),
        anyList(),
        anyList()
    );

    when(helper.getMimeMessage()).thenReturn(mimeMessage);
    assertDoesNotThrow(() -> spyService.findValidFileStorages(emailComposed.getFileStorages()));
    assertDoesNotThrow(
        () -> spyService.sendSeparateEmails(emailComposed, javaMailSender, Collections.emptyList(),
            Collections.emptyList()));
    // Verify interactions
    verify(helper, times(2)).setTo(any(String[].class));
    verify(javaMailSender, times(2)).send(mimeMessage);
  }

  @Test
  void sendSeparateEmails_emptyPartners_noEmailsSent() {
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setTo(List.of());
    emailComposed.setPartners(Collections.emptyList());
    SendEmailHandler spyService = spy(sendEmailHandler);
    // Act & Assert
    assertDoesNotThrow(
        () -> spyService.sendSeparateEmails(emailComposed, javaMailSender, Collections.emptyList(),
            Collections.emptyList()));
    // Verify no emails were sent
    verify(javaMailSender, never()).send(any(MimeMessage.class));
  }

  @Test
  void sendSeparateEmails_partnerWithNoAddresses_doesNotSendEmail() {
    EmailComposedModel emailComposed = new EmailComposedModel();
    EmailPartnerRequest partner = new EmailPartnerRequest();
    partner.setAddresses(Collections.emptyList());
    emailComposed.setPartners(List.of(partner));
    SendEmailHandler spyService = spy(sendEmailHandler);
    // Act & Assert
    assertDoesNotThrow(
        () -> spyService.sendSeparateEmails(emailComposed, javaMailSender, Collections.emptyList(),
            Collections.emptyList()));
    // Verify no emails were sent
    verify(javaMailSender, never()).send(any(MimeMessage.class));
  }

  @Test
  void sendSeparateEmails_exceptionDuringEmailSending_logsError()
      throws MessagingException, IOException, BusinessException {
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setTo(List.of("<EMAIL>"));
    EmailPartnerRequest partner = new EmailPartnerRequest();
    partner.setAddresses(List.of("<EMAIL>"));
    emailComposed.setPartners(List.of(partner));
    MimeMessageHelper helper = mock(MimeMessageHelper.class);
    SendEmailHandler spyService = spy(sendEmailHandler);
    // Mock builderHelper
    doReturn(helper).when(spyService).builderHelper(
        any(EmailComposedModel.class),
        any(JavaMailSender.class),
        anyList(),
        anyList()
    );
    // Mock exception during send
    when(helper.getMimeMessage()).thenReturn(mimeMessage);
    doThrow(new RuntimeException("Email sending failed")).when(javaMailSender)
        .send(any(MimeMessage.class));
    // Act
    RuntimeException exception = assertThrows(RuntimeException.class, () ->
        spyService.sendSeparateEmails(emailComposed, javaMailSender, Collections.emptyList(),
            Collections.emptyList())
    );
    // Assert
    assertTrue(exception.getCause().getMessage().contains("Email sending failed"));
    // Verify send was attempted
    verify(javaMailSender, times(1)).send(mimeMessage);
  }

  @Test
  void builderHelper_success() throws Exception {
    // Arrange
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setSubject("Test Subject");
    emailComposed.setContent("<p>Test Content</p>");
    emailComposed.setEmailSender("<EMAIL>");
    emailComposed.setCc(Arrays.asList("<EMAIL>", "<EMAIL>"));
    MultipartFile mockFile = mock(MultipartFile.class);
    when(mockFile.getOriginalFilename()).thenReturn("mockFile.txt");
    when(mockFile.getBytes()).thenReturn("File Content".getBytes());
    when(mockFile.getContentType()).thenReturn("text/plain");
    List<MultipartFile> validFilesUpload = List.of(mockFile);
    File tempFile = File.createTempFile("testFileResource", ".txt");
    Files.write(tempFile.toPath(), "File Resource Content".getBytes());
    List<File> validFileResources = List.of(tempFile);
    JavaMailSender mailSender = mock(JavaMailSender.class);
    MimeMessage mimeMessage = mock(MimeMessage.class);
    when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
    sendEmailHandler.builderHelper(emailComposed, mailSender, validFilesUpload, validFileResources);
    verify(mockFile, times(1)).getOriginalFilename();
    verify(mockFile, times(1)).getBytes();
    Files.deleteIfExists(Path.of(tempFile.getPath()));
  }


  @Test
  void sendSepeEmail_allRecipientsPresent_sendsEmail()
      throws MessagingException, IOException, BusinessException {
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setTo(List.of("<EMAIL>", "<EMAIL>"));
    EmailPartnerRequest partner1 = new EmailPartnerRequest();
    partner1.setAddresses(List.of("<EMAIL>"));
    EmailPartnerRequest partner2 = new EmailPartnerRequest();
    partner2.setAddresses(List.of("<EMAIL>", "<EMAIL>"));
    EmailPartnerRequest partner3 = new EmailPartnerRequest();
    partner3.setAddresses(List.of());
    emailComposed.setPartners(List.of(partner1, partner2, partner3));
    emailComposed.setIsOneEmail(false);
    List<MultipartFile> validFilesUpload = Collections.emptyList();
    List<File> validFileResources = Collections.emptyList();
    MimeMessageHelper helper = mock(MimeMessageHelper.class);
    when(helper.getMimeMessage()).thenReturn(mimeMessage);
    doNothing().when(javaMailSender).send(mimeMessage);

    // Mock builderHelper method
    SendEmailHandler spyService = spy(sendEmailHandler);
    doReturn(helper).when(spyService).builderHelper(
        any(EmailComposedModel.class),
        any(JavaMailSender.class),
        any(List.class),
        any(List.class)
    );
    assertDoesNotThrow(
        () -> spyService.sendSeparateEmails(emailComposed, javaMailSender, validFilesUpload,
            validFileResources));
    // Verify the email was sent
    verify(spyService, times(1)).sendSeparateEmails(emailComposed, javaMailSender, validFilesUpload,
        validFileResources);  // Verify that send is called exactly 3 times
    verify(javaMailSender, times(3)).send(
        mimeMessage);  // Verify that send is called exactly 3 times
  }


  @Test
  void sendEmails_MultiplePartners() throws Exception {
    // Arrange
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setEmailSender("<EMAIL>");
    emailComposed.setSubject("Test Subject");
    emailComposed.setContent("Test Content");
    emailComposed.setIsOneEmail(false);
    emailComposed.setFileStorages(List.of(new FileStorageModel(1L, "invalidPath", null)));
    // Partners setup
    EmailPartnerRequest partner1 = new EmailPartnerRequest();
    partner1.setId(1L);
    partner1.setName("Partner1");
    partner1.setAddresses(List.of("<EMAIL>"));
    EmailPartnerRequest partner2 = new EmailPartnerRequest();
    partner2.setId(2L);
    partner2.setName("Partner2");
    partner2.setAddresses(List.of("<EMAIL>"));
    emailComposed.setPartners(List.of(partner1, partner2));
    EmailPartnerEntity partnerEntity1 = new EmailPartnerEntity();
    partnerEntity1.setId(1L);
    partnerEntity1.setName("Partner1");
    EmailPartnerEntity partnerEntity2 = new EmailPartnerEntity();
    partnerEntity2.setId(2L);
    partnerEntity2.setName("Partner2");
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setPassword("encrypted-password");
    emailConfigEntity.setActive(true);
    when(emailConfigService.findByEmailAndProtocolType("<EMAIL>",
        EmailProtocolTypeEnum.SMTP))
        .thenReturn(Optional.of(emailConfigEntity));
    when(emailService.configSessionMailSender(any(EmailConfigModel.class))).thenReturn(
        javaMailSender);
    when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
    when(emailPartnerService.findAllById(anyList()))
        .thenReturn(List.of(partnerEntity1, partnerEntity2));

    when(emailPartnerAddressService.findAllByEmailPartnerId(1L))
        .thenReturn(List.of(new EmailPartnerAddressEntity(1L, "<EMAIL>", 1L)));
    when(emailPartnerAddressService.findAllByEmailPartnerId(2L))
        .thenReturn(List.of(new EmailPartnerAddressEntity(2L, "<EMAIL>", 2L)));
    sendEmailHandler.sendEmails(emailComposed, null);
    verify(emailConfigService, times(1)).findByEmailAndProtocolType("<EMAIL>",
        EmailProtocolTypeEnum.SMTP);
    verify(emailPartnerService, times(1)).findAllById(List.of(1L, 2L));
    verify(emailPartnerAddressService, times(1)).findAllByEmailPartnerId(1L);
    verify(emailPartnerAddressService, times(1)).findAllByEmailPartnerId(2L);
    verify(javaMailSender, times(2)).send(any(MimeMessage.class));
  }


  @Test
  void sendEmails_SinglePartners() throws Exception {
    // Arrange
    EmailComposedModel emailComposed = new EmailComposedModel();
    emailComposed.setEmailSender("<EMAIL>");
    emailComposed.setTo(Collections.singletonList("<EMAIL>"));
    emailComposed.setSubject("Test Subject");
    emailComposed.setContent("Test Content");
    emailComposed.setIsOneEmail(true);
    emailComposed.setFileStorages(List.of(new FileStorageModel(1L, "invalidPath", null)));
    EmailPartnerRequest partner1 = new EmailPartnerRequest();
    partner1.setId(1L);
    partner1.setName("Partner1");
    partner1.setAddresses(List.of("<EMAIL>"));
    EmailPartnerEntity partnerEntity1 = new EmailPartnerEntity();
    partnerEntity1.setId(1L);
    partnerEntity1.setName("Partner1");
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setPassword("encrypted-password");
    emailConfigEntity.setActive(true);
    when(emailConfigService.findByEmailAndProtocolType("<EMAIL>",
        EmailProtocolTypeEnum.SMTP))
        .thenReturn(Optional.of(emailConfigEntity));

    when(emailService.configSessionMailSender(any(EmailConfigModel.class))).thenReturn(
        javaMailSender);
    when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
    sendEmailHandler.sendEmails(emailComposed, null);

    verify(emailConfigService, times(1)).findByEmailAndProtocolType("<EMAIL>",
        EmailProtocolTypeEnum.SMTP);
    verify(javaMailSender, times(1)).send(any(MimeMessage.class));
  }

  @Test
  void testProcessHtmlContentWithBase64Images() throws Exception {
    MimeMessage mimeMessage = new MimeMessage((Session) null);
    MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
    MimeMultipart parentMultipart = new MimeMultipart();
    String htmlContent =
        "<html><body><img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA'/></body></html>";
    sendEmailHandler.processHtmlContentWithBase64Images(htmlContent, helper, parentMultipart);
    mimeMessage.setContent(parentMultipart);
    assertTrue(mimeMessage.getContent() instanceof MimeMultipart);
    assertEquals(1, parentMultipart.getCount());
    MimeMultipart relatedMultipart = (MimeMultipart) parentMultipart.getBodyPart(0).getContent();
    assertEquals(2, relatedMultipart.getCount()); // 1 phần text + 1 hình ảnh
  }


  @Test
  void testProcessHtmlContentWithBase64Images_ValidateArguments() throws MessagingException {
    MimeMessageHelper mockHelper = mock(MimeMessageHelper.class);
    String htmlContent =
        "<html><body><img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA'/></body></html>";
    MimeMultipart parentMultipart = new MimeMultipart();
    sendEmailHandler.processHtmlContentWithBase64Images(htmlContent, mockHelper, parentMultipart);
    ArgumentCaptor<String> cidCaptor = ArgumentCaptor.forClass(String.class);
    ArgumentCaptor<DataSource> dataSourceCaptor = ArgumentCaptor.forClass(DataSource.class);
    verify(mockHelper).addInline(cidCaptor.capture(), dataSourceCaptor.capture());
    String capturedCid = cidCaptor.getValue();
    DataSource capturedDataSource = dataSourceCaptor.getValue();
    assertTrue(capturedCid.startsWith("image"));
    assertTrue(capturedDataSource.getContentType().startsWith("image/"));
  }

  @Test
  void testExecutorShutdown() throws InterruptedException {
    ExecutorService mockExecutor = Mockito.mock(ExecutorService.class);
    Logger mockLogger = Mockito.mock(Logger.class);
    when(mockExecutor.awaitTermination(60, TimeUnit.SECONDS)).thenReturn(true);
    assertDoesNotThrow(() -> {
      mockExecutor.shutdown();
      if (!mockExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
        mockExecutor.shutdownNow();
        if (!mockExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
          mockLogger.error("Executor did not terminate");
        }
      }
    });
    verify(mockExecutor, times(1)).shutdown();
    verify(mockExecutor, times(1)).awaitTermination(60, TimeUnit.SECONDS);
    verify(mockLogger, never()).error("Executor did not terminate");
  }

}