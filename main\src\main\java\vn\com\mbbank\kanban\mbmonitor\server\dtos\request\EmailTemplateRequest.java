package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailTemplateModel;

/**
 * Model view attribute to create email template.
 */

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailTemplateRequest extends EmailTemplateModel {

}
