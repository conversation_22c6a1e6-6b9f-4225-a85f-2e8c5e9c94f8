package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils.formatDuration;

import java.io.IOException;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.enums.SortType;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ExportDataResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NoteEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileSourceEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.ExportDataResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.factory.FileExporterFactory;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.ExportDataMessageModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertCursor;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ExportFileAlertRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AlertResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.NoteResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AlertRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExportDataService;
import vn.com.mbbank.kanban.mbmonitor.server.services.NoteService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;


/**
 * Service Logic Alert service.
 */
@Service
@RequiredArgsConstructor
public class AlertServiceImpl extends BaseServiceImpl<AlertEntity, Long>
    implements AlertService {

  private static final Logger logger = LoggerFactory.getLogger(AlertServiceImpl.class);
  private final AlertRepository alertRepository;
  private final ApplicationService applicationService;
  private final ServiceService serviceService;
  private final AlertPriorityConfigService alertPriorityConfigService;
  private final NoteService noteService;
  private final ExportDataService exportDataService;
  private final CommonKafkaProducerService commonKafkaProducerService;
  private final NoteResponseMapper noteResponseMapper = NoteResponseMapper.INSTANCE;

  protected JpaCommonRepository<AlertEntity, Long> getRepository() {
    return alertRepository;
  }

  @Override
  @Transactional(readOnly = true)
  public CursorPageResponse<AlertResponse, AlertCursor> findAll(AlertPaginationRequest paginationRequest) {
    var result = alertRepository.findAll(paginationRequest);
    if (CollectionUtils.isEmpty(result.getData())) {
      return result;
    }
    var groupIdSet = result.getData().stream().map(AlertResponse::getAlertGroupId)
        .filter(groupId -> !AlertConstants.DEFAULT_ALERT_GROUP_ID.equals(groupId))
        .collect(Collectors.toSet());
    var noteMap =
        noteService.findAllByAlertGroupIdInOrderByCreatedDateDesc(groupIdSet.stream().toList()).stream().collect(
            Collectors.groupingBy(NoteEntity::getAlertGroupId));
    for (AlertResponse alert : result.getData()) {
      var notes = noteMap.get(alert.getAlertGroupId());
      if (CollectionUtils.isNotEmpty(notes)) {
        alert.setNotes(noteResponseMapper.map(notes));
      }
      // format duration
      try {
        var duration = Long.parseLong(alert.getClosedDuration());
        alert.setClosedDuration(formatDuration(duration, ChronoUnit.SECONDS));
      } catch (NumberFormatException e) {
        alert.setClosedDuration("");
      }
    }
    return result;
  }

  @Override
  @Transactional(rollbackFor = BusinessException.class)
  public ExportDataResponse exportFile(ExportFileAlertRequest request)
      throws IOException, BusinessException {
    var exportDataModel = request.getExportDataModel();
    exportDataModel.setFileName(FileExporterFactory.generateFileExportName(ExportFileSourceEnum.ALERT.name(),
        exportDataModel.getFileName()));

    var exportDataEntity = exportDataService.save(exportDataModel);
    request.setExportDataModel(exportDataModel);
    var exportDataMessageModel = ExportDataMessageModel.builder().id(exportDataEntity.getId())
        .source(ExportFileSourceEnum.ALERT).request(request).build();
    var data = new BaseKafkaModel<ExportDataMessageModel>();
    data.setType(KafkaTypeEnum.EXPORT_DATA);
    data.setValue(exportDataMessageModel);
    commonKafkaProducerService.sendAndWait(data);
    return ExportDataResponseMapper.INSTANCE.map(exportDataEntity);
  }

  @Override
  public List<AlertEntity> findTopAlertsByAlertGroupIdNullAndStatus(AlertStatusEnum status,
                                                                    int numberOfResult) {
    return alertRepository.findTopAlertsByAlertGroupIdNullAndStatus(status, numberOfResult);
  }

  @Override
  public List<AlertEntity> findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(
      List<String> serviceIds,
      List<String> applicationIds,
      int alertGroupHandleTriggerInterval,
      AlertGroupStatusEnum status) {
    return alertRepository.findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(
        serviceIds,
        applicationIds,
        alertGroupHandleTriggerInterval, status);
  }

  @Override
  public List<AlertEntity> findAllByAlertGroupIdIn(List<Long> alertGroupIds) {
    return alertRepository.findAllByAlertGroupIdIn(alertGroupIds);
  }

  @Override
  public Page<AlertEntity> findAllByAlertGroupIdIn(List<Long> alertGroupIds,
                                                   PaginationRequestDTO paginationRequest) {
    var sort = SortType.ASC.equals(paginationRequest.getSortOrder())
        ? Sort.by(Sort.Direction.ASC, paginationRequest.getSortBy()) :
        Sort.by(Sort.Direction.DESC, paginationRequest.getSortBy());
    return alertRepository.findAllByAlertGroupIdIn(alertGroupIds,
        PageRequest.of(paginationRequest.getPage(), paginationRequest.getSize(), sort));
  }

  @Override
  public Long countAllAlert(AlertPaginationRequest paginationRequest) {
    return alertRepository.countAllAlert(paginationRequest);
  }

  @Override
  public List<AlertEntity> findAllByAlertGroupIdInAndStatus(List<Long> alertGroupIds, AlertStatusEnum alertStatus) {
    return alertRepository.findAllByAlertGroupIdInAndStatus(alertGroupIds, alertStatus);
  }


}
