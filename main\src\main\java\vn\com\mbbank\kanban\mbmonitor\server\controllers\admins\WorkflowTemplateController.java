package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.WorkflowTemplateRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.WorkflowTemplateResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.WorkflowTemplateResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.services.WorkflowTemplateService;

/**
 * WorkflowTemplateController.
 */
@RestController
@RequestMapping(ServerUrl.WORKFLOW_TEMPLATE)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class WorkflowTemplateController extends BaseController {
  private final WorkflowTemplateService workflowTemplateService;
  private final WorkflowTemplateResponseMapper workflowTemplateResponseMapper =
      WorkflowTemplateResponseMapper.INSTANCE;

  /**
   * Api find config by ID.
   *
   * @param id id of config.
   * @return WorkflowTemplateResponse.
   */
  @GetMapping(value = "/{id}")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.IT_SERVICE_MANAGEMENT, action = PermissionActionEnum.VIEW)
  })
  ResponseData<WorkflowTemplateResponse> findById(@PathVariable String id)
      throws BusinessException {
    return ResponseUtils.success(workflowTemplateService.findWithId(id));
  }

  /**
   * Api find a list of config.
   *
   * @param request search
   * @return Page of WorkflowTemplateResponse
   */
  @GetMapping(value = "/with-paging")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.IT_SERVICE_MANAGEMENT, action = PermissionActionEnum.VIEW)
  })
  ResponseData<Page<WorkflowTemplateResponse>> findAllWithPaging(@ModelAttribute PaginationRequestDTO request)
      throws BusinessException {
    request.setPropertiesSearch(List.of("name", "description"));
    request.setSortBy("createdDate");
    return ResponseUtils.success(
        workflowTemplateService.findAllWithPaging(request).map(workflowTemplateResponseMapper::map));
  }

  /**
   * Api save config.
   *
   * @param request input data.
   * @return WorkflowTemplateResponse.
   * @throws BusinessException exception
   */
  @PostMapping
  ResponseData<WorkflowTemplateResponse> save(
      @Valid @RequestBody WorkflowTemplateRequest request) throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.IT_SERVICE_MANAGEMENT,
        PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(request.getId()));
    return ResponseUtils.success(workflowTemplateResponseMapper.map(workflowTemplateService.createOrUpdate(request)));
  }

  /**
   * Api delete config.
   *
   * @param id config id.
   * @return String
   */
  @DeleteMapping(value = "/{id}")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.IT_SERVICE_MANAGEMENT, action = PermissionActionEnum.DELETE)
  })
  ResponseData<String> deleteById(@PathVariable String id) throws BusinessException {
    workflowTemplateService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }
}
