package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.util.regex.Pattern;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanStringFormatter;
import vn.com.mbbank.kanban.core.utils.KanbanStringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/24/2024
 */
public class StringUtils {
  private static final Pattern FILENAME_PATTERN = Pattern
      .compile("^[\\p{L}0-9._ -]+$", Pattern.UNICODE_CHARACTER_CLASS);

  private static final Pattern FILENAME_SAFE_REGEX = Pattern.compile("[^\\p{L}0-9._-]");
  private static final String FILENAME_SAFE_REPLACEMENT = "_";

  /**
   * Capitalizes the first letter of the input string.
   *
   * @param input The string to capitalize.
   * @return A string with the first letter capitalized.
   */
  public static String capitalizeFirstLetter(String input) {
    return KanbanStringFormatter.newBuilder().blankIfNull().trim().capitalizeFirst(true).build()
        .format(input).toString();
  }

  /**
   * Sanitizes the given file name by removing or replacing invalid characters.
   *
   * @param originalFilename the original file name to sanitize
   * @return a safe and sanitized file name
   * @throws BusinessException if the file name is invalid or cannot be sanitized
   */
  public static String sanitizeFileName(String originalFilename) throws BusinessException {
    String cleanFileName = org.springframework.util.StringUtils.cleanPath(originalFilename);
    if (KanbanStringUtils.isNullOrEmpty(originalFilename)
        || cleanFileName.contains("..") || cleanFileName.contains("/") || cleanFileName.contains("\\")
        || !FILENAME_PATTERN.matcher(cleanFileName).matches()) {
      throw new BusinessException(ErrorCode.INVALID_FILE_NAME);
    }
    return FILENAME_SAFE_REGEX.matcher(cleanFileName).replaceAll(FILENAME_SAFE_REPLACEMENT);
  }

}