package vn.com.mbbank.kanban.mbmonitor.common.utils.condition;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/18/2024
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
@JsonSubTypes({@JsonSubTypes.Type(RuleCondition.class), @JsonSubTypes.Type(RuleGroupType.class)})
public abstract class RuleElement {

  /**
   * check validate rule.
   *
   * @param object data of object is checked.
   * @return true/false
   */
  public abstract boolean check(Object object);

  /**
   * Check validate rule with object data and service function.
   *
   * @param object data of object to check.
   * @return true/false
   */
  public abstract boolean check(Map<String, Object> object);

  /**
   * Check validate rule with object data and service function.
   *
   * @param object data of object to check.
   * @param func   function to fetch additional values for validation.
   * @param <V>    value.
   * @return true/false
   */
  public abstract <V> boolean check(Object object, Function<Object, V> func);

  /**
   * Check validate rule with object data and service function.
   *
   * @param id id of priority.
   * @return true/false
   */
  public abstract boolean checkPriority(Long id);


  /**
   * Validates a rule with the given custom object ID.
   *
   * @param id the ID of the custom object to validate.
   * @return {@code true} if the rule is valid; {@code false} otherwise.
   */
  public abstract boolean checkCustomObject(Long id);

  /**
   * Get customObjectIds compare with field name of class.
   *
   * @param clazz to get field name
   * @return list id
   */
  public abstract List<Long> getCustomObjectIds(Class<?> clazz);

  /**
   * Get customObjectIds compare with field name.
   *
   * @param fieldNames to get field name
   * @return list id
   */
  public abstract List<Long> getCustomObjectIds(Set<String> fieldNames);

  /**
   * set value for rule element.
   *
   * @param newVal to set
   */
  public abstract void setValueFromExternal(Object newVal);
}
