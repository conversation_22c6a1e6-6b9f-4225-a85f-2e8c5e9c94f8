package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionApiEntity;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ExecutionApiRepository;
import java.util.Arrays;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith({MockitoExtension.class})
class ExecutionApiServiceImplTest {

  @Mock
  private ExecutionApiRepository executionApiRepository;

  @InjectMocks
  @Spy
  ExecutionApiServiceImpl executionApiService;

  @Test
  void getRepository_success() {
    JpaCommonRepository<ExecutionApiEntity, String> result = executionApiService.getRepository();
    assertEquals(executionApiRepository, result);
  }

  @Test
  void findApiInfoByExecutionId_success() {
    String executionId = "exec-1";
    ExecutionApiEntity mockEntity = new ExecutionApiEntity();
    when(executionApiRepository.findAllByExecutionId(executionId)).thenReturn(mockEntity);

    ExecutionApiEntity result = executionApiService.findApiInfoByExecutionId(executionId);

    assertEquals(mockEntity, result);
    verify(executionApiRepository).findAllByExecutionId(executionId);
  }

  @Test
  void deleteApiInfoByExecutionId_success() {
    String executionId = "exec-2";

    executionApiService.deleteApiInfoByExecutionId(executionId);

    verify(executionApiRepository).deleteAllByExecutionId(executionId);
  }

  @Test
  void findAllByExecutionIdIn_success() {
    List<String> ids = Arrays.asList("id1", "id2");
    List<ExecutionApiEntity> expected = Arrays.asList(new ExecutionApiEntity(), new ExecutionApiEntity());

    when(executionApiRepository.findAllByExecutionIdIn(ids)).thenReturn(expected);

    List<ExecutionApiEntity> result = executionApiService.findAllByExecutionIdIn(ids);

    assertEquals(expected, result);
    verify(executionApiRepository).findAllByExecutionIdIn(ids);
  }

}