package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysPermissionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ExecutionRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionGroupService;
import vn.com.mbbank.kanban.mbmonitor.server.services.SysPermissionService;
import vn.com.mbbank.kanban.mbmonitor.server.services.SysUserService;

@Service
@RequiredArgsConstructor
public class ExecutionDependencyServiceImpl extends BaseServiceImpl<ExecutionEntity, String> implements
    ExecutionDependencyService {
  private final ExecutionRepository executionRepository;
  private final ExecutionGroupService executionGroupService;
  private final SysPermissionService sysPermissionService;
  private final CommonAclPermissionService commonAclPermissionService;
  private final SysUserService sysUserService;

  @Override
  protected JpaCommonRepository<ExecutionEntity, String> getRepository() {
    return executionRepository;
  }

  @Override
  public List<ExecutionEntity> findAllWithPermissionByExecutionGroupId(String executionGroupId)
      throws BusinessException {
    var executionGroup = executionGroupService.findById(executionGroupId);
    if (Objects.isNull(executionGroup)) {
      throw new BusinessException(ErrorCode.EXECUTION_GROUP_NOT_FOUND);
    }
    var hasPermissionInGroup = commonAclPermissionService.isSupperAdmin();
    var executionIds = new ArrayList<String>();
    if (!hasPermissionInGroup) {
      var user = sysUserService.findByUserName(getUserName()).orElseThrow(() ->
          new BusinessException(ErrorCode.USER_NOT_FOUND));
      var executePermission = sysPermissionService.findAllByUserIdAndModuleInAndActionIn(user.getId(),
          List.of(PermissionModuleEnum.RUN_EXECUTION), List.of(PermissionActionEnum.EXECUTE));
      for (SysPermissionEntity permission : executePermission) {
        if (Objects.equals(permission.getModuleId(), executionGroupId)
            && KanbanCommonUtil.isEmpty(permission.getModuleParentId())) {
          hasPermissionInGroup = true;
          break;
        } else if (!KanbanCommonUtil.isNullOrEmpty(permission.getModuleParentId())
            && permission.getModuleParentId().equals(executionGroupId)) {
          executionIds.add(permission.getModuleId());
        }
      }
    }
    return hasPermissionInGroup
        ? executionRepository.findAllByExecutionGroupId(executionGroupId)
        : executionRepository.findAllByIdIn(executionIds);
  }

}
