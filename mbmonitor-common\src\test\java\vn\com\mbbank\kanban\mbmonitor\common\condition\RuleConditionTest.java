package vn.com.mbbank.kanban.mbmonitor.common.condition;


import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OperatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleCondition;

public class RuleConditionTest {

  private RuleCondition<Object> ruleCondition;


  @BeforeEach
  void setup() {
    ruleCondition = RuleCondition.builder()
        .field("testField")
        .operator(OperatorEnum.IS)
        .value("testValue")
        .build();
  }

  @Test
  void check_fieldValuePresent_shouldReturnTrue() {
    TestObject testObject = new TestObject("");
    Assertions.assertFalse(ruleCondition.check(testObject, obj -> null));
  }

  @Test
  void check_fieldValueMissing_useFunctionToRetrieveValue_shouldReturnTrue() {
    TestObject testObject = new TestObject("123");
    ruleCondition.setField("nonExistentField");
    Function<Object, String> func = Mockito.mock(Function.class);
    Mockito.when(func.apply(ruleCondition.getField())).thenReturn("testValue");
    Assertions.assertTrue(ruleCondition.check(testObject, func));
    Mockito.verify(func, Mockito.times(1)).apply(ruleCondition.getField());
  }

  @Test
  void getFieldExistsInSuperClass() {
    RuleCondition<String> ruleCondition = RuleCondition.<String>builder()
        .field("field1")
        .build();

    SubClass subObject = new SubClass();
    Optional<Object> result = ruleCondition.getFieldValue(subObject);

    Assertions.assertTrue(result.isPresent());
  }

  @Test
  void check_success_validField() {
    TestObject testObject = new TestObject("testValue");

    Assertions.assertFalse(ruleCondition.check(testObject), "The field value should match the condition.");
  }


  @Test
  void check_success_objectNull2() {
    TestObject testObject = null;

    Assertions.assertFalse(ruleCondition.check(testObject));
  }

  @Test
  void check_success_invalidField() {
    TestObject testObject = new TestObject("wrongValue");

    Assertions.assertFalse(ruleCondition.check(testObject), "The field value should not match the condition.");
  }

  @Test
  void getFieldValue_success() {
    TestObject testObject = new TestObject("testValue");

    Optional<Object> fieldValue = ruleCondition.getFieldValue(testObject);
    Assertions.assertFalse(fieldValue.isPresent(), "Field value should be present.");
  }

  @Test
  void getFieldValue_error_notField() {
    TestObject testObject = new TestObject("testValue");

    ruleCondition.setField("nonExistingField");
    Optional<Object> fieldValue = ruleCondition.getFieldValue(testObject);
    Assertions.assertFalse(fieldValue.isPresent(), "Field value should not be present.");
  }

  @Test
  void evaluateConditionEqual_success() {
    ruleCondition.setOperator(OperatorEnum.IS);
    Assertions.assertTrue(ruleCondition.evaluateCondition("testValue"), "Condition should evaluate to true.");
  }

  @Test
  void evaluateConditionNotEqual_success() {
    ruleCondition.setOperator(OperatorEnum.IS_NOT);
    Assertions.assertTrue(ruleCondition.evaluateCondition("wrongValue"), "Condition should evaluate to true.");
  }

  @Test
  void evaluateConditionNotEqual_success_notEqual_ReturnFalse() {
    ruleCondition.setOperator(OperatorEnum.IS_NOT);
    ruleCondition.setValue("wrongValue");
    Assertions.assertFalse(ruleCondition.evaluateCondition("wrongValue"), "Condition should evaluate to true.");
  }

  @Test
  void evaluateConditionNotEqual_success_IN_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.IS_ONE_OF);
    ruleCondition.setValue(List.of(1, 2));
    Assertions.assertFalse(ruleCondition.evaluateCondition("wrongValue"));
  }

  @Test
  void evaluateConditionNotEqual_success_IN_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.IS_ONE_OF);
    ruleCondition.setValue(List.of(1, 2));
    Assertions.assertFalse(ruleCondition.evaluateCondition(List.of(1, 2)));
  }

  @Test
  void evaluateConditionNotEqual_success_NOT_IN_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.IS_NOT_ONE_OF);
    ruleCondition.setValue(List.of(1, 2));
    Assertions.assertTrue(ruleCondition.evaluateCondition("wrongValue"));
  }

  @Test
  void evaluateConditionNotEqual_success_NOT_IN_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.IS_NOT_ONE_OF);
    ruleCondition.setValue(List.of(1, 2));
    Assertions.assertTrue(ruleCondition.evaluateCondition(1));
  }

  @Test
  void evaluateConditionNotEqual_success_CONTAINS_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.CONTAINS);
    ruleCondition.setValue("wrongValue1");
    Assertions.assertFalse(ruleCondition.evaluateCondition("wrongValue"));
  }

  @Test
  void evaluateConditionNotEqual_success_CONTAINS_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.CONTAINS);
    ruleCondition.setValue("wrongValue1");
    Assertions.assertTrue(ruleCondition.evaluateCondition("wrongValue12"));
  }

  @Test
  void evaluateConditionNotEqual_success_DOES_NOT_CONTAIN_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.DOES_NOT_CONTAIN);
    ruleCondition.setValue("wrongValue1");
    Assertions.assertTrue(ruleCondition.evaluateCondition("wrongValue"));
  }

  @Test
  void evaluateConditionNotEqual_success_DOES_NOT_CONTAIN_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.DOES_NOT_CONTAIN);
    ruleCondition.setValue("wrongValue1");
    Assertions.assertFalse(ruleCondition.evaluateCondition("wrongValue1"));
  }

  @Test
  void evaluateConditionNotEqual_success_GREATER_THAN_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.GREATER_THAN);
    ruleCondition.setValue(12);
    Assertions.assertFalse(ruleCondition.evaluateCondition(11));
  }

  @Test
  void evaluateConditionNotEqual_success_GREATER_THAN_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.GREATER_THAN);
    ruleCondition.setValue(12);
    Assertions.assertTrue(ruleCondition.evaluateCondition(14));
  }

  @Test
  void evaluateConditionNotEqual_success_GREATER_THAN_OR_EQUAL_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.GREATER_THAN_OR_EQUAL);
    ruleCondition.setValue(12);
    Assertions.assertFalse(ruleCondition.evaluateCondition(11));
  }

  @Test
  void evaluateConditionNotEqual_success_GREATER_THAN_OR_EQUAL_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.GREATER_THAN_OR_EQUAL);
    ruleCondition.setValue(12);
    Assertions.assertTrue(ruleCondition.evaluateCondition(113));
  }

  @Test
  void evaluateConditionNotEqual_success_LESS_THAN_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.LESS_THAN);
    ruleCondition.setValue(12);
    Assertions.assertTrue(ruleCondition.evaluateCondition(11));
  }

  @Test
  void evaluateConditionNotEqual_success_LESS_THAN_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.LESS_THAN);
    ruleCondition.setValue(12);
    Assertions.assertFalse(ruleCondition.evaluateCondition(14));
  }

  @Test
  void evaluateConditionNotEqual_success_nullOperator() {
    ruleCondition.setValue(12);
    Assertions.assertFalse(ruleCondition.evaluateCondition(14));
  }

  @Test
  void evaluateConditionNotEqual_success_LESS_THAN_OR_EQUAL_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.LESS_THAN_OR_EQUAL);
    ruleCondition.setValue(12);
    Assertions.assertTrue(ruleCondition.evaluateCondition(11));
  }

  @Test
  void evaluateConditionNotEqual_success_LESS_THAN_OR_EQUAL_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.LESS_THAN_OR_EQUAL);
    ruleCondition.setValue(12);
    Assertions.assertFalse(ruleCondition.evaluateCondition(113));
  }

  @Test
  void evaluateConditionNotEqual_error_exception() {
    ruleCondition.setOperator(OperatorEnum.LESS_THAN_OR_EQUAL);
    ruleCondition.setValue(null);
    Assertions.assertFalse(ruleCondition.evaluateCondition(null));
  }


  @Test
  void evaluateContainsCondition_error_nullValue() {
    ruleCondition.setOperator(OperatorEnum.DOES_NOT_CONTAIN);
    ruleCondition.setValue(null);

    Assertions.assertNotNull(ReflectionTestUtils.invokeMethod(ruleCondition,"evaluateContainsCondition","this is a test"));
  }


  @Test
  void evaluateContainsCondition_success() {
    ruleCondition.setOperator(OperatorEnum.CONTAINS);
    ruleCondition.setValue("test");

    Assertions.assertNotNull(ReflectionTestUtils.invokeMethod(ruleCondition,"evaluateContainsCondition","this is a test"));
  }

  @Test
  void compareNumbersGreaterThan_success() throws Exception {
    ruleCondition.setOperator(OperatorEnum.GREATER_THAN);
    ruleCondition.setValue(10);

    Assertions.assertNotNull(ReflectionTestUtils.invokeMethod(ruleCondition,"compareNumbers",15));
  }

  @Test
  void compareNumbersGreaterThan_error_nullFieldValue() throws Exception {
    ruleCondition.setOperator(OperatorEnum.GREATER_THAN);
    ruleCondition.setValue(10);
    Assertions.assertThrows(Exception.class, () -> {
      ReflectionTestUtils.invokeMethod(ruleCondition,"compareNumbers",null);
    });
  }

  @Test
  void compareNumbersGreaterThan_error_nullValue() {
    ruleCondition.setOperator(OperatorEnum.GREATER_THAN);
    ruleCondition.setValue(null);
    Assertions.assertThrows(Exception.class, () -> {
      ReflectionTestUtils.invokeMethod(ruleCondition,"compareNumbers",15);
    });
  }

  @Test
  void tryParseFloat_success() {
    Float result = ReflectionTestUtils.invokeMethod(ruleCondition,"tryParseFloat","10.5");
    Assertions.assertNotNull(result, "Parsed value should not be null.");
    Assertions.assertEquals(10.5f, result, 0.001, "Parsed value should be 10.5.");
  }

  @Test
  void tryParseFloat_error_invalidValue() {
    Float result =ReflectionTestUtils.invokeMethod(ruleCondition,"tryParseFloat","invalidNumber");
    Assertions.assertNull(result, "Parsed value should be null.");
  }



  @Test
  void check_nullObject_shouldReturnFalse() {
    Assertions.assertFalse(ruleCondition.check(null, obj -> "someValue"));
  }

  @Test
  void check_functionProvidesValue_shouldReturnTrue() {
    TestObject testObject = new TestObject("wrongValue");
    Function<Object, String> func = Mockito.mock(Function.class);
    Mockito.when(func.apply(ruleCondition.getField())).thenReturn("testValue");

    Assertions.assertTrue(ruleCondition.check(testObject, func));
    Mockito.verify(func, Mockito.times(1)).apply(ruleCondition.getField());
  }

  @Test
  void check_functionProvidesNull_shouldReturnFalse() {
    TestObject testObject = new TestObject("wrongValue");
    Function<Object, String> func = Mockito.mock(Function.class);
    Mockito.when(func.apply(ruleCondition.getField())).thenReturn(null);

    Assertions.assertFalse(ruleCondition.check(testObject, func));
    Mockito.verify(func, Mockito.times(1)).apply(ruleCondition.getField());
  }

  @Test
  void check_fieldValueMatchesCondition_shouldReturnTrue() {
    TestObject testObject = new TestObject("testValue");
    ruleCondition.setField("field1");

    Assertions.assertFalse(ruleCondition.check(testObject, obj -> null));
  }

  @Test
  void check_fieldValueDoesNotMatchCondition_shouldReturnFalse() {
    TestObject testObject = new TestObject("wrongValue");
    ruleCondition.setField("field1");

    Assertions.assertFalse(ruleCondition.check(testObject, obj -> null));
  }


  static class TestObject {
    private String field1 = "value2";

    public TestObject(String testField) {
    }
  }

  static class SubClass extends TestObject {
    private String field2 = "value2";

    public SubClass() {
      super("12");
    }
  }

}
