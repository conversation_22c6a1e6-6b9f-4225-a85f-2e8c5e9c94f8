package vn.com.mbbank.kanban.mbmonitor.common.condition;


import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;

import com.alibaba.fastjson2.JSONArray;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OperatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleCondition;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleElement;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class RuleConditionTest {

  private RuleCondition<Object> ruleCondition;
  public static class SampleClass {
    private String name;
    private Long customField;
  }


  @BeforeEach
  void setup() {
    ruleCondition = RuleCondition.builder()
        .field("testField")
        .operator(OperatorEnum.IS)
        .value("testValue")
        .build();
  }

  @Test
  void check_fieldValuePresent_shouldReturnTrue() {
    TestObject testObject = new TestObject("");
    Assertions.assertFalse(ruleCondition.check(testObject, obj -> null));
  }

  @Test
  void check_fieldValueMissing_useFunctionToRetrieveValue_shouldReturnTrue() {
    TestObject testObject = new TestObject("123");
    ruleCondition.setField("nonExistentField");
    Function<Object, String> func = Mockito.mock(Function.class);
    Mockito.when(func.apply(ruleCondition.getField())).thenReturn("testValue");
    assertTrue(ruleCondition.check(testObject, func));
    Mockito.verify(func, Mockito.times(1)).apply(ruleCondition.getField());
  }

  @Test
  void getFieldExistsInSuperClass() {
    RuleCondition<String> ruleCondition = RuleCondition.<String>builder()
        .field("field1")
        .build();

    SubClass subObject = new SubClass();
    Optional<Object> result = ruleCondition.getFieldValue(subObject);

    assertTrue(result.isPresent());
  }

  @Test
  void check_success_validField() {
    TestObject testObject = new TestObject("testValue");

    Assertions.assertFalse(ruleCondition.check(testObject), "The field value should match the condition.");
  }


  @Test
  void check_success_objectNull2() {
    TestObject testObject = null;

    Assertions.assertFalse(ruleCondition.check(testObject));
  }

  @Test
  void check_success_invalidField() {
    TestObject testObject = new TestObject("wrongValue");

    Assertions.assertFalse(ruleCondition.check(testObject), "The field value should not match the condition.");
  }

  @Test
  void getFieldValue_success() {
    TestObject testObject = new TestObject("testValue");

    Optional<Object> fieldValue = ruleCondition.getFieldValue(testObject);
    Assertions.assertFalse(fieldValue.isPresent(), "Field value should be present.");
  }

  @Test
  void getFieldValue_error_notField() {
    TestObject testObject = new TestObject("testValue");

    ruleCondition.setField("nonExistingField");
    Optional<Object> fieldValue = ruleCondition.getFieldValue(testObject);
    Assertions.assertFalse(fieldValue.isPresent(), "Field value should not be present.");
  }

  @Test
  void evaluateConditionEqual_success() {
    ruleCondition.setOperator(OperatorEnum.IS);
    assertTrue(ruleCondition.evaluateCondition("testValue"), "Condition should evaluate to true.");
  }

  @Test
  void evaluateConditionNotEqual_success() {
    ruleCondition.setOperator(OperatorEnum.IS_NOT);
    assertTrue(ruleCondition.evaluateCondition("wrongValue"), "Condition should evaluate to true.");
  }

  @Test
  void evaluateConditionNotEqual_success_notEqual_ReturnFalse() {
    ruleCondition.setOperator(OperatorEnum.IS_NOT);
    ruleCondition.setValue("wrongValue");
    Assertions.assertFalse(ruleCondition.evaluateCondition("wrongValue"), "Condition should evaluate to true.");
  }

  @Test
  void evaluateConditionNotEqual_success_IN_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.IS_ONE_OF);
    ruleCondition.setValue(List.of(1, 2));
    Assertions.assertFalse(ruleCondition.evaluateCondition("wrongValue"));
  }

  @Test
  void evaluateConditionNotEqual_success_IN_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.IS_ONE_OF);
    ruleCondition.setValue(List.of(1, 2));
    Assertions.assertFalse(ruleCondition.evaluateCondition(List.of(1, 2)));
  }

  @Test
  void evaluateConditionNotEqual_success_NOT_IN_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.IS_NOT_ONE_OF);
    ruleCondition.setValue(List.of(1, 2));
    assertTrue(ruleCondition.evaluateCondition("wrongValue"));
  }

  @Test
  void evaluateConditionNotEqual_success_NOT_IN_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.IS_NOT_ONE_OF);
    ruleCondition.setValue(List.of(1, 2));
    assertTrue(ruleCondition.evaluateCondition(1));
  }

  @Test
  void evaluateConditionNotEqual_success_CONTAINS_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.CONTAINS);
    ruleCondition.setValue("wrongValue1");
    Assertions.assertFalse(ruleCondition.evaluateCondition("wrongValue"));
  }

  @Test
  void evaluateConditionNotEqual_success_CONTAINS_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.CONTAINS);
    ruleCondition.setValue("wrongValue1");
    assertTrue(ruleCondition.evaluateCondition("wrongValue12"));
  }

  @Test
  void evaluateConditionNotEqual_success_DOES_NOT_CONTAIN_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.DOES_NOT_CONTAIN);
    ruleCondition.setValue("wrongValue1");
    assertTrue(ruleCondition.evaluateCondition("wrongValue"));
  }

  @Test
  void evaluateConditionNotEqual_success_DOES_NOT_CONTAIN_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.DOES_NOT_CONTAIN);
    ruleCondition.setValue("wrongValue1");
    Assertions.assertFalse(ruleCondition.evaluateCondition("wrongValue1"));
  }

  @Test
  void evaluateConditionNotEqual_success_GREATER_THAN_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.GREATER_THAN);
    ruleCondition.setValue(12);
    Assertions.assertFalse(ruleCondition.evaluateCondition(11));
  }

  @Test
  void evaluateConditionNotEqual_success_GREATER_THAN_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.GREATER_THAN);
    ruleCondition.setValue(12);
    assertTrue(ruleCondition.evaluateCondition(14));
  }

  @Test
  void evaluateConditionNotEqual_success_GREATER_THAN_OR_EQUAL_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.GREATER_THAN_OR_EQUAL);
    ruleCondition.setValue(12);
    Assertions.assertFalse(ruleCondition.evaluateCondition(11));
  }

  @Test
  void evaluateConditionNotEqual_success_GREATER_THAN_OR_EQUAL_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.GREATER_THAN_OR_EQUAL);
    ruleCondition.setValue(12);
    assertTrue(ruleCondition.evaluateCondition(113));
  }

  @Test
  void evaluateConditionNotEqual_success_LESS_THAN_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.LESS_THAN);
    ruleCondition.setValue(12);
    assertTrue(ruleCondition.evaluateCondition(11));
  }

  @Test
  void evaluateConditionNotEqual_success_LESS_THAN_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.LESS_THAN);
    ruleCondition.setValue(12);
    Assertions.assertFalse(ruleCondition.evaluateCondition(14));
  }

  @Test
  void evaluateConditionNotEqual_success_nullOperator() {
    ruleCondition.setValue(12);
    Assertions.assertFalse(ruleCondition.evaluateCondition(14));
  }

  @Test
  void evaluateConditionNotEqual_success_LESS_THAN_OR_EQUAL_returnTrue() {
    ruleCondition.setOperator(OperatorEnum.LESS_THAN_OR_EQUAL);
    ruleCondition.setValue(12);
    assertTrue(ruleCondition.evaluateCondition(11));
  }

  @Test
  void evaluateConditionNotEqual_success_LESS_THAN_OR_EQUAL_returnFalse() {
    ruleCondition.setOperator(OperatorEnum.LESS_THAN_OR_EQUAL);
    ruleCondition.setValue(12);
    Assertions.assertFalse(ruleCondition.evaluateCondition(113));
  }

  @Test
  void evaluateConditionNotEqual_error_exception() {
    ruleCondition.setOperator(OperatorEnum.LESS_THAN_OR_EQUAL);
    ruleCondition.setValue(null);
    Assertions.assertFalse(ruleCondition.evaluateCondition(null));
  }


  @Test
  void evaluateContainsCondition_error_nullValue() {
    ruleCondition.setOperator(OperatorEnum.DOES_NOT_CONTAIN);
    ruleCondition.setValue(null);

    Assertions.assertNotNull(ReflectionTestUtils.invokeMethod(ruleCondition,"evaluateContainsCondition","this is a test"));
  }


  @Test
  void evaluateContainsCondition_success() {
    ruleCondition.setOperator(OperatorEnum.CONTAINS);
    ruleCondition.setValue("test");

    Assertions.assertNotNull(ReflectionTestUtils.invokeMethod(ruleCondition,"evaluateContainsCondition","this is a test"));
  }

  @Test
  void compareNumbersGreaterThan_success() throws Exception {
    ruleCondition.setOperator(OperatorEnum.GREATER_THAN);
    ruleCondition.setValue(10);

    Assertions.assertNotNull(ReflectionTestUtils.invokeMethod(ruleCondition,"compareNumbers",15));
  }

  @Test
  void compareNumbersGreaterThan_error_nullFieldValue() throws Exception {
    ruleCondition.setOperator(OperatorEnum.GREATER_THAN);
    ruleCondition.setValue(10);
    Assertions.assertThrows(Exception.class, () -> {
      ReflectionTestUtils.invokeMethod(ruleCondition,"compareNumbers",null);
    });
  }

  @Test
  void compareNumbersGreaterThan_error_nullValue() {
    ruleCondition.setOperator(OperatorEnum.GREATER_THAN);
    ruleCondition.setValue(null);
    Assertions.assertThrows(Exception.class, () -> {
      ReflectionTestUtils.invokeMethod(ruleCondition,"compareNumbers",15);
    });
  }

  @Test
  void tryParseFloat_success() {
    Float result = ReflectionTestUtils.invokeMethod(ruleCondition,"tryParseFloat","10.5");
    Assertions.assertNotNull(result, "Parsed value should not be null.");
    assertEquals(10.5f, result, 0.001, "Parsed value should be 10.5.");
  }

  @Test
  void tryParseFloat_error_invalidValue() {
    Float result =ReflectionTestUtils.invokeMethod(ruleCondition,"tryParseFloat","invalidNumber");
    Assertions.assertNull(result, "Parsed value should be null.");
  }



  @Test
  void check_nullObject_shouldReturnFalse() {
    Assertions.assertFalse(ruleCondition.check(null, obj -> "someValue"));
  }

  @Test
  void check_functionProvidesValue_shouldReturnTrue() {
    TestObject testObject = new TestObject("wrongValue");
    Function<Object, String> func = Mockito.mock(Function.class);
    Mockito.when(func.apply(ruleCondition.getField())).thenReturn("testValue");

    assertTrue(ruleCondition.check(testObject, func));
    Mockito.verify(func, Mockito.times(1)).apply(ruleCondition.getField());
  }

  @Test
  void check_functionProvidesNull_shouldReturnFalse() {
    TestObject testObject = new TestObject("wrongValue");
    Function<Object, String> func = Mockito.mock(Function.class);
    Mockito.when(func.apply(ruleCondition.getField())).thenReturn(null);

    Assertions.assertFalse(ruleCondition.check(testObject, func));
    Mockito.verify(func, Mockito.times(1)).apply(ruleCondition.getField());
  }

  @Test
  void check_fieldValueMatchesCondition_shouldReturnTrue() {
    TestObject testObject = new TestObject("testValue");
    ruleCondition.setField("field1");

    Assertions.assertFalse(ruleCondition.check(testObject, obj -> null));
  }

  @Test
  void check_fieldValueDoesNotMatchCondition_shouldReturnFalse() {
    TestObject testObject = new TestObject("wrongValue");
    ruleCondition.setField("field1");

    Assertions.assertFalse(ruleCondition.check(testObject, obj -> null));
  }


  static class TestObject {
    private String field1 = "value2";

    public TestObject(String testField) {
    }
  }

  static class SubClass extends TestObject {
    private String field2 = "value2";

    public SubClass() {
      super("12");
    }
  }

  @Test
  void test_getCustomObjectIds_withValidClass() {
    RuleCondition<Object> condition = new RuleCondition<>();
    condition.setField("customField");
    condition.setValue("123");

    List<Long> result = condition.getCustomObjectIds(SampleClass.class);

    assertEquals(1, result.size());
    assertEquals(123L, result.get(0));
  }

  @Test
  void test_getCustomObjectIds_withFieldNames_matchingField() {
    RuleCondition<Object> condition = new RuleCondition<>();
    condition.setField("myField");
    condition.setValue("456");

    Set<String> fieldNames = Set.of("myField");

    List<Long> result = condition.getCustomObjectIds(fieldNames);

    assertEquals(1, result.size());
    assertEquals(456L, result.get(0));
  }

  @Test
  void test_getCustomObjectIds_withFieldNames_nonMatchingField() {
    RuleCondition<Object> condition = new RuleCondition<>();
    condition.setField("notFound");
    condition.setValue("999");

    Set<String> fieldNames = Set.of("otherField");

    List<Long> result = condition.getCustomObjectIds(fieldNames);

    assertTrue(result.isEmpty());
  }

  @Test
  void test_getCustomObjectIds_withNullValue() {
    RuleCondition<Object> condition = new RuleCondition<>();
    condition.setField("idField");
    condition.setValue(null);

    Set<String> fieldNames = Set.of("idField");

    List<Long> result = condition.getCustomObjectIds(fieldNames);

    assertTrue(result.isEmpty());
  }

  @Test
  void test_extractIdsFromRule_withSingleLongValue() {
    RuleCondition<Object> condition = new RuleCondition<>();
    condition.setField("id");
    condition.setValue("789");

    Set<String> fieldNames = Set.of("id");

    List<Long> result = condition.extractIdsFromRule(condition, fieldNames);

    assertEquals(List.of(789L), result);
  }

  @Test
  void test_extractIdsFromRule_withJSONArrayValue() {
    RuleCondition<Object> condition = new RuleCondition<>();
    condition.setField("id");
    JSONArray jsonArray = new JSONArray();
    jsonArray.add("1");
    jsonArray.add("2");
    jsonArray.add("abc"); // invalid one
    jsonArray.add("3");
    condition.setValue(jsonArray);

    Set<String> fieldNames = Set.of("id");

    List<Long> result = condition.extractIdsFromRule(condition, fieldNames);

    assertEquals(List.of(1L, 2L, 3L), result);
  }

  @Test
  void test_extractIdsFromRule_withNonRuleCondition() {
    RuleElement rule = new RuleElement() {
      @Override
      public boolean check(Object object) {
        return false;
      }

      @Override
      public boolean check(Map<String, Object> object) {
        return false;
      }

      @Override
      public <V> boolean check(Object object, Function<Object, V> func) {
        return false;
      }

      @Override
      public boolean checkPriority(Long id) {
        return false;
      }

      @Override
      public boolean checkCustomObject(Long id) {
        return false;
      }

      @Override
      public List<Long> getCustomObjectIds(Class<?> clazz) {
        return List.of();
      }

      @Override
      public List<Long> getCustomObjectIds(Set<String> fieldNames) {
        return List.of();
      }

      @Override
      public void setValueFromExternal(Object newVal) {

      }
    };
    RuleCondition<Object> condition = new RuleCondition<>();

    List<Long> result = condition.extractIdsFromRule(rule, Set.of("any"));

    assertTrue(result.isEmpty());
  }

  @Test
  void test_extractIdsFromRule_withInvalidLong() {
    RuleCondition<Object> condition = new RuleCondition<>();
    condition.setField("code");
    condition.setValue("invalidNumber");

    Set<String> fieldNames = Set.of("code");

    List<Long> result = condition.extractIdsFromRule(condition, fieldNames);

    assertTrue(result.isEmpty());
  }

  @Test
  void test_setValueFromExternal_setsCorrectly() {
    RuleCondition<String> condition = new RuleCondition<>();
    condition.setValue("initial");

    condition.setValueFromExternal("updated");

    assertEquals("updated", condition.getValue());
  }

}
