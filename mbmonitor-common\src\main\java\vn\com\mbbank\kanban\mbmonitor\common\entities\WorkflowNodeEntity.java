package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.converter.WorkflowNodeConfigurationConverter;
import vn.com.mbbank.kanban.mbmonitor.common.converter.WorkflowNodeIdsConverter;
import vn.com.mbbank.kanban.mbmonitor.common.converter.WorkflowNodePositionConverter;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowNodePositionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.BaseNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;

@Entity
@Data
@Table(name = TableName.WORKFLOW_NODE)
@EqualsAndHashCode(callSuper = true)
@KanbanAutoGenerateUlId
public class WorkflowNodeEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "WORKFLOW_ID")
  private String workflowId;

  @Column(name = "POSITION")
  @Convert(converter = WorkflowNodePositionConverter.class)
  private WorkflowNodePositionModel position;

  @Column(name = "STATUS")
  @Enumerated(EnumType.STRING)
  private WorkflowNodeStatusEnum status;

  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private WorkflowNodeTypeEnum type;

  @Column(name = "CONFIGURATION")
  @Convert(converter = WorkflowNodeConfigurationConverter.class)
  private BaseNodeConfigurationModel configuration;

  @Column(name = "INCOMING_NODE_IDS")
  @Convert(converter = WorkflowNodeIdsConverter.class)
  private List<String> incomingNodeIds;

  @Column(name = "OUTGOING_NODE_IDS")
  @Convert(converter = WorkflowNodeIdsConverter.class)
  private List<String> outgoingNodeIds;

  @Override
  public String getId() {
    return id;
  }
}
