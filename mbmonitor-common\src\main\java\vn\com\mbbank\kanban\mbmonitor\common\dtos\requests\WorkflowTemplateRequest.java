package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.BaseFormBuilderElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowModel;

/**
 * WorkflowTemplateRequest.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WorkflowTemplateRequest {
  String id;
  @Size(max = CommonConstants.COMMON_QUERY_SQL_NAME_MAX_LENGTH)
  @Size(min = 1)
  @NotBlank
  String name;
  @Size(max = CommonConstants.COMMON_DESCRIPTION_MAX_LENGTH)
  String description;
  @Builder.Default
  List<BaseFormBuilderElementModel> formLayout = new ArrayList<>();
  @NotNull
  WorkflowModel workflow;
}
