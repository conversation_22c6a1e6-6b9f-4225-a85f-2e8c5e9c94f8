package vn.com.mbbank.kanban.mbmonitor.common.utils.condition;

import com.alibaba.fastjson2.JSONArray;
import java.lang.reflect.Field;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OperatorEnum;

/**
 * Represents a rule condition that evaluates a field's value in an object.
 *
 * @param <V> The type of value used in the condition.
 * <AUTHOR>
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RuleCondition<V> extends RuleElement {

  String field;
  OperatorEnum operator;
  V value;

  @Override
  public boolean check(Object object) {
    if (object == null) {
      return false;
    }
    return getFieldValue(object)
        .map(this::evaluateCondition)
        .orElse(false);
  }

  @Override
  public boolean check(Map<String, Object> object) {
    if (object == null) {
      return false;
    }
    return Optional.ofNullable(object.get(field))
        .map(this::evaluateCondition)
        .orElse(false);
  }


  @Override
  public <A> boolean check(Object object, Function<Object, A> func) {
    if (object == null) {
      return false;
    }
    Optional<Object> fieldValue = getFieldValue(object);
    fieldValue = fieldValue.isEmpty() ? Optional.ofNullable(func.apply(field)) : fieldValue;

    return fieldValue.map(this::evaluateCondition).orElse(false);
  }

  @Override
  public boolean checkPriority(Long id) {
    if (field.equals("priority")) {
      if (OperatorEnum.IS_ONE_OF.equals(operator) || OperatorEnum.IS_NOT_ONE_OF.equals(operator)) {
        return evaluateInCondition(id);
      } else {
        return value.equals(id.toString());
      }
    }
    return false;
  }

  @Override
  public boolean checkCustomObject(Long id) {
    return field.equals(id.toString());
  }

  /**
   * Retrieves the value of a field from the given object using reflection.
   *
   * @param object the object to extract the field value from.
   * @return an optional containing the field value, or empty if not found.
   */
  public Optional<Object> getFieldValue(Object object) {
    Class<?> currentClass = object.getClass();
    while (currentClass != null) {
      try {
        Field objectField = currentClass.getDeclaredField(field);
        objectField.setAccessible(true);
        return Optional.ofNullable(objectField.get(object));
      } catch (NoSuchFieldException | IllegalAccessException e) {
        currentClass = currentClass.getSuperclass();
      }
    }
    return Optional.empty();
  }

  /**
   * Evaluates the condition using the provided operator.
   *
   * @param fieldValue the value of the field to compare.
   * @return true if the condition is met; false otherwise.
   */
  public boolean evaluateCondition(Object fieldValue) {
    try {
      return switch (operator) {
        case IS -> fieldValue.equals(value);
        case IS_NOT -> !fieldValue.equals(value);
        case IS_ONE_OF -> evaluateInCondition(fieldValue);
        case IS_NOT_ONE_OF -> !evaluateInCondition(fieldValue);
        case CONTAINS -> evaluateContainsCondition(fieldValue);
        case DOES_NOT_CONTAIN -> !evaluateContainsCondition(fieldValue);
        case GREATER_THAN -> compareNumbers(fieldValue) > 0;
        case GREATER_THAN_OR_EQUAL -> compareNumbers(fieldValue) >= 0;
        case LESS_THAN -> compareNumbers(fieldValue) < 0;
        case LESS_THAN_OR_EQUAL -> compareNumbers(fieldValue) <= 0;
      };
    } catch (Exception e) {
      return false;
    }
  }

  private boolean evaluateInCondition(Object fieldValue) {
    if (value instanceof JSONArray valueArray) {
      if (fieldValue instanceof ArrayList<?> fieldArray) {
        return fieldArray.stream()
            .allMatch(fv -> valueArray.stream()
                .anyMatch(v -> v.toString().equals(fv.toString())));
      }
      return valueArray.stream().anyMatch(ele -> ele.equals(String.valueOf(fieldValue)));
    }
    return false;
  }

  private boolean evaluateContainsCondition(Object fieldValue) {
    return fieldValue instanceof String fv && value instanceof String v && fv.contains(v);
  }

  private int compareNumbers(Object fieldValue) throws IllegalArgumentException {
    Float fieldNumber = tryParseFloat(fieldValue);
    Float valueNumber = tryParseFloat(value);
    if (fieldNumber == null || valueNumber == null) {
      throw new IllegalArgumentException("Cannot compare non-numeric values.");
    }
    return fieldNumber.compareTo(valueNumber);
  }

  private Float tryParseFloat(Object value) {
    try {
      if (value instanceof Number number) {
        return number.floatValue();
      }
      return value != null ? Float.parseFloat(value.toString()) : null;
    } catch (NumberFormatException e) {
      return null;
    }
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RuleCondition<?> that = (RuleCondition<?>) o;
    return Objects.equals(field, that.field)
        && operator == that.operator
        && Objects.equals(value, that.value);
  }

  @Override
  public int hashCode() {
    return Objects.hash(field, operator, value);
  }

  @Override
  public String toString() {
    return MessageFormat.format("({0} {1} \"{2}\")", field, operator.name(), value);
  }
}
