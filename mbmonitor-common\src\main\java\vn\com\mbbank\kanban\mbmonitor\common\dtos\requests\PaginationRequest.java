package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;

/**
 * Pagination and filter request.
 */

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PaginationRequest extends PaginationRequestDTO {

}
