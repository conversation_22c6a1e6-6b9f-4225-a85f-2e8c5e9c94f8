package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.WorkflowTemplateRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowTemplateEntity;

/**
 * WorkflowTemplateEntityMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WorkflowTemplateEntityMapper
    extends KanbanBaseMapper<WorkflowTemplateEntity, WorkflowTemplateRequest> {
  WorkflowTemplateEntityMapper INSTANCE = Mappers.getMapper(WorkflowTemplateEntityMapper.class);

  /**
   * map from CustomInputRequest to WorkflowTemplateEntity.
   *
   * @param request WorkflowTemplateRequest.
   * @return CustomInputEntity.
   */
  WorkflowTemplateEntity map(WorkflowTemplateRequest request);

  /**
   * map from WorkflowTemplateEntity to WorkflowTemplateRequest.
   *
   * @param entity  WorkflowTemplateEntity.
   * @param request WorkflowTemplateRequest.
   */
  void merge(@MappingTarget WorkflowTemplateEntity entity, WorkflowTemplateRequest request);

}
