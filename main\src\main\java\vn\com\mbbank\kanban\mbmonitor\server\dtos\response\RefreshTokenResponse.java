package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * response for api refresh token.
 */
@Data
@Builder
@AllArgsConstructor
public class RefreshTokenResponse {
  private String accessToken;
  @Temporal(TemporalType.TIMESTAMP)
  private Date expiresAt;
}
