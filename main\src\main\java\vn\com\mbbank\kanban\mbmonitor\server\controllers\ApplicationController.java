package vn.com.mbbank.kanban.mbmonitor.server.controllers;


import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ExportDataResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ApplicationPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ApplicationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ExportFileApplicationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ApplicationDependenciesResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ApplicationWithPriorityResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;

/**
 * Controller logic application.
 */
@RestController
@RequestMapping(ServerUrl.APPLICATION_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ApplicationController extends BaseController {
  ApplicationService applicationService;
  ApplicationDependencyService applicationDependencyService;

  /**
   * Finds all applications by a list of service IDs.
   *
   * @param applicationPaginationRequest the pagination request containing paging details
   *                                     and the list of service IDs to filter applications
   * @return a paginated list of ApplicationResponseDto
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.APPLICATION_MANAGEMENT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.SERVICE_MANAGEMENT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.WEBHOOK_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.EMAIL_COLLECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_CONNECTION, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_COLLECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_GROUP_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.REPORT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_REQUEST, action = PermissionActionEnum.APPROVE),
      @AclPermission(module = PermissionModuleEnum.FILTER_ALERT_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MODIFY_ALERT_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.AUTO_TRIGGER_ACTION_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MONITOR_WEB_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_REQUEST, action = PermissionActionEnum.VIEW)
  })

  @GetMapping
  public ResponseData<Page<ApplicationResponse>> findAll(
      @ModelAttribute ApplicationPaginationRequest applicationPaginationRequest) {
    return ResponseUtils.success(
        applicationService.findAll(applicationPaginationRequest));
  }

  /**
   * Finds applications with priority by alert status.
   *
   * @param alertGroupStatus the alert group status
   * @param serviceId serviceId
   * @return a list of ApplicationWithPriorityResponse
   */

  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MONITOR_ALERT, action = PermissionActionEnum.VIEW)}
  )
  @GetMapping("/with-priorities")
  public ResponseData<List<ApplicationWithPriorityResponse>> findApplicationWithPriorityByAlertGroupStatusAndServiceId(
      @RequestParam AlertGroupStatusEnum alertGroupStatus, @RequestParam String serviceId) {
    return ResponseUtils.success(
        applicationService.findApplicationWithPriorityByAlertGroupStatusAndServiceId(alertGroupStatus, serviceId));
  }

  /**
   * find all dependencies by applicationId.
   *
   * @param id serviceId
   * @return list alert group config
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.APPLICATION_MANAGEMENT, action = PermissionActionEnum.EDIT),
      @AclPermission(module = PermissionModuleEnum.APPLICATION_MANAGEMENT, action = PermissionActionEnum.DELETE),
  })
  @Operation(summary = "Find all dependencies by applicationId")
  @GetMapping("{id}/dependencies")
  public ResponseData<ApplicationDependenciesResponse> findAllDependenciesById(
      @PathVariable String id) {
    return ResponseUtils.success(applicationDependencyService.findAllDependenciesById(id));
  }

  /**
   * Find By Id.
   *
   * @param id id
   * @return data
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.APPLICATION_MANAGEMENT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.SERVICE_MANAGEMENT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.WEBHOOK_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.EMAIL_COLLECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_CONNECTION, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_COLLECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_GROUP_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MONITOR_ALERT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.FILTER_ALERT_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MODIFY_ALERT_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.AUTO_TRIGGER_ACTION_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MONITOR_WEB_CONFIG, action = PermissionActionEnum.VIEW),
  })
  @GetMapping("{id}")
  public ResponseData<ApplicationResponse> findById(@PathVariable String id) {
    return ResponseUtils.success(applicationService.findApplicationById(id));
  }

  /**
   * Create or update service.
   *
   * @param applicationRequest ApplicationRequest
   * @return new application ApplicationRequest
   */

  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.APPLICATION_MANAGEMENT, action = PermissionActionEnum.CREATE),
      @AclPermission(module = PermissionModuleEnum.APPLICATION_MANAGEMENT, action = PermissionActionEnum.EDIT)
  })
  @PostMapping
  public ResponseData<ApplicationEntity> createOrUpdate(
      @RequestBody @Valid ApplicationRequest applicationRequest
  ) throws BusinessException {
    return ResponseUtils.success(applicationService.createOrUpdate(applicationRequest));
  }

  /**
   * Delete by Id.
   *
   * @param id id
   * @return Object
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.APPLICATION_MANAGEMENT, action = PermissionActionEnum.DELETE),
  })
  @Operation(summary = "Delete application by id")
  @DeleteMapping("/{id}")
  public ResponseData<String> deleteById(@PathVariable String id) throws BusinessException {
    applicationDependencyService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }

  /**
   * API to export a file as a stream based on the provided request data.
   *
   * @param request The request object containing the necessary data for file export.
   *                The request is validated using the {@link Valid} annotation.
   * @return StreamingResponseBody A response entity containing the file to be exported as a stream.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.APPLICATION_MANAGEMENT, action = PermissionActionEnum.EXPORT),
  })
  @PostMapping(value = "/export")
  public ResponseData<ExportDataResponse> export(
      @RequestBody @Valid ExportFileApplicationRequest request)
      throws IOException, BusinessException {
    return ResponseUtils.success(applicationService.exportFile(request));
  }
}
