package vn.com.mbbank.kanban.mbmonitor.common.configs;

import java.net.URI;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.checksums.RequestChecksumCalculation;
import software.amazon.awssdk.core.checksums.ResponseChecksumValidation;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;
import vn.com.mbbank.kanban.mbmonitor.common.services.S3FileService;
import vn.com.mbbank.kanban.mbmonitor.common.services.impl.S3FileServiceImpl;

/**
 * Configuration class for S3FileService bean.
 * This bean is only initialized when all required S3 properties are present
 * in the application configuration.
 */
@Configuration
public class S3FileServiceConfig {

  /**
   * Creates the S3FileService bean if all S3-related properties are defined.
   *
   * @param s3Properties the S3 configuration properties
   * @return the S3FileService implementation
   */
  @Bean
  @ConditionalOnProperty(name = "mbmonitor.s3.enabled", havingValue = "true")
  public S3FileService s3FileService(S3Properties s3Properties) {
    S3Client s3Client = S3Client.builder()
        .credentialsProvider(StaticCredentialsProvider.create(
            AwsBasicCredentials.create(s3Properties.getAccessKey(), s3Properties.getSecretKey())))
        .endpointOverride(URI.create(s3Properties.getEndpoint()))
        .region(Region.of(s3Properties.getRegion()))
        .requestChecksumCalculation(RequestChecksumCalculation.WHEN_REQUIRED)
        .responseChecksumValidation(ResponseChecksumValidation.WHEN_REQUIRED)
        .serviceConfiguration(S3Configuration.builder()
            .pathStyleAccessEnabled(true)
            .build())
        .build();
    return new S3FileServiceImpl(s3Properties, s3Client);
  }
}
