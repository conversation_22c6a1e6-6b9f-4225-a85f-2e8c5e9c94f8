package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.CustomInputRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CustomInputResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomInputEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.FormBuilderElementTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.CustomInputResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.CustomInputService;
import vn.com.mbbank.kanban.test.ControllerTest;

@ExtendWith(MockitoExtension.class)
class CustomInputControllerTest extends ControllerTest {

  @Mock
  private CustomInputService customInputService;

  @Mock
  private CommonAclPermissionService commonAclPermissionService;

  // Mapper is usually tested separately or assumed correct if using MapStruct INSTANCE
  private final CustomInputResponseMapper customInputResponseMapper = CustomInputResponseMapper.INSTANCE;

  @InjectMocks
  @Spy
  private CustomInputController customInputController;

  // Test data
  private final String testId = "test-id-123";
  private final String testName = "Test Custom Input";
  private final String testDescription = "Test Description";

  @Test
  void findById_success() throws BusinessException {
    // Arrange
    verifyPermissions(List.of(
        new AclPermissionModel(PermissionModuleEnum.IT_SERVICE_MANAGEMENT, PermissionActionEnum.VIEW)
    ), "findById", CustomInputController.class);

    CustomInputEntity customInputEntity = createTestCustomInputEntity();
    when(customInputService.findWithId(testId)).thenReturn(customInputEntity);

    // Act
    ResponseData<CustomInputResponse> response = customInputController.findById(testId);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(testId, response.getData().getId());
    assertEquals(testName, response.getData().getName());
    verify(customInputService, times(1)).findWithId(testId);
  }

  @Test
  void findById_failed_whenNotFound() throws BusinessException {
    // Arrange
    when(customInputService.findWithId(testId))
        .thenThrow(new BusinessException(ErrorCode.CUSTOM_INPUT_NOT_FOUND));

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      customInputController.findById(testId);
    });

    assertEquals(ErrorCode.CUSTOM_INPUT_NOT_FOUND.getCode(), exception.getCode());
    verify(customInputService, times(1)).findWithId(testId);
  }

  @Test
  void findAllWithPaging_success() throws BusinessException {
    // Arrange
    verifyPermissions(List.of(
        new AclPermissionModel(PermissionModuleEnum.IT_SERVICE_MANAGEMENT, PermissionActionEnum.VIEW)
    ), "findAllWithPaging", CustomInputController.class);

    PaginationRequestDTO requestDTO = new PaginationRequestDTO();
    CustomInputEntity customInputEntity = createTestCustomInputEntity();
    Page<CustomInputEntity> pageResponse = new PageImpl<>(Collections.singletonList(customInputEntity));
    when(customInputService.findAllWithPaging(any(PaginationRequestDTO.class))).thenReturn(pageResponse);

    // Act
    ResponseData<Page<CustomInputResponse>> response = customInputController.findAllWithPaging(requestDTO);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(1, response.getData().getTotalElements());
    assertEquals(testId, response.getData().getContent().get(0).getId());
    
    // Verify that the request was modified with search properties and sort
    assertEquals(List.of("name", "description"), requestDTO.getPropertiesSearch());
    assertEquals("createdDate", requestDTO.getSortBy());
    
    verify(customInputService, times(1)).findAllWithPaging(requestDTO);
  }

  @Test
  void findAll_success() throws BusinessException {
    // Arrange
    verifyPermissions(List.of(
        new AclPermissionModel(PermissionModuleEnum.IT_SERVICE_MANAGEMENT, PermissionActionEnum.EDIT),
        new AclPermissionModel(PermissionModuleEnum.IT_SERVICE_MANAGEMENT, PermissionActionEnum.CREATE)
    ), "findAll", CustomInputController.class);

    CustomInputEntity customInputEntity = createTestCustomInputEntity();
    List<CustomInputEntity> listResponse = Collections.singletonList(customInputEntity);
    when(customInputService.findAll()).thenReturn(listResponse);

    // Act
    ResponseData<List<CustomInputResponse>> response = customInputController.findAll();

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(1, response.getData().size());
    assertEquals(testId, response.getData().get(0).getId());
    verify(customInputService, times(1)).findAll();
  }

  @Test
  void save_success_whenCreateOrUpdate() throws BusinessException {
    // Arrange
    CustomInputRequest customInputRequest = createTestCustomInputRequest();
    CustomInputEntity customInputEntity = createTestCustomInputEntity();
    
    // Note: Permission for save is handled by makeSureCreateOrUpdate in BaseController
    doNothing().when(customInputController).makeSureCreateOrUpdate(any(), any(), any(), any());
    when(customInputService.createOrUpdate(any(CustomInputRequest.class))).thenReturn(customInputEntity);

    // Act
    ResponseData<CustomInputResponse> response = customInputController.save(customInputRequest);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(testId, response.getData().getId());
    verify(customInputService, times(1)).createOrUpdate(customInputRequest);
    verify(customInputController, times(1)).makeSureCreateOrUpdate(
        PermissionModuleEnum.IT_SERVICE_MANAGEMENT,
        PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT,
        Collections.singleton(customInputRequest.getId())
    );
  }

  @Test
  void save_failed_whenNameExists() throws BusinessException {
    // Arrange
    CustomInputRequest customInputRequest = createTestCustomInputRequest();
    
    doNothing().when(customInputController).makeSureCreateOrUpdate(any(), any(), any(), any());
    when(customInputService.createOrUpdate(any(CustomInputRequest.class)))
        .thenThrow(new BusinessException(ErrorCode.CUSTOM_INPUT_NAME_IS_EXISTED));

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      customInputController.save(customInputRequest);
    });

    assertEquals(ErrorCode.CUSTOM_INPUT_NAME_IS_EXISTED.getCode(), exception.getCode());
    verify(customInputService, times(1)).createOrUpdate(customInputRequest);
  }

  @Test
  void deleteById_success() throws BusinessException {
    // Arrange
    verifyPermissions(List.of(
        new AclPermissionModel(PermissionModuleEnum.IT_SERVICE_MANAGEMENT, PermissionActionEnum.DELETE)
    ), "deleteById", CustomInputController.class);

    doNothing().when(customInputService).deleteWithId(testId);

    // Act
    ResponseData<String> response = customInputController.deleteById(testId);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertEquals("OK", response.getData());
    verify(customInputService, times(1)).deleteWithId(testId);
  }

  @Test
  void deleteById_failed_whenNotFound() throws BusinessException {
    // Arrange
    doThrow(new BusinessException(ErrorCode.CUSTOM_INPUT_NOT_FOUND))
        .when(customInputService).deleteWithId(testId);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      customInputController.deleteById(testId);
    });

    assertEquals(ErrorCode.CUSTOM_INPUT_NOT_FOUND.getCode(), exception.getCode());
    verify(customInputService, times(1)).deleteWithId(testId);
  }

  // Helper methods to create test data
  private CustomInputEntity createTestCustomInputEntity() {
    CustomInputEntity entity = new CustomInputEntity();
    entity.setId(testId);
    entity.setName(testName);
    entity.setDescription(testDescription);
    entity.setType(FormBuilderElementTypeEnum.TEXT);
    return entity;
  }

  private CustomInputRequest createTestCustomInputRequest() {
    return CustomInputRequest.builder()
        .id(testId)
        .name(testName)
        .description(testDescription)
        .type(FormBuilderElementTypeEnum.TEXT)
        .build();
  }
}
