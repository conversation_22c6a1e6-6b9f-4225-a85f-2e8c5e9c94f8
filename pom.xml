<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version> <!-- lookup parent from repository -->
    </parent>
    <groupId>vn.com.mbbank.kanban.mbmonitor.main</groupId>
    <artifactId>main</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>mbmonitor-common</module>
        <module>main</module>
    </modules>
    <properties>
        <java.version>17</java.version>
    </properties>
    <build>
        <plugins>            <!--          config checkstyle-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.1.1</version>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>10.14.1</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <includes>**/*.java</includes>
                    <configLocation>mbmonitor-common/src/main/resources/checkstyle.xml</configLocation>
                    <suppressionsLocation>mbmonitor-common/src/main/resources/checkstyle-suppressions.xml
                    </suppressionsLocation>
                    <encoding>UTF-8</encoding>
                    <consoleOutput>true</consoleOutput>
                    <failsOnError>true</failsOnError>
                    <failOnViolation>true</failOnViolation>
                    <violationSeverity>warning</violationSeverity>
                    <linkXRef>true</linkXRef>
                    <outputFileFormat>plain</outputFileFormat>
                </configuration>
                <executions>
                    <execution>
                        <id>check-style</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-maven-plugin</artifactId>
                <version>3.0.3</version>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.12</version>
                <configuration>
                    <excludes>
                        <exclude>**/*.xlsx</exclude>
                        <exclude>**/*.xls</exclude>
                        <exclude>**/dtos/**</exclude>
                        <exclude>**/mappers/**</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://10.1.12.211:8081/repository/maven-kanban-repo/</url>
        </repository>
    </distributionManagement>
    <pluginRepositories>
        <pluginRepository>
            <id>nexus</id>
            <name>Nexus Plugin Repository</name>
            <url>http://10.1.12.211:8081/repository/maven-kanban-group/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </pluginRepository>
    </pluginRepositories>
</project>