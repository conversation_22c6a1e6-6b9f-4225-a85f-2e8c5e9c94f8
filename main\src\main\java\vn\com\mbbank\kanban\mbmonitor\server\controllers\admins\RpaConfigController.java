package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.RpaConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.RpaConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.RpaConfigService;

/**
 * Controller logic rpa config.
 */
@RestController
@RequestMapping(ServerUrl.RPA_CONFIG_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RpaConfigController extends BaseController {
  RpaConfigService rpaConfigService;
  
  /**
   * Finds the rpa config.
   *
   * @return a RpaConfigResponse
   */
  @HasPermission(value = {
    @AclPermission(module = PermissionModuleEnum.RPA_CONFIG, action = PermissionActionEnum.CONFIG)})
  @Operation(summary = "Finds the rpa config.")
  @GetMapping
  public ResponseData<RpaConfigResponse> findRpaConfig() {
    return ResponseUtils.success(rpaConfigService.findRpaConfig());
  }
  
  /**
   * Create or update rpa config.
   *
   * @param request RpaConfigRequest
   * @return a RpaConfigResponse
   */
  @HasPermission(value = {
    @AclPermission(module = PermissionModuleEnum.RPA_CONFIG, action = PermissionActionEnum.CONFIG)})
  @PostMapping
  @Operation(summary = "Create or update rpa config.")
  public ResponseData<RpaConfigResponse> createOrUpdate(@RequestBody @Valid RpaConfigRequest request)
        throws BusinessException {
    return ResponseUtils.success(rpaConfigService.createOrUpdate(request));
  }
  
  /**
   * Active or inactive rpa config by id.
   *
   * @param id id
   * @return RpaConfigResponse
   */
  @HasPermission(value = {
    @AclPermission(module = PermissionModuleEnum.RPA_CONFIG, action = PermissionActionEnum.CONFIG)})
  @Operation(summary = "Active or inactive rpa config by id.")
  @PutMapping("/{id}/toggle-status")
  public ResponseData<RpaConfigResponse> active(@PathVariable String id) throws BusinessException {
    return ResponseUtils.success(rpaConfigService.updateStatus(id));
  }
}
