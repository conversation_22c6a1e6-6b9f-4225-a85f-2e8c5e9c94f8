package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigExecutionMapEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AutoTriggerActionConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AutoTriggerActionConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TriggerExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.AutoTriggerActionConfigEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.ExecutionResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.ServiceResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AutoTriggerActionConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AutoTriggerActionConfigDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AutoTriggerActionConfigExecutionMapService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;

import java.util.List;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AutoTriggerActionConfigServiceImplTest {

  @Mock
  private AutoTriggerActionConfigRepository autoTriggerActionConfigRepository;

  @Mock
  private AutoTriggerActionConfigExecutionMapService triggerExecutionMapService;

  @Mock
  private ExecutionService executionService;

  @InjectMocks
  private AutoTriggerActionConfigServiceImpl autoTriggerActionConfigService;
  @Mock
  ExecutionResponseMapper executionResponseMapper;
  @Mock
  AutoTriggerActionConfigEntityMapper autoTriggerActionConfigEntityMapper;

  @Mock
  ServiceService serviceService;
  @Mock
  ApplicationService applicationService;
  @Mock
  AutoTriggerActionConfigDependencyService dependencyService;
  @Mock
  SysLogKafkaProducerService sysLogKafkaProducerService;

  @Test
  void getRepository_success() {
    JpaCommonRepository<AutoTriggerActionConfigEntity, String> result =
            autoTriggerActionConfigService.getRepository();
    assertEquals(autoTriggerActionConfigRepository, result);
  }

  @Test
  void findAllWithSearch_success_shouldReturnEmptyPage_whenNoData() throws BusinessException {
    PaginationRequestDTO request = new PaginationRequestDTO();

    when(autoTriggerActionConfigRepository.findAllBySearch(request))
            .thenReturn(Page.empty());

    Page<AutoTriggerActionConfigResponse> result = autoTriggerActionConfigService.findAllWithSearch(request);

    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void findAllWithSearch_success_shouldReturnConfigsWithEmptyExecutions_whenNoMappingFound() throws BusinessException {
    PaginationRequestDTO request = new PaginationRequestDTO();
    AutoTriggerActionConfigResponse config = new AutoTriggerActionConfigResponse();
    config.setId("cfg-1");
    config.setRuleGroupColumn("{}");

    Page<AutoTriggerActionConfigResponse> page = new PageImpl<>(List.of(config));

    when(autoTriggerActionConfigRepository.findAllBySearch(request)).thenReturn(page);
    when(triggerExecutionMapService.findExecutionResponseByConfigIdIn(List.of("cfg-1")))
            .thenReturn(List.of());

    Page<AutoTriggerActionConfigResponse> result = autoTriggerActionConfigService.findAllWithSearch(request);

    assertEquals(1, result.getContent().size());
    assertTrue(result.getContent().get(0).getExecutions().isEmpty());
  }

  @Test
  void findAllWithSearch_success_shouldSkipNullExecutionMapping() throws BusinessException {
    PaginationRequestDTO request = new PaginationRequestDTO();
    AutoTriggerActionConfigResponse config = new AutoTriggerActionConfigResponse();
    config.setId("cfg-2");
    config.setRuleGroupColumn("{}");

    Page<AutoTriggerActionConfigResponse> page = new PageImpl<>(List.of(config));

    TriggerExecutionResponse mapping = new TriggerExecutionResponse();
    mapping.setAutoTriggerActionConfigId("cfg-2");
    mapping.setExecutionId("exe-2");

    when(autoTriggerActionConfigRepository.findAllBySearch(request)).thenReturn(page);
    when(triggerExecutionMapService.findExecutionResponseByConfigIdIn(List.of("cfg-2")))
            .thenReturn(List.of(mapping));

    when(executionService.findAllByIdIn(List.of("exe-2"))).thenReturn(List.of());

    Page<AutoTriggerActionConfigResponse> result = autoTriggerActionConfigService.findAllWithSearch(request);
    assertEquals(1, result.getContent().size());
    assertTrue(result.getContent().get(0).getExecutions().isEmpty());
  }

  @Test
  void findAllWithSearch_success_shouldReturnConfigWithMappedExecutions_realMapper() throws BusinessException {
    PaginationRequestDTO request = new PaginationRequestDTO();
    AutoTriggerActionConfigResponse config = new AutoTriggerActionConfigResponse();
    config.setId("cfg-3");
    config.setRuleGroupColumn("{}");

    Page<AutoTriggerActionConfigResponse> page = new PageImpl<>(List.of(config));

    TriggerExecutionResponse mapping = new TriggerExecutionResponse();
    mapping.setAutoTriggerActionConfigId("cfg-3");
    mapping.setExecutionId("exe-3");

    ExecutionEntity entity = new ExecutionEntity();
    entity.setId("exe-3");

    when(autoTriggerActionConfigRepository.findAllBySearch(request)).thenReturn(page);
    when(triggerExecutionMapService.findExecutionResponseByConfigIdIn(List.of("cfg-3"))).thenReturn(List.of(mapping));
    when(executionService.findAllByIdIn(List.of("exe-3"))).thenReturn(List.of(entity));

    Page<AutoTriggerActionConfigResponse> result = autoTriggerActionConfigService.findAllWithSearch(request);

    assertEquals(1, result.getContent().size());
    assertEquals(1, result.getContent().get(0).getExecutions().size());
    assertEquals("exe-3", result.getContent().get(0).getExecutions().get(0).getId());
  }

  @Test
  void save_success_CreateNewEntity() throws BusinessException {
    AutoTriggerActionConfigRequest request = new AutoTriggerActionConfigRequest();
    request.setServiceIds(List.of("s1"));
    request.setApplicationIds(List.of("a1"));
    request.setExecutionIds(List.of("e1"));
    request.setName("test");

    AutoTriggerActionConfigEntity entity = new AutoTriggerActionConfigEntity();
    when(serviceService.findAllByIdInAndDeleted(List.of("s1"), false)).thenReturn(List.of(new ServiceEntity("s1", "a", "a")));
    when(applicationService.findAllByIdInAndServiceIdInAndDeleted(List.of("a1"), List.of("s1"), false))
            .thenReturn(List.of(new ApplicationEntity("a1", "s1", "", "")));
    ExecutionEntity execution = new ExecutionEntity();
    execution.setId("e1");
    when(executionService.findAllByIdIn(List.of("e1"))).thenReturn(List.of(execution));


    autoTriggerActionConfigService.save(request);

    verify(dependencyService).saveAll(anyList());
    verify(triggerExecutionMapService).saveAll(anyList());
    verify(sysLogKafkaProducerService).send(
            eq(LogActionEnum.CREATE_AUTO_TRIGGER_EXECUTION),
            eq("test"),
            any()
    );
  }

  @Test
  void save_success_shouldUpdateEntityWhenIdExists() throws BusinessException {
    AutoTriggerActionConfigRequest request = new AutoTriggerActionConfigRequest();
    request.setId("id-1");
    request.setServiceIds(List.of("s1"));
    request.setApplicationIds(List.of("a1"));
    request.setExecutionIds(List.of("e1"));
    request.setName("test");

    AutoTriggerActionConfigEntity entity = new AutoTriggerActionConfigEntity();
    when(autoTriggerActionConfigRepository.findById("id-1")).thenReturn(Optional.of(entity));
    when(serviceService.findAllByIdInAndDeleted(List.of("s1"), false)).thenReturn(List.of(new ServiceEntity("s1", "a", "a")));
    when(applicationService.findAllByIdInAndServiceIdInAndDeleted(anyList(), anyList(), eq(false)))
            .thenReturn(List.of(new ApplicationEntity("a1", "s1", "a2", "")));
    ExecutionEntity execution = new ExecutionEntity();
    execution.setId("e1");
    when(executionService.findAllByIdIn(List.of("e1"))).thenReturn(List.of(execution));


    autoTriggerActionConfigService.save(request);

    verify(dependencyService).deleteAllByAutoTriggerActionConfigId("id-1");
    verify(triggerExecutionMapService, times(2)).deleteAllByAutoTriggerActionConfigId("id-1");
  }

  @Test
  void save_failure_shouldThrowExceptionWhenServiceDeleted() {
    AutoTriggerActionConfigRequest request = new AutoTriggerActionConfigRequest();
    request.setServiceIds(List.of("s1"));
    when(serviceService.findAllByIdInAndDeleted(anyList(), eq(false))).thenReturn(List.of());

    assertThrows(BusinessException.class, () -> autoTriggerActionConfigService.save(request));
  }

  @Test
  void save_failure_shouldThrowExceptionWhenApplicationDeleted() {
    AutoTriggerActionConfigRequest request = new AutoTriggerActionConfigRequest();
    request.setServiceIds(List.of("s1"));
    request.setApplicationIds(List.of("a1"));
    when(serviceService.findAllByIdInAndDeleted(List.of("s1"), false)).thenReturn(List.of(new ServiceEntity("s1", "a", "a")));
    when(applicationService.findAllByIdInAndServiceIdInAndDeleted(anyList(), anyList(), eq(false)))
            .thenReturn(List.of());

    assertThrows(BusinessException.class, () -> autoTriggerActionConfigService.save(request));
  }

  @Test
  void findWithId_success() throws BusinessException {
    String id = "config123";

    AutoTriggerActionConfigEntity configEntity = new AutoTriggerActionConfigEntity();
    configEntity.setId(id);

    List<AutoTriggerActionConfigDependencyEntity> dependencies = List.of(
            buildDependency("svc1", DependencyTypeEnum.SERVICE),
            buildDependency("svc2", DependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION),
            buildDependency("app1", DependencyTypeEnum.APPLICATION)
    );

    List<ServiceEntity> services = List.of(new ServiceEntity());
    List<ApplicationResponse> applications = List.of(new ApplicationResponse());
    List<AutoTriggerActionConfigExecutionMapEntity> mappers = List.of(
            buildMapper("exec1"), buildMapper("exec2")
    );
    List<ExecutionEntity> executions = List.of(new ExecutionEntity(), new ExecutionEntity());
    AutoTriggerActionConfigResponse expectedResponse = new AutoTriggerActionConfigResponse();
    expectedResponse.setId("config123");
    expectedResponse.setActive(null);


    when(autoTriggerActionConfigRepository.findById(id)).thenReturn(Optional.of(configEntity));
    when(dependencyService.findAllByAutoTriggerActionConfigId(id)).thenReturn(dependencies);
    when(triggerExecutionMapService.findAllByAutoTriggerActionConfigId(id)).thenReturn(mappers);
    when(serviceService.findAllByIdIn(List.of("svc1", "svc2"))).thenReturn(services);
    when(applicationService.findAllByIdIn(List.of("app1"))).thenReturn(applications);
    when(executionService.findAllById(List.of("exec1", "exec2"))).thenReturn(executions);
    expectedResponse.setExecutions(ExecutionResponseMapper.INSTANCE.map(executions));
    expectedResponse.setServices(ServiceResponseMapper.INSTANCE.map(services));
    expectedResponse.setApplications(applications);
    AutoTriggerActionConfigResponse result = autoTriggerActionConfigService.findWithId(id);

    assertEquals(expectedResponse, result);
  }

  @Test
  void findWithId_failure_configNotFound_throwsException() {
    String id = "not-found";

    when(autoTriggerActionConfigRepository.findById(id)).thenReturn(Optional.empty());

    BusinessException ex = assertThrows(BusinessException.class, () -> autoTriggerActionConfigService.findWithId(id));
    assertEquals(ErrorCode.AUTO_TRIGGER_ACTION_CONFIG_NOT_FOUND.getCode(), ex.getCode());
  }

  @Test
  void updateActive_success_setActiveTrue() throws BusinessException {
    String id = "config-id";
    boolean active = true;
    AutoTriggerActionConfigEntity entity = new AutoTriggerActionConfigEntity();
    entity.setId(id);
    entity.setName("Config A");
    entity.setActive(false);

    when(autoTriggerActionConfigRepository.findById(id)).thenReturn(Optional.of(entity));
    when(autoTriggerActionConfigRepository.save(entity)).thenReturn(entity);

    AutoTriggerActionConfigEntity result = autoTriggerActionConfigService.updateActive(id);

    assertTrue(result.getActive());
    verify(autoTriggerActionConfigRepository).save(entity);
    verify(sysLogKafkaProducerService).send(LogActionEnum.ACTIVE_AUTO_TRIGGER_EXECUTION, "Config A");
  }

  @Test
  void updateActive_success_setActiveFalse() throws BusinessException {
    String id = "config-id";
    boolean active = false;
    AutoTriggerActionConfigEntity entity = new AutoTriggerActionConfigEntity();
    entity.setId(id);
    entity.setName("Config B");
    entity.setActive(true);

    when(autoTriggerActionConfigRepository.findById(id)).thenReturn(Optional.of(entity));
    when(autoTriggerActionConfigRepository.save(entity)).thenReturn(entity);

    AutoTriggerActionConfigEntity result = autoTriggerActionConfigService.updateActive(id);

    assertFalse(result.getActive());
    verify(autoTriggerActionConfigRepository).save(entity);
    verify(sysLogKafkaProducerService).send(LogActionEnum.INACTIVE_AUTO_TRIGGER_EXECUTION, "Config B");
  }

  @Test
  void updateActive_configNotFound_throwsException() {
    String id = "invalid-id";

    when(autoTriggerActionConfigRepository.findById(id)).thenReturn(Optional.empty());

    BusinessException exception = assertThrows(BusinessException.class, () -> autoTriggerActionConfigService.updateActive(id));
    assertEquals(ErrorCode.AUTO_TRIGGER_ACTION_CONFIG_NOT_FOUND.getMessage(), exception.getMessage());

    verify(autoTriggerActionConfigRepository, never()).save(any());
    verify(sysLogKafkaProducerService, never()).send(any(), any());
  }

  @ParameterizedTest
  @CsvSource({
          "config-id,true,true",
          "config-id,false,false",
          ",true,true",
          ",false,false"
  })
  void validateTriggerConfigRequest_variousCases(String id, boolean isExist, boolean shouldThrow) {
    AutoTriggerActionConfigRequest request = new AutoTriggerActionConfigRequest();
    request.setId(id);
    request.setName("testName");

    String capitalizedName = StringUtils.capitalizeFirstLetter("testName");

    if (id != null) {
      when(autoTriggerActionConfigRepository.existsByIdNotAndNameIgnoreCase(id, capitalizedName)).thenReturn(isExist);
    } else {
      when(autoTriggerActionConfigRepository.existsByNameIgnoreCase(capitalizedName)).thenReturn(isExist);
    }

    if (shouldThrow) {
      BusinessException exception = assertThrows(BusinessException.class, () ->
              autoTriggerActionConfigService.validateTriggerConfigRequest(request));
      assertEquals(ErrorCode.AUTO_TRIGGER_ACTION_CONFIG_NAME_IS_EXISTED.getMessage(), exception.getMessage());
    } else {
      assertDoesNotThrow(() -> autoTriggerActionConfigService.validateTriggerConfigRequest(request));
    }
  }

  @Test
  void findAllByCustomObjectId_success_withValidMatchingRuleGroup() {
    Long customObjectId = 123L;
    RuleGroupType matchingRule = mock(RuleGroupType.class);
    RuleGroupType nonMatchingRule = mock(RuleGroupType.class);

    AutoTriggerActionConfigEntity config1 = new AutoTriggerActionConfigEntity();
    config1.setName("Trigger A");
    config1.setRuleGroup(matchingRule);

    AutoTriggerActionConfigEntity config2 = new AutoTriggerActionConfigEntity();
    config2.setName("Trigger B");
    config2.setRuleGroup(nonMatchingRule);

    when(autoTriggerActionConfigRepository.findAll()).thenReturn(List.of(config1, config2));
    when(matchingRule.checkCustomObject(customObjectId)).thenReturn(true);
    when(nonMatchingRule.checkCustomObject(customObjectId)).thenReturn(false);

    List<String> result = autoTriggerActionConfigService.findAllByCustomObjectId(customObjectId);

    assertEquals(1, result.size());
    assertTrue(result.contains("Trigger A"));
  }

  @Test
  void findAllByCustomObjectId_success_withEmptyRuleGroup() {
    Long customObjectId = 456L;
    AutoTriggerActionConfigEntity config = new AutoTriggerActionConfigEntity();
    config.setName("NoRule");
    config.setRuleGroup(null);

    when(autoTriggerActionConfigRepository.findAll()).thenReturn(List.of(config));

    List<String> result = autoTriggerActionConfigService.findAllByCustomObjectId(customObjectId);

    assertTrue(result.isEmpty());
  }

  @Test
  void findDependencyNameByDependencyId_success() {
    String dependencyId = "dep-id";
    List<DependencyTypeEnum> types = List.of(DependencyTypeEnum.SERVICE);
    List<String> names = List.of("Service A", "Service B");

    when(autoTriggerActionConfigRepository.findDependencyNameByDependencyId(dependencyId, types)).thenReturn(names);

    List<String> result = autoTriggerActionConfigService.findDependencyNameByDependencyId(dependencyId, types);

    assertEquals(names, result);
  }

  @Test
  void deleteWithId_success() throws BusinessException {
    String id = "config-id";
    AutoTriggerActionConfigEntity autoTriggerActionConfigEntity = new AutoTriggerActionConfigEntity();
    autoTriggerActionConfigEntity.setId(id);
    when(autoTriggerActionConfigRepository.findById(id)).thenReturn(Optional.of(autoTriggerActionConfigEntity));
    autoTriggerActionConfigService.deleteWithId(id);

    verify(autoTriggerActionConfigRepository).deleteById(id);
    verify(dependencyService).deleteAllByAutoTriggerActionConfigId(id);
    verify(triggerExecutionMapService).deleteAllByAutoTriggerActionConfigId(id);
  }

  @Test
  void findWithId_success_withApplicationIds() throws BusinessException {
    String id = "test-id";
    AutoTriggerActionConfigEntity configEntity = new AutoTriggerActionConfigEntity();
    configEntity.setId(id);

    AutoTriggerActionConfigDependencyEntity appDependency = new AutoTriggerActionConfigDependencyEntity();
    appDependency.setDependencyId("app-id");
    appDependency.setType(DependencyTypeEnum.APPLICATION);

    AutoTriggerActionConfigExecutionMapEntity mapper = new AutoTriggerActionConfigExecutionMapEntity();
    mapper.setExecutionId("exec-id");

    ExecutionEntity execEntity = new ExecutionEntity();
    execEntity.setId("exec-id");

    ApplicationResponse appResponse = new ApplicationResponse();
    appResponse.setId("app-id");

    when(autoTriggerActionConfigRepository.findById(id)).thenReturn(Optional.of(configEntity));
    when(dependencyService.findAllByAutoTriggerActionConfigId(id)).thenReturn(List.of(appDependency));
    when(triggerExecutionMapService.findAllByAutoTriggerActionConfigId(id)).thenReturn(List.of(mapper));
    when(applicationService.findAllByIdIn(List.of("app-id"))).thenReturn(List.of(appResponse));
    when(executionService.findAllById(List.of("exec-id"))).thenReturn(List.of(execEntity));

    AutoTriggerActionConfigResponse response = autoTriggerActionConfigService.findWithId(id);

    assertNotNull(response);
    verify(applicationService).findAllByIdIn(List.of("app-id"));
  }

  @Test
  void findWithId_success_withoutApplicationIds() throws BusinessException {
    String id = "test-id";
    AutoTriggerActionConfigEntity configEntity = new AutoTriggerActionConfigEntity();
    configEntity.setId(id);

    AutoTriggerActionConfigDependencyEntity serviceDependency = new AutoTriggerActionConfigDependencyEntity();
    serviceDependency.setDependencyId("service-id");
    serviceDependency.setType(DependencyTypeEnum.SERVICE);

    AutoTriggerActionConfigExecutionMapEntity mapper = new AutoTriggerActionConfigExecutionMapEntity();
    mapper.setExecutionId("exec-id");

    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("service-id");

    ExecutionEntity execEntity = new ExecutionEntity();
    execEntity.setId("exec-id");

    when(autoTriggerActionConfigRepository.findById(id)).thenReturn(Optional.of(configEntity));
    when(dependencyService.findAllByAutoTriggerActionConfigId(id)).thenReturn(List.of(serviceDependency));
    when(triggerExecutionMapService.findAllByAutoTriggerActionConfigId(id)).thenReturn(List.of(mapper));
    when(serviceService.findAllByIdIn(List.of("service-id"))).thenReturn(List.of(serviceEntity));
    when(executionService.findAllById(List.of("exec-id"))).thenReturn(List.of(execEntity));

    AutoTriggerActionConfigResponse response = autoTriggerActionConfigService.findWithId(id);

    assertNotNull(response);
    verify(applicationService, never()).findAllByIdIn(anyList());
  }

  @Test
  void deleteWithId_verifyNotCalledWhenIdNull() throws BusinessException {
    AutoTriggerActionConfigEntity entity = new AutoTriggerActionConfigEntity();
    entity.setId("123");

    when(autoTriggerActionConfigRepository.findById(any())).thenReturn(Optional.of(entity));
    autoTriggerActionConfigService.deleteWithId(null);

    verify(autoTriggerActionConfigRepository).deleteById(null);
    verify(dependencyService).deleteAllByAutoTriggerActionConfigId(null);
    verify(triggerExecutionMapService).deleteAllByAutoTriggerActionConfigId(null);
  }

  @Test
  void testSave_failure_UpdateMode_AutoTriggerNotFound_ShouldThrowBusinessException() {
    // given
    AutoTriggerActionConfigRequest request = new AutoTriggerActionConfigRequest();
    request.setId("123"); // update mode

    when(autoTriggerActionConfigRepository.findById("123")).thenReturn(Optional.empty());

    // when - then
    BusinessException exception = assertThrows(BusinessException.class,
            () -> autoTriggerActionConfigService.save(request));

    assertEquals(ErrorCode.AUTO_TRIGGER_ACTION_CONFIG_NOT_FOUND.getMessage(), exception.getMessage());
  }

  @Test
  void testFindAllByPriorityConfigId() {
    // Given
    Long priorityId = 1L;

    AutoTriggerActionConfigEntity config1 = new AutoTriggerActionConfigEntity();
    config1.setName("Config A");
    RuleGroupType ruleGroup1 = Mockito.mock(RuleGroupType.class);
    Mockito.when(ruleGroup1.checkPriority(priorityId)).thenReturn(true);
    config1.setRuleGroup(ruleGroup1);

    AutoTriggerActionConfigEntity config2 = new AutoTriggerActionConfigEntity();
    config2.setName("Config B");
    RuleGroupType ruleGroup2 = Mockito.mock(RuleGroupType.class);
    Mockito.when(ruleGroup2.checkPriority(priorityId)).thenReturn(false);
    config2.setRuleGroup(ruleGroup2);

    AutoTriggerActionConfigEntity config3 = new AutoTriggerActionConfigEntity();
    config3.setName("Config A");
    RuleGroupType ruleGroup3 = Mockito.mock(RuleGroupType.class);
    Mockito.when(ruleGroup3.checkPriority(priorityId)).thenReturn(true);
    config3.setRuleGroup(ruleGroup3);

    List<AutoTriggerActionConfigEntity> mockConfigs = List.of(config1, config2, config3);
    Mockito.when(autoTriggerActionConfigRepository.findAll()).thenReturn(mockConfigs);

    // When
    List<String> result = autoTriggerActionConfigService.findAllByPriorityConfigId(priorityId);

    // Then
    Assertions.assertEquals(1, result.stream().filter(name -> name.equals("Config A")).count());
    Assertions.assertTrue(result.contains("Config A"));
    Assertions.assertFalse(result.contains("Config B"));
  }

  @Test
  void testSave_success_WithServicesAndApplicationMapContainsServiceId_ShouldAddServiceDependency() throws BusinessException {
    // given
    AutoTriggerActionConfigRequest request = new AutoTriggerActionConfigRequest();
    request.setServiceIds(List.of("1L"));
    request.setApplicationIds(List.of("10L"));
    request.setExecutionIds(List.of("100L"));

    ServiceEntity service = new ServiceEntity();
    service.setId("1L");
    when(serviceService.findAllByIdInAndDeleted(request.getServiceIds(), false)).thenReturn(List.of(service));

    ApplicationEntity application = new ApplicationEntity();
    application.setId("10L");
    application.setServiceId("1L");
    when(applicationService.findAllByIdInAndServiceIdInAndDeleted(any(), any(), eq(false))).thenReturn(List.of(application));

    AutoTriggerActionConfigEntity entity = new AutoTriggerActionConfigEntity();
    entity.setId("999L");
    ExecutionEntity execution = new ExecutionEntity();
    execution.setId("123");

    when(executionService.findAllByIdIn(request.getExecutionIds())).thenReturn(List.of(execution));

    // when
    autoTriggerActionConfigService.save(request);

    // then
    ArgumentCaptor<List<AutoTriggerActionConfigDependencyEntity>> captor = ArgumentCaptor.forClass(List.class);
    verify(dependencyService).saveAll(captor.capture());

    List<AutoTriggerActionConfigDependencyEntity> dependencies = captor.getValue();
    assertEquals(2, dependencies.size());

    var serviceDep = dependencies.stream()
            .filter(dep -> dep.getType() == DependencyTypeEnum.SERVICE)
            .findFirst().orElse(null);
    assertNotNull(serviceDep);
  }

  private AutoTriggerActionConfigDependencyEntity buildDependency(String id, DependencyTypeEnum type) {
    AutoTriggerActionConfigDependencyEntity dep = new AutoTriggerActionConfigDependencyEntity();
    dep.setDependencyId(id);
    dep.setType(type);
    return dep;
  }

  private AutoTriggerActionConfigExecutionMapEntity buildMapper(String executionId) {
    AutoTriggerActionConfigExecutionMapEntity mapper = new AutoTriggerActionConfigExecutionMapEntity();
    mapper.setExecutionId(executionId);
    return mapper;
  }

  @Test
  void testSave_failure_ExecutionInTriggerBeDeleted_ShouldThrowBusinessException() {
    // given
    AutoTriggerActionConfigRequest request = new AutoTriggerActionConfigRequest();
    request.setExecutionIds(List.of("e1"));
    request.setName("test");

    // when - then
    BusinessException exception = assertThrows(BusinessException.class,
            () -> autoTriggerActionConfigService.save(request));

    assertEquals(ErrorCode.EXECUTION_IN_TRIGGER_BE_DELETED.getMessage(), exception.getMessage());
  }
}


