package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.BrowserEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MonitorTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.ActionModel;

/**
 * Model request service to create or update rpa config.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MonitorWebConfigResponse {
  String id;
  String name;
  String webUrl;
  MonitorTypeEnum monitorType;
  Integer timeout;
  String content;
  String contentJson;
  boolean active;
  String serviceId;
  String serviceName;
  String applicationId;
  String applicationName;
  Long priorityId;
  Integer[] months;
  Integer[] dayOfMonths;
  Integer[] dayOfWeeks;
  Integer[] hours;
  Integer[] minutes;
  String description;
  String contact;
  List<ActionModel> authActions;
  List<ActionModel> actions;
  BrowserEnum browser;
}
