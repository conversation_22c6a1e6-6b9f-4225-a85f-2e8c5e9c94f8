package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailTemplateEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.EmailTemplateDependenciesResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.EmailTemplateRepository;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.WorkflowNodeDependencyRepository;

@ExtendWith(MockitoExtension.class)
class EmailTemplateDependencyServiceImplTest {

  @Mock
  private EmailTemplateRepository emailTemplateRepository;

  @Mock
  private WorkflowNodeDependencyRepository workflowNodeDependencyRepository;

  @Mock
  private EmailTemplateReceiverServiceImpl emailTemplateReceiverService;

  @Mock
  private SysLogKafkaProducerService sysLogKafkaProducerService;

  @InjectMocks
  private EmailTemplateDependencyServiceImpl emailTemplateDependencyService;

  private final Long testId = 1L;
  private final String testName = "Test Email Template";

  @Test
  void getRepository_success() {
    // Act
    JpaCommonRepository<EmailTemplateEntity, Long> result = emailTemplateDependencyService.getRepository();

    // Assert
    assertEquals(emailTemplateRepository, result);
  }

  @Test
  void deleteWithId_success_noDependencies() throws BusinessException {
    // Arrange
    EmailTemplateEntity emailTemplate = createTestEmailTemplate();
    
    when(emailTemplateRepository.findById(testId)).thenReturn(Optional.of(emailTemplate));
    when(workflowNodeDependencyRepository.findWorkflowTemplateNameByReferenceIdAndType(
        String.valueOf(testId), WorkflowNodeTypeEnum.EMAIL)).thenReturn(List.of());
    when(emailTemplateReceiverService.deleteAllByEmailTemplateId(testId)).thenReturn(1L);
    doNothing().when(sysLogKafkaProducerService).send(any(LogActionEnum.class), anyString());

    // Act
    emailTemplateDependencyService.deleteWithId(testId);

    // Assert
    verify(emailTemplateRepository, times(1)).findById(testId);
    verify(workflowNodeDependencyRepository, times(1))
        .findWorkflowTemplateNameByReferenceIdAndType(String.valueOf(testId), WorkflowNodeTypeEnum.EMAIL);
    verify(emailTemplateReceiverService, times(1)).deleteAllByEmailTemplateId(testId);
    verify(sysLogKafkaProducerService, times(1)).send(LogActionEnum.DELETE_TEMPLATE, testName);
  }

  @Test
  void deleteWithId_failed_emailTemplateNotFound() {
    // Arrange
    when(emailTemplateRepository.findById(testId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      emailTemplateDependencyService.deleteWithId(testId);
    });

    assertEquals(ErrorCode.EMAIL_TEMPLATE_NOT_FOUND.getCode(), exception.getCode());
    verify(emailTemplateRepository, times(1)).findById(testId);
    verify(workflowNodeDependencyRepository, never())
        .findWorkflowTemplateNameByReferenceIdAndType(anyString(), any(WorkflowNodeTypeEnum.class));
    verify(emailTemplateReceiverService, never()).deleteAllByEmailTemplateId(any());
    verify(sysLogKafkaProducerService, never()).send(any(LogActionEnum.class), anyString());
  }

  @Test
  void deleteWithId_failed_hasDependencies() {
    // Arrange
    EmailTemplateEntity emailTemplate = createTestEmailTemplate();
    List<String> workflowTemplates = Arrays.asList("Workflow Template 1", "Workflow Template 2");
    
    when(emailTemplateRepository.findById(testId)).thenReturn(Optional.of(emailTemplate));
    when(workflowNodeDependencyRepository.findWorkflowTemplateNameByReferenceIdAndType(
        String.valueOf(testId), WorkflowNodeTypeEnum.EMAIL)).thenReturn(workflowTemplates);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      emailTemplateDependencyService.deleteWithId(testId);
    });

    assertEquals(ErrorCode.EMAIL_TEMPLATE_CAN_NOT_BE_DELETE.getCode(), exception.getCode());
    verify(emailTemplateRepository, times(1)).findById(testId);
    verify(workflowNodeDependencyRepository, times(1))
        .findWorkflowTemplateNameByReferenceIdAndType(String.valueOf(testId), WorkflowNodeTypeEnum.EMAIL);
    verify(emailTemplateReceiverService, never()).deleteAllByEmailTemplateId(any());
    verify(sysLogKafkaProducerService, never()).send(any(LogActionEnum.class), anyString());
  }

  @Test
  void findAllDependenciesById_success_noDependencies() {
    // Arrange
    when(workflowNodeDependencyRepository.findWorkflowTemplateNameByReferenceIdAndType(
        String.valueOf(testId), WorkflowNodeTypeEnum.EMAIL)).thenReturn(List.of());

    // Act
    EmailTemplateDependenciesResponse result = emailTemplateDependencyService.findAllDependenciesById(testId);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getWorkflowTemplates());
    assertEquals(0, result.getWorkflowTemplates().size());
    verify(workflowNodeDependencyRepository, times(1))
        .findWorkflowTemplateNameByReferenceIdAndType(String.valueOf(testId), WorkflowNodeTypeEnum.EMAIL);
  }

  @Test
  void findAllDependenciesById_success_withDependencies() {
    // Arrange
    List<String> workflowTemplates = Arrays.asList("Workflow Template 1", "Workflow Template 2", "Workflow Template 3");
    when(workflowNodeDependencyRepository.findWorkflowTemplateNameByReferenceIdAndType(
        String.valueOf(testId), WorkflowNodeTypeEnum.EMAIL)).thenReturn(workflowTemplates);

    // Act
    EmailTemplateDependenciesResponse result = emailTemplateDependencyService.findAllDependenciesById(testId);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getWorkflowTemplates());
    assertEquals(3, result.getWorkflowTemplates().size());
    assertEquals("Workflow Template 1", result.getWorkflowTemplates().get(0));
    assertEquals("Workflow Template 2", result.getWorkflowTemplates().get(1));
    assertEquals("Workflow Template 3", result.getWorkflowTemplates().get(2));
    verify(workflowNodeDependencyRepository, times(1))
        .findWorkflowTemplateNameByReferenceIdAndType(String.valueOf(testId), WorkflowNodeTypeEnum.EMAIL);
  }

  @Test
  void deleteWithId_success_emptyDependenciesList() throws BusinessException {
    // Arrange
    EmailTemplateEntity emailTemplate = createTestEmailTemplate();
    
    when(emailTemplateRepository.findById(testId)).thenReturn(Optional.of(emailTemplate));
    when(workflowNodeDependencyRepository.findWorkflowTemplateNameByReferenceIdAndType(
        String.valueOf(testId), WorkflowNodeTypeEnum.EMAIL)).thenReturn(Arrays.asList());
    when(emailTemplateReceiverService.deleteAllByEmailTemplateId(testId)).thenReturn(0L);
    doNothing().when(sysLogKafkaProducerService).send(any(LogActionEnum.class), anyString());

    // Act
    emailTemplateDependencyService.deleteWithId(testId);

    // Assert
    verify(emailTemplateRepository, times(1)).findById(testId);
    verify(emailTemplateReceiverService, times(1)).deleteAllByEmailTemplateId(testId);
    verify(sysLogKafkaProducerService, times(1)).send(LogActionEnum.DELETE_TEMPLATE, testName);
  }

  @Test
  void findAllDependenciesById_success_nullId() {
    // Arrange
    Long nullId = null;
    when(workflowNodeDependencyRepository.findWorkflowTemplateNameByReferenceIdAndType(
        String.valueOf(nullId), WorkflowNodeTypeEnum.EMAIL)).thenReturn(List.of());

    // Act
    EmailTemplateDependenciesResponse result = emailTemplateDependencyService.findAllDependenciesById(nullId);

    // Assert
    assertNotNull(result);
    assertNotNull(result.getWorkflowTemplates());
    assertEquals(0, result.getWorkflowTemplates().size());
    verify(workflowNodeDependencyRepository, times(1))
        .findWorkflowTemplateNameByReferenceIdAndType("null", WorkflowNodeTypeEnum.EMAIL);
  }

  @Test
  void deleteWithId_success_withLargeId() throws BusinessException {
    // Arrange
    Long largeId = 999999999L;
    EmailTemplateEntity emailTemplate = new EmailTemplateEntity();
    ReflectionTestUtils.setField(emailTemplate, "id", largeId);
    ReflectionTestUtils.setField(emailTemplate, "name", "Large ID Template");

    when(emailTemplateRepository.findById(largeId)).thenReturn(Optional.of(emailTemplate));
    when(workflowNodeDependencyRepository.findWorkflowTemplateNameByReferenceIdAndType(
        String.valueOf(largeId), WorkflowNodeTypeEnum.EMAIL)).thenReturn(List.of());
    when(emailTemplateReceiverService.deleteAllByEmailTemplateId(largeId)).thenReturn(5L);
    doNothing().when(sysLogKafkaProducerService).send(any(LogActionEnum.class), anyString());

    // Act
    emailTemplateDependencyService.deleteWithId(largeId);

    // Assert
    verify(emailTemplateRepository, times(1)).findById(largeId);
    verify(emailTemplateReceiverService, times(1)).deleteAllByEmailTemplateId(largeId);
    verify(sysLogKafkaProducerService, times(1)).send(LogActionEnum.DELETE_TEMPLATE, "Large ID Template");
  }

  @Test
  void deleteWithId_failed_singleDependency() {
    // Arrange
    EmailTemplateEntity emailTemplate = createTestEmailTemplate();
    List<String> singleDependency = Arrays.asList("Single Workflow Template");

    when(emailTemplateRepository.findById(testId)).thenReturn(Optional.of(emailTemplate));
    when(workflowNodeDependencyRepository.findWorkflowTemplateNameByReferenceIdAndType(
        String.valueOf(testId), WorkflowNodeTypeEnum.EMAIL)).thenReturn(singleDependency);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      emailTemplateDependencyService.deleteWithId(testId);
    });

    assertEquals(ErrorCode.EMAIL_TEMPLATE_CAN_NOT_BE_DELETE.getCode(), exception.getCode());
    verify(emailTemplateRepository, times(1)).findById(testId);
    verify(workflowNodeDependencyRepository, times(1))
        .findWorkflowTemplateNameByReferenceIdAndType(String.valueOf(testId), WorkflowNodeTypeEnum.EMAIL);
    verify(emailTemplateReceiverService, never()).deleteAllByEmailTemplateId(any());
  }

  @Test
  void findAllDependenciesById_success_largeNumberOfDependencies() {
    // Arrange
    List<String> manyDependencies = Arrays.asList(
        "Template 1", "Template 2", "Template 3", "Template 4", "Template 5",
        "Template 6", "Template 7", "Template 8", "Template 9", "Template 10"
    );
    when(workflowNodeDependencyRepository.findWorkflowTemplateNameByReferenceIdAndType(
        String.valueOf(testId), WorkflowNodeTypeEnum.EMAIL)).thenReturn(manyDependencies);

    // Act
    EmailTemplateDependenciesResponse result = emailTemplateDependencyService.findAllDependenciesById(testId);

    // Assert
    assertNotNull(result);
    assertEquals(10, result.getWorkflowTemplates().size());
    assertEquals("Template 1", result.getWorkflowTemplates().get(0));
    assertEquals("Template 10", result.getWorkflowTemplates().get(9));
  }

  @Test
  void deleteWithId_success_emailTemplateWithNullName() throws BusinessException {
    // Arrange
    EmailTemplateEntity emailTemplate = new EmailTemplateEntity();
    ReflectionTestUtils.setField(emailTemplate, "id", testId);
    ReflectionTestUtils.setField(emailTemplate, "name", null); // Null name

    when(emailTemplateRepository.findById(testId)).thenReturn(Optional.of(emailTemplate));
    when(workflowNodeDependencyRepository.findWorkflowTemplateNameByReferenceIdAndType(
        String.valueOf(testId), WorkflowNodeTypeEnum.EMAIL)).thenReturn(List.of());
    when(emailTemplateReceiverService.deleteAllByEmailTemplateId(testId)).thenReturn(2L);
    doNothing().when(sysLogKafkaProducerService).send(any(LogActionEnum.class), anyString());

    // Act
    emailTemplateDependencyService.deleteWithId(testId);

    // Assert
    verify(sysLogKafkaProducerService, times(1)).send(LogActionEnum.DELETE_TEMPLATE, (String) null);
  }

  // Helper methods
  private EmailTemplateEntity createTestEmailTemplate() {
    EmailTemplateEntity entity = new EmailTemplateEntity();
    ReflectionTestUtils.setField(entity, "id", testId);
    ReflectionTestUtils.setField(entity, "name", testName);
    ReflectionTestUtils.setField(entity, "subject", "Test Subject");
    ReflectionTestUtils.setField(entity, "content", "Test Content");
    return entity;
  }
}
