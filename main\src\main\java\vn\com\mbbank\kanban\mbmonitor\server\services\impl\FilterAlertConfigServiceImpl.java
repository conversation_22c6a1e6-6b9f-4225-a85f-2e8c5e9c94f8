package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FilterAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.log.FilterConfigLogModelMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupConverter;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.FilterAlertConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.FilterAlertPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.FilterAlertConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.FilterAlertConfigEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.FilterAlertConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.FilterAlertConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.FilterAlertConfigService;

/**
 * Service Logic FilterAlertConfigServiceImpl.
 */
@Service
@RequiredArgsConstructor
public class FilterAlertConfigServiceImpl extends BaseServiceImpl<FilterAlertConfigEntity, Long>
    implements FilterAlertConfigService {
  private final FilterAlertConfigRepository filterAlertConfigRepository;
  private final SysLogKafkaProducerService sysLogKafkaProducerService;
  private final FilterAlertConfigEntityMapper filterAlertConfigEntityMapper =
      FilterAlertConfigEntityMapper.INSTANCE;
  private final FilterAlertConfigResponseMapper filterAlertConfigResponseMapper =
      FilterAlertConfigResponseMapper.INSTANCE;
  private final FilterConfigLogModelMapper filterConfigLogModelMapper = FilterConfigLogModelMapper.INSTANCE;

  @Override
  protected JpaCommonRepository<FilterAlertConfigEntity, Long> getRepository() {
    return filterAlertConfigRepository;
  }

  @Override
  public Page<FilterAlertConfigResponse> findAllWithSearch(FilterAlertPaginationRequest request) {
    var configs =  this.filterAlertConfigRepository.findAllBySearch(request);
    configs.getContent().forEach(model -> model.setRuleGroup(RuleGroupConverter
        .convertToRuleGroup(model.getRuleGroupColumn())));
    return configs;
  }

  @Override
  @Transactional
  public FilterAlertConfigEntity save(FilterAlertConfigRequest request) throws BusinessException {
    var isCreateMode = Objects.isNull(request.getId());
    this.validateFilterAlertRequest(request);
    FilterAlertConfigEntity filterAlertConfigEntity;
    var formatName = StringUtils.capitalizeFirstLetter(request.getName());
    var logName = formatName;
    if (isCreateMode) {
      filterAlertConfigEntity = filterAlertConfigEntityMapper.map(request);
      filterAlertConfigEntity.setActive(false);
    } else {
      filterAlertConfigEntity =
          filterAlertConfigRepository.findById(request.getId()).orElseThrow(() -> new BusinessException(
              ErrorCode.FILTER_ALERT_CONFIG_NOT_FOUND));
      logName = filterAlertConfigEntity.getName();
      filterAlertConfigEntityMapper.merge(filterAlertConfigEntity, request);
    }
    filterAlertConfigEntity.setName(formatName);
    var res = filterAlertConfigRepository.save(filterAlertConfigEntity);
    sysLogKafkaProducerService.send(
        isCreateMode ? LogActionEnum.CREATE_FILTER_CONFIG : LogActionEnum.EDIT_FILTER_CONFIG, logName,
        filterConfigLogModelMapper.map(res));
    return res;
  }

  public FilterAlertConfigResponse findByIdWithDetail(Long id) throws BusinessException {
    var filterAlertConfig = filterAlertConfigRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.FILTER_ALERT_CONFIG_NOT_FOUND));
    return filterAlertConfigResponseMapper.map(filterAlertConfig);
  }

  @Override
  public FilterAlertConfigEntity updateActive(Long id, Boolean active) throws BusinessException {
    var filterAlertConfig = filterAlertConfigRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.FILTER_ALERT_CONFIG_NOT_FOUND));
    filterAlertConfig.setActive(active);
    var res = filterAlertConfigRepository.save(filterAlertConfig);
    sysLogKafkaProducerService.send(active ? LogActionEnum.ACTIVE_FILTER_CONFIG : LogActionEnum.INACTIVE_FILTER_CONFIG,
        res.getName());
    return res;
  }

  @Override
  public void deleteWithId(Long id) throws BusinessException {
    var config = filterAlertConfigRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.FILTER_ALERT_CONFIG_NOT_FOUND));
    this.delete(config);
    sysLogKafkaProducerService.send(LogActionEnum.DELETE_FILTER_CONFIG, config.getName());
  }

  void validateFilterAlertRequest(FilterAlertConfigRequest request) throws BusinessException {
    var name = request.getName().trim();
    var isUpdateMode = Objects.nonNull(request.getId());
    var isExist =
        isUpdateMode ? filterAlertConfigRepository.existsByIdNotAndNameIgnoreCase(request.getId(), name)
            : filterAlertConfigRepository.existsByNameIgnoreCase(name);
    if (isExist) {
      throw new BusinessException(ErrorCode.FILTER_ALERT_CONFIG_NAME_IS_EXISTED);
    }
  }

  @Override
  public List<String> findAllByCustomObjectId(Long id) {
    Set<String> filterAlertConfigNamesSet = new HashSet<>();
    var configs = filterAlertConfigRepository.findAll();
    for (FilterAlertConfigEntity config : configs) {
      if (!KanbanCommonUtil.isEmpty(config.getRuleGroup()) && config.getRuleGroup().checkCustomObject(id)) {
        filterAlertConfigNamesSet.add(config.getName());
      }
    }
    return new ArrayList<>(filterAlertConfigNamesSet);
  }

}
