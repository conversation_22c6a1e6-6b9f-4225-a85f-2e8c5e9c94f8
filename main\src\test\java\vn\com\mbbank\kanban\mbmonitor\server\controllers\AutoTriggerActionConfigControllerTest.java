package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.controllers.admins.AutoTriggerActionConfigController;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AutoTriggerActionConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AutoTriggerActionConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.AutoTriggerActionConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.AutoTriggerActionConfigService;
import vn.com.mbbank.kanban.test.ControllerTest;

@ExtendWith(MockitoExtension.class)
class AutoTriggerActionConfigControllerTest extends ControllerTest {

  @InjectMocks
  @Spy
  private AutoTriggerActionConfigController controller;

  @Mock
  private AutoTriggerActionConfigService service;

  @Mock
  private AutoTriggerActionConfigResponseMapper mapper;
  @Mock
  private CommonAclPermissionService commonAclPermissionService;
  @Mock
  private AutoTriggerActionConfigResponseMapper autoTriggerConfigConfigResponseMapper;

  private AutoTriggerActionConfigResponse response;
  private AutoTriggerActionConfigRequest request;
  private String id;

  @BeforeEach
  void setup() {
    response = new AutoTriggerActionConfigResponse();
    request = new AutoTriggerActionConfigRequest();
    request.setName("test");
    request.setApplicationIds(new ArrayList<>());
    request.setRuleGroup(new RuleGroupType());
    request.setExecutionIds(new ArrayList<>());
    request.setServiceIds(new ArrayList<>());
    request.setTimeSinceLastTrigger(10000L);
    id = UUID.randomUUID().toString();
  }

  @Test
  void findWithId_success() throws BusinessException {
    when(service.findWithId(id)).thenReturn(response);

    ResponseData<AutoTriggerActionConfigResponse> res = controller.findWithId(id);

    verify(service, times(1)).findWithId(id);
    assertNotNull(res);
    assertEquals(response, res.getData());
  }

  @Test
  void findAll_success() throws BusinessException {
    PaginationRequest paginationRequest = new PaginationRequest();
    Page<AutoTriggerActionConfigResponse> page = new PageImpl<>(
            List.of(response),
            PageRequest.of(0, 10),
            1
    );
    when(service.findAllWithSearch(paginationRequest)).thenReturn(page);

    ResponseData<Page<AutoTriggerActionConfigResponse>> res = controller.findAll(paginationRequest);

    verify(service, times(1)).findAllWithSearch(paginationRequest);
    assertNotNull(res);
    assertEquals(1, res.getData().getTotalElements());
  }

  @Test
  void save_success() throws BusinessException {
    doNothing().when(controller).makeSureCreateOrUpdate(any(), any(), any(), any());
    AutoTriggerActionConfigEntity mappedResponse = new AutoTriggerActionConfigEntity();
    mappedResponse.setName("test");
    mappedResponse.setId("2");
    mappedResponse.setLastRun(new Date());
    mappedResponse.setRuleGroup(new RuleGroupType());
    mappedResponse.setActive(true);
    mappedResponse.setTimeSinceLastTrigger(2L);
    when(service.save(request)).thenReturn(mappedResponse);
    ResponseData<AutoTriggerActionConfigResponse> res = controller.save(request);

    verify(service, times(1)).save(request);
    assertNotNull(res);
    assertNotNull(res.getData());
  }

  @Test
  void updateActive_success() throws BusinessException {
    AutoTriggerActionConfigEntity mappedResponse = new AutoTriggerActionConfigEntity();
    mappedResponse.setName("test");
    mappedResponse.setId("2");
    mappedResponse.setLastRun(new Date());
    mappedResponse.setRuleGroup(new RuleGroupType());
    mappedResponse.setActive(true);
    when(service.updateActive(id)).thenReturn(mappedResponse);
    ResponseData<AutoTriggerActionConfigResponse> res = controller.updateActive(id);
    verify(service, times(1)).updateActive(id);
    assertNotNull(res);
    assertTrue(res.getData().getActive());
  }

  @Test
  void deleteById_success() throws BusinessException {
    doNothing().when(service).deleteWithId(id);

    ResponseData<String> res = controller.deleteById(id);

    verify(service, times(1)).deleteWithId(id);
    assertNotNull(res);
    assertEquals("OK", res.getData());
  }
}
