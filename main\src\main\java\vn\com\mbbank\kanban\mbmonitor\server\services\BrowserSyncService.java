package vn.com.mbbank.kanban.mbmonitor.server.services;

/**
 * Service interface for synchronizing browser binaries (e.g. Chromium and Firefox)
 * from application resources to a specified executable directory.
 */
public interface BrowserSyncService {
  
  /**
   * Checks whether the required browser zip files (Chromium and Firefox)
   * have already been extracted to the expected runtime directory.
   * If not, copies them from the application's classpath resources
   * and extracts the zip contents.
   *
   * @return A string message indicating the result of the sync operation.
   * @throws Exception if any error occurs during file copy or extraction.
   */
  String checkAndSyncBrowsers() throws Exception;
}
