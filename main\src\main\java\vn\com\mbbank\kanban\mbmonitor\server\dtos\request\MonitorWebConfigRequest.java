package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.BrowserEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MonitorTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.ActionModel;

/**
 * Model request service to create or update monitor web config.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MonitorWebConfigRequest {
  String id;
  @NotBlank
  @Size(min = 1)
  @Size(max = CommonConstants.COMMON_NAME_MAX_LENGTH)
  String name;
  
  @Size(max = CommonConstants.COMMON_DESCRIPTION_MAX_LENGTH)
  String description;
  
  @NotBlank
  @Size(min = 1)
  @Size(max = CommonConstants.MONITOR_WEB_WEB_URL_MAX_LENGTH)
  String webUrl;
  
  @NotNull
  MonitorTypeEnum monitorType;
  
  @Max(value = CommonConstants.MONITOR_WEB_TIMEOUT_MAX_VALUE, message = "Timeout value has max {value}")
  @Min(value = CommonConstants.MONITOR_WEB_TIMEOUT_MIN_VALUE, message = "Timeout value has min {value}")
  @NotNull
  Integer timeout;
  
  @NotNull
  BrowserEnum browser;
  
  @NotNull
  List<ActionModel> actions;
  
  @NotNull
  String serviceId;
  
  @NotNull
  String applicationId;
  
  @NotNull
  Long priorityId;
  
  @NotNull
  @Size(min = 1)
  @Size(max = CommonConstants.MONITOR_WEB_CONTACT_MAX_LENGTH)
  String contact;
  
  @NotNull
  String content;
  
  @NotNull
  String contentJson;
  
  List<ActionModel> authActions;
  boolean active;
  Integer[] months;
  Integer[] dayOfMonths;
  Integer[] dayOfWeeks;
  Integer[] hours;
  Integer[] minutes;
}
