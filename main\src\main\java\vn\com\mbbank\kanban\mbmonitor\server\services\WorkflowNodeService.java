package vn.com.mbbank.kanban.mbmonitor.server.services;

import java.util.List;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowNodeEntity;

/**
 * interface logic WorkflowTemplateNodeService.
 */
public interface WorkflowNodeService extends BaseService<WorkflowNodeEntity, String> {

  /**
   * delete node by workflowId.
   *
   * @param workflowId workflowId
   * @return number of result
   */
  int deleteAllByWorkflowId(String workflowId);

  /**
   * save workflow Template node.
   *
   * @param workflowId workflowId
   * @param workflow WorkflowModel
   */
  void save(String workflowId, WorkflowModel workflow) throws BusinessException;

  /**
   * findAllByWorkflowId.
   *
   * @param workflowId workflowId
   * @return list of result
   */
  List<WorkflowNodeEntity> findAllByWorkflowId(String workflowId);
}