package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.WorkflowNodeDependencyRepositoryCustom;

/**
 * variableRepositoryCustomImpl.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class WorkflowNodeDependencyRepositoryCustomImpl implements WorkflowNodeDependencyRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public List<String> findWorkflowTemplateNameByReferenceIdAndType(String referenceId, WorkflowNodeTypeEnum type) {
    var query = new PrepareQuery("""
        SELECT workflowTemplate.NAME
        FROM WORKFLOW_TEMPLATE workflowTemplate
        WHERE workflowTemplate.WORKFLOW_ID IN
           (SELECT WORKFLOW.id
            FROM WORKFLOW
                     LEFT JOIN WORKFLOW_NODE_DEPENDENCY workflowNodeDependency
                               ON WORKFLOW.ID = workflowNodeDependency.WORKFLOW_ID
            WHERE workflowNodeDependency.REFERENCE_ID = :referenceId
              AND workflowNodeDependency.TYPE = :type)
        """, Map.of("referenceId", referenceId, "type", type.name()));
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), String.class);
  }
}
