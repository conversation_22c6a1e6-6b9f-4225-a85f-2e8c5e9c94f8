package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.Collection;
import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;

/**
 * Repository table Execution.
 */
@Repository
public interface ExecutionRepository
    extends JpaCommonRepository<ExecutionEntity, String>, ExecutionRepositoryCustom {

  /**
   * Finds by executionGroupId.
   *
   * @param executionGroupId the ID of group
   * @return list of Execution
   */
  List<ExecutionEntity> findAllByExecutionGroupId(String executionGroupId);

  /**
   * Finds by executionGroupId.
   *
   * @param executionGroupId the ID of group
   * @param name             name of execution
   * @return list of Execution
   */
  List<ExecutionEntity> findAllByExecutionGroupIdAndNameContainingIgnoreCase(String executionGroupId, String name);

  /**
   * Finds by executionGroupId.
   *
   * @param ids  list id of execution
   * @param name name of execution
   * @return list of Execution
   */
  List<ExecutionEntity> findAllByIdInAndNameContainingIgnoreCase(List<String> ids, String name);

  /**
   * check execution existed by id, name and deleted status, executionGroupId.
   *
   * @param id               execution id
   * @param name             execution name
   * @param executionGroupId executionGroupId
   * @return existed or not
   */
  boolean existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(String id, String name, String executionGroupId);

  /**
   * check execution existed by name and deleted status, executionGroupId.
   *
   * @param name             execution name
   * @param executionGroupId executionGroupId
   * @return existed or not
   */
  boolean existsByNameIgnoreCaseAndExecutionGroupId(String name, String executionGroupId);

  /**
   * Checks if any execution record exists with the given databaseConnectionId.
   *
   * @param databaseConnectionId The id of the database connection.
   * @return true if at least one execution record with the specified name exists, false otherwise.
   */
  boolean existsByDatabaseConnectionId(Long databaseConnectionId);

  /**
   * find all by ids.
   *
   * @param ids for find
   * @return a list of execution
   */
  List<ExecutionEntity> findAllByIdIn(List<String> ids);

  /**
   * get execution list by id in.
   *
   * @param ids ids.
   * @return list if execution.
   */
  List<ExecutionEntity> findAllByIdIn(Collection<String> ids);
}
