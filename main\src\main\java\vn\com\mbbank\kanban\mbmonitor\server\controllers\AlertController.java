package vn.com.mbbank.kanban.mbmonitor.server.controllers;


import jakarta.validation.Valid;
import java.io.IOException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ExportDataResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertCursor;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ExportFileAlertRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AlertResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertService;

/**
 * Controller logic alert.
 */
@RestController
@RequestMapping(ServerUrl.ALERT_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AlertController extends BaseController {
  AlertService alertService;

  /**
   * find all alert by paginationRequest.
   *
   * @param paginationRequest Pagination parameters for the request.
   * @return ResponseEntity containing the response data with HTTP status OK.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.REPORT, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  public ResponseData<CursorPageResponse<AlertResponse, AlertCursor>> findAll(
      @ModelAttribute AlertPaginationRequest paginationRequest) {
    return ResponseUtils.success(alertService.findAll(paginationRequest));
  }

  /**
   * count all alert by paginationRequest.
   *
   * @param paginationRequest Pagination parameters for the request.
   * @return ResponseEntity containing the response data with count of all alert.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.REPORT, action = PermissionActionEnum.VIEW)
  })
  @GetMapping("/count")
  public ResponseData<Long> countAllAlert(
      @ModelAttribute AlertPaginationRequest paginationRequest) {
    return ResponseUtils.success(alertService.countAllAlert(paginationRequest));
  }

  /**
   * API to export a file as a stream based on the provided request data.
   *
   * @param request The request object containing the necessary data for file export.
   *                The request is validated using the {@link Valid} annotation.
   * @return StreamingResponseBody A response entity containing the file to be exported as a stream.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.REPORT, action = PermissionActionEnum.EXPORT)
  })
  @PostMapping("/export")
  public ResponseData<ExportDataResponse> export(
      @RequestBody @Valid ExportFileAlertRequest request)
      throws IOException, BusinessException {
    return ResponseUtils.success(alertService.exportFile(request));
  }
}
