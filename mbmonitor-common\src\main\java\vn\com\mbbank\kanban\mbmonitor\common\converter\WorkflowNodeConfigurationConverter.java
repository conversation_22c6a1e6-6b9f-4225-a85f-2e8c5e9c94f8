package vn.com.mbbank.kanban.mbmonitor.common.converter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.BaseNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.EmailNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.ExecutionNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.TeamsNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;

/**
 * WorkflowTemplateNodeConfigurationConverter.
 */
@Converter
public class WorkflowNodeConfigurationConverter implements AttributeConverter<BaseNodeConfigurationModel, String> {

  @Override
  public String convertToDatabaseColumn(BaseNodeConfigurationModel attribute) {
    if (attribute == null) {
      return null;
    }
    return JSON.toJSONString(attribute);
  }

  @Override
  public BaseNodeConfigurationModel convertToEntityAttribute(String dbData) {
    if (KanbanCommonUtil.isEmpty(dbData)) {
      return null;
    }
    JSONObject obj = JSON.parseObject(dbData);
    WorkflowNodeTypeEnum type = obj.getObject("type", WorkflowNodeTypeEnum.class);
    return switch (type) {
      case START, END -> obj.toJavaObject(BaseNodeConfigurationModel.class);
      case EMAIL -> obj.toJavaObject(EmailNodeConfigurationModel.class);
      case TEAMS -> obj.toJavaObject(TeamsNodeConfigurationModel.class);
      case EXECUTION, SQL, PYTHON, API -> obj.toJavaObject(ExecutionNodeConfigurationModel.class);
    };
  }
}
