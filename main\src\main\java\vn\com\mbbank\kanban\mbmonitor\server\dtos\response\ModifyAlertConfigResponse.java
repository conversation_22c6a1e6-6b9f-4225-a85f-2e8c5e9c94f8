package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

/**
 * Response DTO for representing filter alert configurations.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ModifyAlertConfigResponse {
  Long id;
  String name;
  String description;
  @Builder.Default
  Boolean active = true;
  AlertSourceTypeEnum type;
  RuleGroupType ruleGroup;
  int position;
  @Builder.Default
  List<ServiceResponse> services = new ArrayList<>();
  @Builder.Default
  List<ApplicationResponse> applications = new ArrayList<>();
  @Builder.Default
  List<ModifyAlertConfigModifyResponse> modifies = new ArrayList<>();
}
