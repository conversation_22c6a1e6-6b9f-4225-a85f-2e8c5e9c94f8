package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

/**
 * Response DTO for representing filter alert configurations.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FilterAlertConfigResponse {
  Long id;
  String name;
  String description;
  @Builder.Default
  Boolean active = true;
  RuleGroupType ruleGroup;
  String ruleGroupColumn;
}
