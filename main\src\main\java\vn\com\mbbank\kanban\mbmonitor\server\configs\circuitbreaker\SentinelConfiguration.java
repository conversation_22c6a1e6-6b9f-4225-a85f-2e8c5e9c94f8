package vn.com.mbbank.kanban.mbmonitor.server.configs.circuitbreaker;

import com.alibaba.csp.sentinel.annotation.aspectj.SentinelResourceAspect;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRuleManager;
import com.alibaba.csp.sentinel.slots.block.degrade.circuitbreaker.CircuitBreaker;
import com.alibaba.csp.sentinel.slots.block.degrade.circuitbreaker.EventObserverRegistry;
import com.alibaba.csp.sentinel.util.TimeUtil;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.server.constants.SentinelConstants;

/**
 * Configuration class for setting up Sentinel integration.
 */
@Configuration
@ConditionalOnExpression(value = "${mbmonitor.sentinel.enabled:true}")
public class SentinelConfiguration {

  private final Logger logger = LoggerFactory.getLogger(SentinelConfiguration.class);

  @Autowired
  private MeterRegistry meterRegistry;

  @Autowired
  private DegradeRuleConfig degradeRuleConfig;

  /**
   * Configures the SentinelResourceAspect bean.
   *
   * @return SentinelResourceAspect instance
   */
  @Bean
  public SentinelResourceAspect sentinelResourceAspect() {
    return new SentinelResourceAspect();
  }

  /**
   * Initializes Sentinel rules from {@link DegradeRuleConfig} and registers state change observer.
   *
   * @return An arbitrary object to satisfy @Bean return type requirements
   */
  @Bean
  public Object initRules() {
    List<DegradeRule> rules = degradeRuleConfig.getRules();
    DegradeRuleManager.loadRules(rules);
    registerStateChangeObserver();
    return new Object();
  }

  /**
   * Registers a state change observer for Sentinel circuit breakers.
   */

  protected void registerStateChangeObserver() {
    try {
      CircuitBreakerStates states = new CircuitBreakerStates();
      List<DegradeRule> rules = DegradeRuleManager.getRules();
      for (DegradeRule dr : rules) {
        CircuitBreakerState circuitBreakerState =
            KanbanCommonUtil.toObject(dr, CircuitBreakerState.class);
        circuitBreakerState.setName(dr.getResource());
        states.addCircuitBreakerState(circuitBreakerState);
      }

      EventObserverRegistry.getInstance()
          .addStateChangeObserver(SentinelConstants.TYPE_STATE_CHANGE_OBSERVER,
              (prevState, newState, rule, snapshotValue) -> {
                try {
                  addLogWhenStateChange(prevState, newState, snapshotValue);
                  CircuitBreakerState circuitBreakerState =
                      states.getHashCircuitBreaker().get(rule.getResource());
                  if (circuitBreakerState != null) {
                    circuitBreakerState.setState(this.getCircuitBreakerState(newState.toString()));
                  } else {
                    circuitBreakerState = KanbanCommonUtil.toObject(rule, CircuitBreakerState.class);
                    circuitBreakerState.setName(rule.getResource());
                    circuitBreakerState.setState(this.getCircuitBreakerState(newState.toString()));
                    states.addCircuitBreakerState(circuitBreakerState);
                  }
                } catch (Exception e) {
                  logger.warn(SentinelConstants.MONITOR_ERROR, e);
                }
              });

      Iterator<CircuitBreakerState> circuitBreakerStates =
          states.getHashCircuitBreaker().values().iterator();
      while (circuitBreakerStates.hasNext()) {
        CircuitBreakerState cb = circuitBreakerStates.next();
        List<Tag> tags = addTag(cb);
        Gauge.builder(SentinelConstants.SENTINEL_CB_MONITORING, cb, (state) -> cb.getState())
            .tags(tags)
            .description(SentinelConstants.SENTINEL_CB_STATE_TRACKING).register(this.meterRegistry);
      }
    } catch (Exception ex) {
      logger.warn(SentinelConstants.REGISTER_ERROR, ex);
    }
  }

  protected void addLogWhenStateChange(CircuitBreaker.State prevState,
                                       CircuitBreaker.State newState,
                                       double snapshotValue) {
    String logMessage;
    if (newState == CircuitBreaker.State.OPEN) {
      logMessage = String.format("%s -> OPEN at %d, snapshotValue=%.2f", prevState.name(),
          TimeUtil.currentTimeMillis(), snapshotValue);
      logger.error(logMessage);
    } else {
      logMessage = String.format("%s -> %s at %d", prevState.name(), newState.name(),
          TimeUtil.currentTimeMillis());
      logger.info(logMessage);
    }
    System.err.println(logMessage);
  }

  protected double getCircuitBreakerState(String state) {
    return switch (CircuitBreaker.State.valueOf(state)) {
      case OPEN -> 2.0D;
      case HALF_OPEN -> 1.0D;
      default -> 0.0D;
    };
  }

  private List<Tag> addTag(CircuitBreakerState cb) {
    List<Tag> tags = new ArrayList<>();
    tags.add(Tag.of("name", cb.getName()));
    tags.add(Tag.of("resource", cb.getResource()));
    tags.add(Tag.of("grade", String.valueOf(cb.getGrade())));
    tags.add(Tag.of("minRequestAmount", String.valueOf(cb.getMinRequestAmount())));
    tags.add(Tag.of("slowRatioThreshold", String.valueOf(cb.getSlowRatioThreshold())));
    tags.add(Tag.of("statIntervalMs", String.valueOf(cb.getStatIntervalMs())));
    tags.add(Tag.of("count", String.valueOf(cb.getCount())));
    tags.add(Tag.of("timeWindow", String.valueOf(cb.getTimeWindow())));
    return tags;
  }
}