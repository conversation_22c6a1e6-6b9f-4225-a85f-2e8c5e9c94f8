package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.annotations.SkipAuthentication;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.dtos.TokenDTO;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AuthRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.RefreshTokenResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.AuthenticationService;

/**
 * AuthController.
 */
@RestController
@RequestMapping(ServerUrl.AUTHENTICATE_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AuthenticationController {
  AuthenticationService service;

  /**
   * Authorize.
   *
   * @param request to login
   * @return token
   */
  @SkipAuthentication
  @PostMapping(value = "/login")
  public ResponseData<TokenDTO> login(@RequestBody @Valid AuthRequest request) throws Exception {
    return ResponseUtils.success(service.generateLoginToken(request));
  }

  /**
   * Refresh token.
   *
   * @param refreshToken to refresh token
   * @param request the HttpServletRequest
   * @return token
   */
  @GetMapping(value = "/refresh-token")
  public ResponseData<RefreshTokenResponse> refreshToken(@RequestParam String refreshToken, HttpServletRequest request)
          throws Exception {
    return ResponseUtils.success(service.refreshToken(refreshToken, request));
  }

  /**
   * logout api.
   *
   * @param refreshToken for delete
   * @return responseData
   */
  @GetMapping(value = "/logout")
  public ResponseData<Integer> logout(@RequestParam String refreshToken) {
    return ResponseUtils.success(service.logout(refreshToken));
  }
}