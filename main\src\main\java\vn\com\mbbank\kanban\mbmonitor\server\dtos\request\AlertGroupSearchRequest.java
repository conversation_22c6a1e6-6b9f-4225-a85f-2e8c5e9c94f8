package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * AlertGroupSearchRequest.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlertGroupSearchRequest {
  List<String> serviceIds;
  List<String> applicationIds;
  String content;
  String recipient;
  List<Long> alertPriorityConfigIds;
}
