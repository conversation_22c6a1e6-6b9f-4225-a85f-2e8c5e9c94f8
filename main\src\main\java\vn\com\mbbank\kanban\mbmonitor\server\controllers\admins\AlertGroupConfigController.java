package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertGroupConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertGroupConfigUpdatePositionRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AlertGroupConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.AlertGroupConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertGroupConfigService;

/**
 * Controller logic AlertGroupConfigController.
 */
@RestController
@RequestMapping(ServerUrl.ALERT_GROUP_CONFIG_URL)
@RequiredArgsConstructor
public class AlertGroupConfigController extends BaseController {

  private final AlertGroupConfigService alertGroupConfigService;
  private final AlertGroupConfigResponseMapper alertGroupConfigResponseMapper =
      AlertGroupConfigResponseMapper.INSTANCE;

  /**
   * Api find group config by ID.
   *
   * @param id id of priority config.
   * @return AlertGroupConfigResponse.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ALERT_GROUP_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping(value = "/{id}")
  ResponseData<AlertGroupConfigResponse> findById(@PathVariable Long id)
      throws BusinessException {
    return ResponseUtils.success(
        alertGroupConfigService.findByIdWithDetail(id));
  }

  /**
   * Api find a list of group config.
   *
   * @param withDeleted option to fetch with rawValues.
   * @param search      search
   * @return List of AlertPriorityConfigResponse
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ALERT_GROUP_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  ResponseData<List<AlertGroupConfigResponse>> findAll(
      @RequestParam(value = "withDeleted", required = false, defaultValue = "false")
      boolean withDeleted,
      @RequestParam(value = "search", required = false, defaultValue = "")
      String search) {
    return ResponseUtils.success(
        alertGroupConfigResponseMapper.map(
            alertGroupConfigService.findAllWithDeletedAndSearch(withDeleted, search)));
  }

  /**
   * Api save group config.
   *
   * @param alertGroupConfigRequest input data.
   * @return AlertGroupConfigResponse.
   * @throws BusinessException exception
   */
  @PostMapping
  ResponseData<AlertGroupConfigResponse> save(
      @Valid @RequestBody AlertGroupConfigRequest alertGroupConfigRequest)
      throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.ALERT_GROUP_CONFIG, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(alertGroupConfigRequest.getId()));
    return ResponseUtils.success(
        alertGroupConfigResponseMapper.map(alertGroupConfigService.save(alertGroupConfigRequest)));
  }

  /**
   * Api delete priority config.
   *
   * @param id priority config id.
   * @return String
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ALERT_GROUP_CONFIG, action = PermissionActionEnum.DELETE)
  })
  @DeleteMapping(value = "/{id}")
  ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    alertGroupConfigService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }

  /**
   * Api delete priority config.
   *
   * @param request alert priority config update info.
   * @return list of alert priority config
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ALERT_GROUP_CONFIG, action = PermissionActionEnum.EDIT)
  })
  @PutMapping("/position")
  public ResponseData<List<AlertGroupConfigResponse>> updatePosition(
      @RequestBody @Valid AlertGroupConfigUpdatePositionRequest request) throws BusinessException {
    return ResponseUtils.success(
        alertGroupConfigResponseMapper.map(alertGroupConfigService.updatePosition(request)));
  }


  /**
   * Api delete priority config.
   *
   * @param id     alert group id.
   * @param active active status
   * @return list of alert priority config
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ALERT_GROUP_CONFIG, action = PermissionActionEnum.EDIT),
  })
  @PutMapping("/{id}")
  public ResponseData<AlertGroupConfigResponse> updateActive(@PathVariable Long id,
                                                             @RequestParam(value = "active")
                                                             Boolean active)
      throws BusinessException {
    return ResponseUtils.success(
        alertGroupConfigResponseMapper.map(alertGroupConfigService.updateActive(id, active)));
  }
}
