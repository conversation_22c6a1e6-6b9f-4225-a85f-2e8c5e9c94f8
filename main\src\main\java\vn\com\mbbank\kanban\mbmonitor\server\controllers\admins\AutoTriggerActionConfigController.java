package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import jakarta.validation.Valid;
import java.util.Collections;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AutoTriggerActionConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AutoTriggerActionConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.AutoTriggerActionConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.AutoTriggerActionConfigService;

/**
 * Controller logic AutoTriggerActionConfigController.
 */
@RestController
@RequestMapping(ServerUrl.AUTO_TRIGGER_ACTION_CONFIG_URL)
@RequiredArgsConstructor
public class AutoTriggerActionConfigController extends BaseController {
  private final AutoTriggerActionConfigService autoTriggerConfigConfigService;
  private final AutoTriggerActionConfigResponseMapper autoTriggerConfigConfigResponseMapper =
          AutoTriggerActionConfigResponseMapper.INSTANCE;

  /**
   * Api find config by ID.
   *
   * @param id id of config.
   * @return AutoTriggerConfigConfigResponse.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum. AUTO_TRIGGER_ACTION_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping(value = "/{id}")
  public ResponseData<AutoTriggerActionConfigResponse> findWithId(@PathVariable String id)
      throws BusinessException {
    return ResponseUtils.success(autoTriggerConfigConfigService.findWithId(id));
  }

  /**
   * Api find a list of config.
   *
   * @param  paginationRequest for page
   * @return List of AutoTriggerConfigConfigResponse
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.AUTO_TRIGGER_ACTION_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  public ResponseData<Page<AutoTriggerActionConfigResponse>> findAll(
          @ModelAttribute PaginationRequest paginationRequest)
      throws BusinessException {
    return ResponseUtils.success(autoTriggerConfigConfigService.findAllWithSearch(paginationRequest));
  }

  /**
   * Api save config.
   *
   * @param autoTriggerConfigConfigRequest input data.
   * @return AutoTriggerConfigConfigResponse.
   * @throws BusinessException exception
   */
  @PostMapping
  public ResponseData<AutoTriggerActionConfigResponse> save(
          @Valid @RequestBody AutoTriggerActionConfigRequest autoTriggerConfigConfigRequest) throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.AUTO_TRIGGER_ACTION_CONFIG, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(autoTriggerConfigConfigRequest.getId()));
    return ResponseUtils.success(
        autoTriggerConfigConfigResponseMapper.map(autoTriggerConfigConfigService.save(autoTriggerConfigConfigRequest)));
  }

  /**
   * Api delete config.
   *
   * @param id config id.
   * @return String
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.AUTO_TRIGGER_ACTION_CONFIG, action = PermissionActionEnum.DELETE)
  })
  @DeleteMapping(value = "/{id}")
  public ResponseData<String> deleteById(@PathVariable String id) throws BusinessException {
    autoTriggerConfigConfigService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }


  /**
   * Api delete config.
   *
   * @param id     config id.
   * @return list of config
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.AUTO_TRIGGER_ACTION_CONFIG, action = PermissionActionEnum.EDIT),
  })
  @PutMapping("/{id}/toggle-status")
  public ResponseData<AutoTriggerActionConfigResponse> updateActive(@PathVariable String id)
      throws BusinessException {
    return ResponseUtils.success(
        autoTriggerConfigConfigResponseMapper.map(autoTriggerConfigConfigService.updateActive(id)));
  }

}
