package vn.com.mbbank.kanban.mbmonitor.common.converter;

import com.alibaba.fastjson2.JSON;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowEdgeModel;

/**
 * WorkflowTemplateNodePositionConverter.
 */
@Converter
public class WorkflowEdgeConverter implements AttributeConverter<WorkflowEdgeModel, String> {

  @Override
  public String convertToDatabaseColumn(WorkflowEdgeModel attribute) {
    if (attribute == null) {
      return null;
    }
    return JSON.toJSONString(attribute);
  }

  @Override
  public WorkflowEdgeModel convertToEntityAttribute(String dbData) {
    try {
      return JSON.parseObject(dbData, WorkflowEdgeModel.class);
    } catch (Exception e) {
      return null;
    }
  }
}
