package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowTemplateEntity;

/**
 * Repository WorkflowTemplateRepository.
 */
@Repository
public interface WorkflowTemplateRepository
    extends JpaCommonRepository<WorkflowTemplateEntity, String> {

  /**
   * check existed by id, name and deleted status.
   *
   * @param id   id
   * @param name name
   * @return existed or not
   */
  boolean existsByIdNotAndNameIgnoreCase(String id, String name);

  /**
   * check existed by name and deleted status.
   *
   * @param name name
   * @return existed or not
   */
  boolean existsByNameIgnoreCase(String name);

}
