package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CustomInputResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomInputEntity;

/**
 * CustomInputResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CustomInputResponseMapper extends KanbanBaseMapper<CustomInputResponse, CustomInputEntity> {
  CustomInputResponseMapper INSTANCE = Mappers.getMapper(CustomInputResponseMapper.class);

  /**
  *  map from CustomInputEntity to CustomInputResponse.
  *
  * @param entity CustomInputEntity.
  * @return CustomInputResponse.
  */
  @Mapping(target = "configuration", source = "configuration")
  CustomInputResponse map(CustomInputEntity entity);
}
