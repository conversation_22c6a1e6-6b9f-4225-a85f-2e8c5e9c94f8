package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import java.io.IOException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ExportDataResponse;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExportDataService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 02/06/2025
 */
@RestController
@RequestMapping(ServerUrl.EXPORT_DATA_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ExportDataController extends BaseController {
  ExportDataService exportDataService;


  /**
   * findAll by paging with owner.
   *
   * @param paginationRequest PaginationRequestDTO
   * @return Page Export data entity.
   */
  @GetMapping
  public ResponseData<Page<ExportDataResponse>> findAll(@ModelAttribute PaginationRequest paginationRequest)
      throws BusinessException {
    return ResponseUtils.success(exportDataService.findAll(paginationRequest));
  }

  /**
   * download file with owner.
   *
   * @param  id id of export data entity.
   * @return StreamingResponseBody.
   */
  @GetMapping("{id}")
  public ResponseEntity<StreamingResponseBody> download(@PathVariable String id) throws BusinessException, IOException {
    return ResponseEntity.ok()
        .contentType(MediaType.APPLICATION_OCTET_STREAM)
        .header(HttpHeaders.CONTENT_DISPOSITION, CommonConstants.CONTENT_DISPOSITION_ATTACHMENT)
        .body(exportDataService.download(id));
  }

  /**
   * delete file with owner.
   *
   * @param  id id of export data entity.
   * @return StreamingResponseBody.
   */
  @DeleteMapping("{id}")
  public ResponseData<String> deleteById(@PathVariable String id) throws BusinessException {
    exportDataService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }
}
