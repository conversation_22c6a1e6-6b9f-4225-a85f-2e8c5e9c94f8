package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.VariableRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.VariableResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.common.constants.ExecutionConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.services.VariableService;
import vn.com.mbbank.kanban.test.ControllerTest;

@ExtendWith(MockitoExtension.class)
class VariableControllerTest extends ControllerTest {

  @Mock
  private VariableService variableService;

  @Mock
  private CommonAclPermissionService commonAclPermissionService;

  @InjectMocks
  @Spy
  private VariableController variableController;

  private VariableResponse variableResponse;
  private VariableRequest variableRequest;
  private VariableEntity variableEntity;
  private String testId;

  @BeforeEach
  void setUp() {
    // Arrange common test data
    testId = "test-id-123";

    variableEntity = new VariableEntity();
    variableEntity.setId(testId);
    variableEntity.setName("Test Variable");
    variableEntity.setDescription("Test Description");
    variableEntity.setValue("Test Value");
    variableEntity.setHidden(false);

    variableResponse = VariableResponse.builder()
        .id(testId)
        .name("Test Variable")
        .description("Test Description")
        .value("Test Value")
        .hidden(false)
        .build();

    variableRequest = VariableRequest.builder()
        .id(testId)
        .name("Test Variable")
        .description("Test Description")
        .value("Test Value")
        .hidden(false)
        .build();
  }

  // Permission Tests
  @Test
  void findById_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.VARIABLE, PermissionActionEnum.VIEW)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "findById", VariableController.class);
  }

  @Test
  void findAllWithPaging_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.VARIABLE, PermissionActionEnum.VIEW)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "findAllWithPaging", VariableController.class);
  }

  @Test
  void findAll_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.EXECUTION, PermissionActionEnum.EDIT),
        new AclPermissionModel(PermissionModuleEnum.EXECUTION, PermissionActionEnum.CREATE),
        new AclPermissionModel(PermissionModuleEnum.EXECUTION, PermissionActionEnum.VIEW)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "findAll", VariableController.class);
  }

  @Test
  void deleteById_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.VARIABLE, PermissionActionEnum.DELETE)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "deleteById", VariableController.class);
  }

  // Functional Tests
  @Test
  void findById_success_whenFound() throws BusinessException {
    // Arrange
    when(variableService.findWithId(testId)).thenReturn(variableEntity);

    // Act
    ResponseData<VariableResponse> response = variableController.findById(testId);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(testId, response.getData().getId());
    assertEquals("Test Variable", response.getData().getName());
    assertEquals("Test Value", response.getData().getValue());
    verify(variableService, times(1)).findWithId(testId);
  }

  @Test
  void findById_success_whenFoundWithHiddenValue() throws BusinessException {
    // Arrange
    variableEntity.setHidden(true);
    when(variableService.findWithId(testId)).thenReturn(variableEntity);

    // Act
    ResponseData<VariableResponse> response = variableController.findById(testId);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(testId, response.getData().getId());
    assertEquals("Test Variable", response.getData().getName());
    assertEquals(ExecutionConstants.HIDDEN_VARIABLE_PLACEHOLDER, response.getData().getValue());
    verify(variableService, times(1)).findWithId(testId);
  }

  @Test
  void findAllWithPaging_success() {
    // Arrange
    PaginationRequest requestDTO = new PaginationRequest();
    Page<VariableEntity> pageEntity = new PageImpl<>(Collections.singletonList(variableEntity));
    when(variableService.findAll(any(PaginationRequest.class))).thenReturn(pageEntity);

    // Act
    ResponseData<Page<VariableResponse>> response = variableController.findAllWithPaging(requestDTO);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(1, response.getData().getTotalElements());
    assertEquals(testId, response.getData().getContent().get(0).getId());
    assertEquals("Test Variable", response.getData().getContent().get(0).getName());
    verify(variableService, times(1)).findAll(requestDTO);
  }

  @Test
  void findAll_success() {
    // Arrange
    List<VariableEntity> listEntity = Collections.singletonList(variableEntity);
    when(variableService.findAll()).thenReturn(listEntity);

    // Act
    ResponseData<List<VariableResponse>> response = variableController.findAll();

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(1, response.getData().size());
    assertEquals(testId, response.getData().get(0).getId());
    assertEquals("Test Variable", response.getData().get(0).getName());
    verify(variableService, times(1)).findAll();
  }

  @Test
  void save_success_whenCreateOrUpdate() throws BusinessException {
    // Arrange
    doNothing().when(variableController).makeSureCreateOrUpdate(any(), any(), any(), any());
    when(variableService.createOrUpdate(any(VariableRequest.class))).thenReturn(variableEntity);

    // Act
    ResponseData<VariableResponse> response = variableController.save(variableRequest);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(testId, response.getData().getId());
    assertEquals("Test Variable", response.getData().getName());
    verify(variableService, times(1)).createOrUpdate(variableRequest);
    verify(variableController, times(1)).makeSureCreateOrUpdate(
        PermissionModuleEnum.VARIABLE,
        PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT,
        Collections.singleton(testId));
  }

  @Test
  void deleteById_success() throws BusinessException {
    // Arrange
    doNothing().when(variableService).deleteWithId(testId);

    // Act
    ResponseData<String> response = variableController.deleteById(testId);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertEquals("OK", response.getData());
    verify(variableService, times(1)).deleteWithId(testId);
  }
}
