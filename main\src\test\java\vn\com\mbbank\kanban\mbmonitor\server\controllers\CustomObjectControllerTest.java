package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.server.controllers.admins.CustomObjectController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CustomObjectDependenciesResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.CustomObjectService;
import vn.com.mbbank.kanban.test.ApplicationTest;

@ExtendWith({MockitoExtension.class})
class CustomObjectControllerTest extends ApplicationTest {
  @Mock
  CustomObjectService customObjectService;
  @InjectMocks
  CustomObjectController customObjectController;

  @Test
  void findAll_success_caseWithoutDeleted() {
    when(customObjectService.findAllWithPaging(any(PaginationRequest.class))).thenReturn(Page.empty());
    var res = customObjectController.findAll(new PaginationRequest(), "", false);
    verify(customObjectService, times(1)).findAllWithPaging(any(PaginationRequest.class));
    assertEquals(0, res.getData().getContent().size());
  }

  @Test
  public void findDependenciesByServiceId_success() throws Exception {
    when(customObjectService.findAllDependenciesById(any())).thenReturn(new CustomObjectDependenciesResponse());
    var res = customObjectController.findAllDependenciesById(1L);
    assertNotNull(res);
    Assertions.assertEquals(200, res.getStatus());
  }

  @Test
  void findAll_success_caseWithDeleted() {
    when(customObjectService.findAllWithPagingAndWithDeleted(any(PaginationRequest.class))).thenReturn(Page.empty());
    var res = customObjectController.findAll(new PaginationRequest(), "", true);
    verify(customObjectService).findAllWithPagingAndWithDeleted(any(PaginationRequest.class));
    assertEquals(0, res.getData().getContent().size());
  }

  @Test
  void findById_success() {
    when(customObjectService.findById(any())).thenReturn(null);
    var res = customObjectController.findById(1L);
    verify(customObjectService, times(1)).findById(any());
    assertNull(res.getData());
  }

  @Test
  void createOrUpdate_success() throws BusinessException {
    when(customObjectService.createOrUpdate(any())).thenReturn((null));
    var res = customObjectController.createOrUpdate(any());
    verify(customObjectService, times(1)).createOrUpdate(any());
    assertNull(res.getData());
  }

  @Test
  void deleteById_success() throws BusinessException {
    var res = customObjectController.deleteById(any());
    verify(customObjectService, times(1)).deleteCustomObjectById(any());
    assertEquals("OK", res.getData());
  }

  @Test
  void deleteByIdIn_success() throws BusinessException {
    var res = customObjectController.deleteByIdIn(any());
    verify(customObjectService, times(1)).deleteByIdIn(any());
    assertEquals("OK", res.getData());
  }
}

