package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.converter.ExecutionKeyValueListConverter;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecutionKeyValue;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionApiAuthTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionApiMethodEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionBodyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionContentTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.HttpVersionEnum;

@Entity
@Getter
@Setter
@Table(name = TableName.EXECUTION_API_CONFIG)
@DynamicInsert
@KanbanAutoGenerateUlId
public class ExecutionApiEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "EXECUTION_ID")
  private String executionId;

  @Column(name = "URL")
  private String url;

  @Column(name = "METHOD")
  @Enumerated(EnumType.STRING)
  private ExecutionApiMethodEnum method;

  @Column(name = "HTTP_VERSION")
  private HttpVersionEnum httpVersion;

  @Column(name = "ENABLE_SSL")
  private Boolean enableSsl;

  @Column(name = "BODY_TYPE")
  @Enumerated(EnumType.STRING)
  private ExecutionBodyTypeEnum bodyType;

  @Column(name = "CONTENT_TYPE")
  @Enumerated(EnumType.STRING)
  private ExecutionContentTypeEnum contentType;

  @Column(name = "AUTH_TYPE")
  @Enumerated(EnumType.STRING)
  private ExecutionApiAuthTypeEnum authType;

  @Column(name = "BODY_RAW")
  private String bodyRaw;

  @Column(name = "AUTH_TOKEN")
  private String authToken;

  @Column(name = "USERNAME")
  private String username;

  @Column(name = "PASSWORD")
  private String password;

  @Column(name = "HEADERS")
  @Convert(converter = ExecutionKeyValueListConverter.class)
  private List<ExecutionKeyValue> headers;

  @Column(name = "PARAMS")
  @Convert(converter = ExecutionKeyValueListConverter.class)
  private List<ExecutionKeyValue> params;

  @Column(name = "BODY_URLENCODED")
  @Convert(converter = ExecutionKeyValueListConverter.class)
  private List<ExecutionKeyValue> formUrlEncoded;

  @Override
  public String getId() {
    return id;
  }
}
