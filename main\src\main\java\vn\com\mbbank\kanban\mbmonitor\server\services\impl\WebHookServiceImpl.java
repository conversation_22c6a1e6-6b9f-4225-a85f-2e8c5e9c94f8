package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanUuidUtils;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.ModifyField;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WebHookEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WebHookConfigMapTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.log.WebhookLogModelMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.WebHookDto;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.WebHookEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.WebHookRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;
import vn.com.mbbank.kanban.mbmonitor.server.services.WebHookService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 8/19/2024
 */
@Service
@RequiredArgsConstructor
public class WebHookServiceImpl extends BaseServiceImpl<WebHookEntity, Long> implements
    WebHookService {

  private final WebHookRepository webHookRepository;
  private final ServiceService serviceService;
  private final ApplicationService applicationService;
  private final AlertPriorityConfigService alertPriorityConfigService;
  private final SysLogKafkaProducerService sysLogKafkaProducerService;
  private final WebhookLogModelMapper webhookLogModelMapper = WebhookLogModelMapper.INSTANCE;

  @Override
  protected JpaCommonRepository<WebHookEntity, Long> getRepository() {
    return webHookRepository;
  }

  @Override
  public WebHookEntity saveConfig(WebHookDto body) throws BusinessException {
    cleanRequest(body);
    // check name from database
    var isCreateMode = KanbanCommonUtil.isEmpty(body.getId());
    if (isCreateMode
        &&
        webHookRepository.existsWebHookEntitiesByNameIgnoreCase(body.getName())) {
      throw new BusinessException(ErrorCode.WEB_HOOK_CONFIG_NAME_EXIST, body.getName());
    }

    AlertPriorityConfigEntity priorityConfig = null;
    if (WebHookConfigMapTypeEnum.CUSTOM.equals(body.getPriorityType())) {
      priorityConfig =
          alertPriorityConfigService.findById(body.getAlertPriorityConfigId());

      if (Objects.isNull(priorityConfig) || priorityConfig.getDeleted()) {
        throw new BusinessException(ErrorCode.WEB_HOOK_CONFIG_PRIORITY_CONFIG_ID_IS_INVALID);
      }
    }

    ServiceEntity service = null;
    if (WebHookConfigMapTypeEnum.CUSTOM.equals(body.getServiceNameType())) {
      service = serviceService.findById(body.getServiceId());

      if (Objects.isNull(service) || service.getDeleted()) {
        throw new BusinessException(ErrorCode.WEB_HOOK_CONFIG_SERVICE_ID_IS_INVALID);
      }
    }

    ApplicationEntity application = null;
    if (WebHookConfigMapTypeEnum.CUSTOM.equals(body.getApplicationType())) {
      application =
          applicationService.findById(body.getApplicationId());

      if (Objects.isNull(application) || application.getDeleted()) {
        throw new BusinessException(ErrorCode.WEB_HOOK_CONFIG_APPLICATION_ID_IS_INVALID);
      }
    }

    WebHookEntity entity = WebHookEntityMapper.INSTANCE.map(body);
    entity.setToken(KanbanUuidUtils.generateUUID());
    var logName = body.getName();
    // case update
    if (!KanbanCommonUtil.isEmpty(entity.getId())) {
      WebHookEntity entityDb = findById(entity.getId());
      if (KanbanCommonUtil.isEmpty(entityDb)) {
        throw new BusinessException(ErrorCode.WEB_HOOK_CONFIG_NOT_EXIST);
      }
      logName = entityDb.getName();
      entity.setToken(entityDb.getToken());
      entity.setName(entityDb.getName());
    }
    var res = save(entity);
    sysLogKafkaProducerService.send(
        isCreateMode ? LogActionEnum.CREATE_WEBHOOK_COLLECT : LogActionEnum.EDIT_WEBHOOK_COLLECT, logName,
        webhookLogModelMapper.map(res, service, application, priorityConfig));
    return res;
  }

  @Override
  public void refreshToken(Long id) throws BusinessException {
    WebHookEntity entity = webHookRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.WEB_HOOK_CONFIG_NOT_EXIST));
    entity.setToken(KanbanUuidUtils.generateUUID());
    save(entity);
  }

  @Override
  public List<WebHookEntity> findAllByServiceId(String serviceId) {
    return webHookRepository.findAllByServiceId(serviceId);
  }

  @Override
  public List<WebHookEntity> findAllByApplicationId(String applicationId) {
    return webHookRepository.findAllByApplicationId(applicationId);
  }

  @Override
  public List<WebHookEntity> findAllByAlertPriorityConfigId(Long id) {
    return webHookRepository.findAllByAlertPriorityConfigId(id);
  }

  private void cleanRequest(@ModifyField WebHookDto data) {
    //service
    if (WebHookConfigMapTypeEnum.FROM_SOURCE.equals(data.getServiceNameType())) {
      data.setServiceId(null);
    } else {
      data.setServiceMapValue(null);
    }
    //application
    if (WebHookConfigMapTypeEnum.FROM_SOURCE.equals(data.getApplicationType())) {
      data.setApplicationId(null);
    } else {
      data.setApplicationMapValue(null);
    }

    //alert content
    if (WebHookConfigMapTypeEnum.FROM_SOURCE.equals(data.getAlertContentType())) {
      data.setAlertContentCustomValue(null);
    } else {
      data.setAlertContentMapValue(null);
    }

    //priority
    if (WebHookConfigMapTypeEnum.FROM_SOURCE.equals(data.getPriorityType())) {
      data.setAlertPriorityConfigId(null);
    } else {
      data.setPriorityMapValue(null);
    }
    //contact
    if (WebHookConfigMapTypeEnum.FROM_SOURCE.equals(data.getContactType())) {
      data.setContactCustomValue(null);
    } else {
      data.setContactMapValue(null);
    }
  }

  @Override
  public void deleteWithId(Long webhookId) throws BusinessException {
    var res = webHookRepository.findById(webhookId)
        .orElseThrow(() -> new BusinessException(ErrorCode.WEB_HOOK_CONFIG_NOT_EXIST));
    delete(res);
    sysLogKafkaProducerService.send(LogActionEnum.DELETE_WEBHOOK_COLLECT, res.getName());
  }

}
