package vn.com.mbbank.kanban.mbmonitor.common.converter;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowModel;

/**
 * WorkflowTemplateModelConverter.
 */
@Configurable
@Converter
public class WorkflowModelConverter implements AttributeConverter<WorkflowModel, String> {

  @Autowired
  private ObjectMapper objectMapper;

  @Override
  public String convertToDatabaseColumn(WorkflowModel attribute) {
    if (attribute == null) {
      return "";
    }
    try {
      return objectMapper.writeValueAsString(attribute);
    } catch (Exception e) {
      return "";
    }
  }

  @Override
  public WorkflowModel convertToEntityAttribute(String dbData) {
    if (KanbanCommonUtil.isEmpty(dbData)) {
      return null;
    }
    try {
      return objectMapper.readValue(dbData, WorkflowModel.class);
    } catch (Exception e) {
      e.printStackTrace();
      return null;
    }
  }
}
