package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.CustomInputRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CustomInputResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.CustomInputResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.CustomInputService;

/**
 * CustomInputController.
 */
@RestController
@RequestMapping(ServerUrl.CUSTOM_INPUT)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustomInputController extends BaseController {
  private final CustomInputService customInputService;
  private final CustomInputResponseMapper customInputResponseMapper =
      CustomInputResponseMapper.INSTANCE;

  /**
   * Api find config by ID.
   *
   * @param id id of config.
   * @return CustomInputResponse.
   */
  @GetMapping(value = "/{id}")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.IT_SERVICE_MANAGEMENT, action = PermissionActionEnum.VIEW)
  })
  ResponseData<CustomInputResponse> findById(@PathVariable String id)
      throws BusinessException {
    return ResponseUtils.success(customInputResponseMapper.map(customInputService.findWithId(id)));
  }

  /**
   * Api find a list of config.
   *
   * @param request search
   * @return Page of CustomInputResponse
   */
  @GetMapping(value = "/with-paging")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.IT_SERVICE_MANAGEMENT, action = PermissionActionEnum.VIEW)
  })
  ResponseData<Page<CustomInputResponse>> findAllWithPaging(@ModelAttribute PaginationRequestDTO request)
      throws BusinessException {
    request.setPropertiesSearch(List.of("name", "description"));
    request.setSortBy("createdDate");
    return ResponseUtils.success(customInputService.findAllWithPaging(request).map(customInputResponseMapper::map));
  }

  /**
   * Api find a list of config.
   *
   * @return Page of CustomInputResponse
   */
  @GetMapping
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.IT_SERVICE_MANAGEMENT, action = PermissionActionEnum.EDIT),
      @AclPermission(module = PermissionModuleEnum.IT_SERVICE_MANAGEMENT, action = PermissionActionEnum.CREATE)
  })
  ResponseData<List<CustomInputResponse>> findAll()
      throws BusinessException {
    return ResponseUtils.success(customInputResponseMapper.map(customInputService.findAll()));
  }

  /**
   * Api save config.
   *
   * @param request input data.
   * @return CustomInputResponse.
   * @throws BusinessException exception
   */
  @PostMapping
  ResponseData<CustomInputResponse> save(
      @Valid @RequestBody CustomInputRequest request) throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.IT_SERVICE_MANAGEMENT,
        PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(request.getId()));
    return ResponseUtils.success(customInputResponseMapper.map(customInputService.createOrUpdate(request)));
  }

  /**
   * Api delete config.
   *
   * @param id config id.
   * @return String
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.IT_SERVICE_MANAGEMENT, action = PermissionActionEnum.DELETE)
  })
  @DeleteMapping(value = "/{id}")
  ResponseData<String> deleteById(@PathVariable String id) throws BusinessException {
    customInputService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }
}
