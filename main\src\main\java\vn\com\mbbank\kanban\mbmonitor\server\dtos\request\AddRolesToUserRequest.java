package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.NotNull;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AlertGroupConfigRequest.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AddRolesToUserRequest {
  @NotNull
  private Long userId;
  @NotNull
  private Set<Long> roleIds;
}
