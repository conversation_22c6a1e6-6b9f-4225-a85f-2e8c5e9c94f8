package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.kafka.KafkaException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.KafkaJobModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CollectEmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CollectEmailConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaJobTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.log.CollectEmailConfigLogModelMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.JobKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleConverterUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupConverter;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.CollectEmailConfigModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.CollectEmailConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CollectEmailConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.enums.CollectEmailAlertContentTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.CollectEmailConfigEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.CollectEmailConfigResponseCollectEmailConfigEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.CollectEmailConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.EmailConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.CollectEmailConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ConditionMapService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CollectEmailConfigServiceImpl extends BaseServiceImpl<CollectEmailConfigEntity, Long>
    implements CollectEmailConfigService {
  CollectEmailConfigRepository collectEmailConfigRepository;
  EmailConfigRepository emailConfigRepository;
  JobKafkaProducerService jobKafkaProducerService;
  SysLogKafkaProducerService sysLogKafkaProducerService;
  ServiceService serviceService;
  ApplicationService applicationService;
  AlertPriorityConfigService alertPriorityConfigService;
  ConditionMapService conditionMapService;
  CollectEmailConfigLogModelMapper collectEmailConfigLogModelMapper = CollectEmailConfigLogModelMapper.INSTANCE;

  @Override
  public JpaCommonRepository<CollectEmailConfigEntity, Long> getRepository() {
    return collectEmailConfigRepository;
  }

  @Override
  public List<CollectEmailConfigEntity> findAllByServiceId(String id) {
    return collectEmailConfigRepository.findAllByServiceId(id);
  }

  @Override
  public List<CollectEmailConfigEntity> findAllByApplicationId(String id) {
    return collectEmailConfigRepository.findAllByApplicationId(id);
  }

  @Override
  public List<CollectEmailConfigEntity> findAllByPriorityConfigId(Long id) {
    return collectEmailConfigRepository.findAllByPriorityConfigId(id);
  }

  @Override
  @Transactional(rollbackFor = BusinessException.class)
  public CollectEmailConfigResponse createOrUpdate(
      CollectEmailConfigRequest collectEmailConfigRequest)
      throws BusinessException {
    var formatName = StringUtils.capitalizeFirstLetter(collectEmailConfigRequest.getName());
    var isUpdateMode = Objects.nonNull(collectEmailConfigRequest.getId()) && collectEmailConfigRequest.getId() > 0L;
    var emailConfig = emailConfigRepository.findById(collectEmailConfigRequest.getEmailConfigId())
        .orElseThrow(() -> new BusinessException(ErrorCode.EMAIL_CONFIG_NOT_FOUND));
    validateSaveRequest(collectEmailConfigRequest, emailConfig.isActive());
    emailConfig.setIntervalTime(collectEmailConfigRequest.getIntervalTime());
    emailConfigRepository.save(emailConfig);
    var collectEmailConfigEntity =
        CollectEmailConfigEntityMapper.INSTANCE.map(collectEmailConfigRequest);
    collectEmailConfigEntity.setName(formatName);
    if (CollectEmailConfigTypeEnum.ABSENCE_ALERT.equals(collectEmailConfigEntity.getType())) {
      long halfIntervalMillis = (collectEmailConfigRequest.getIntervalTime() * 1000L) / 2;
      Date adjustedDate = new Date(System.currentTimeMillis() - halfIntervalMillis);
      collectEmailConfigEntity.setLastReceivedEmailDate(adjustedDate);
    }
    var logName =
        isUpdateMode ? collectEmailConfigRepository.getReferenceById(collectEmailConfigRequest.getId()).getName() :
            collectEmailConfigRequest.getName();
    var collectEmailConfig = collectEmailConfigRepository.save(collectEmailConfigEntity);
    try {
      jobKafkaProducerService.notifyJobUpdate(KafkaTypeEnum.EMAIL_COLLECT,
          new KafkaJobModel<Long>().configId(emailConfig.getId()).type(
              KafkaJobTypeEnum.NEW_OR_UPDATE));
    } catch (KafkaException e) {
      throw new BusinessException(ErrorCode.COLLECT_EMAIL_CONFIG_NOTIFY_KAFKA_ERROR);
    }
    var res = CollectEmailConfigResponseCollectEmailConfigEntityMapper.INSTANCE.map(
        collectEmailConfig);
    sysLogKafkaProducerService.send(
        isUpdateMode ? LogActionEnum.EDIT_EMAIL_COLLECT : LogActionEnum.CREATE_EMAIL_COLLECT, logName,
        collectEmailConfigLogModelMapper.map(collectEmailConfig, emailConfig,
                serviceService.findById(collectEmailConfigRequest.getServiceId()),
                applicationService.findById(collectEmailConfigRequest.getApplicationId()),
                alertPriorityConfigService.findById(collectEmailConfigRequest.getPriorityConfigId()),
                conditionMapService.mapNameCondition(collectEmailConfigRequest.getRuleGroup(),
                        CollectEmailConfigModel.class, false, null)));
    return res;
  }

  public void validateSaveRequest(CollectEmailConfigRequest collectEmailConfigRequest,
                                  boolean active)
      throws BusinessException {

    Long id = collectEmailConfigRequest.getId();
    String name = StringUtils.capitalizeFirstLetter(collectEmailConfigRequest.getName());
    var isUpdate = !KanbanCommonUtil.isEmpty(id) && id > 0L;
    if (isUpdate) {
      var oldConfig = collectEmailConfigRepository.findById(id)
          .orElseThrow(() -> new BusinessException(ErrorCode.COLLECT_EMAIL_CONFIG_NOT_FOUND));
      if (!active && !Objects.equals(oldConfig.getEmailConfigId(),
          collectEmailConfigRequest.getEmailConfigId())) {
        throw new BusinessException(ErrorCode.EMAIL_CONFIG_INACTIVE);
      }
    }
    boolean isNameExists = isUpdate ? existByIdNotAndName(id, name) : existByName(name);
    if (isNameExists) {
      throw new BusinessException(ErrorCode.COLLECT_EMAIL_CONFIG_NAME_EXIST);
    }
    var service = serviceService.findById(collectEmailConfigRequest.getServiceId());
    if (Objects.isNull(service)) {
      throw new BusinessException(ErrorCode.EMAIL_CONFIG_INVALID_SERVICE);
    }
    var application = applicationService.findById(collectEmailConfigRequest.getApplicationId());
    if (Objects.isNull(application)) {
      throw new BusinessException(ErrorCode.EMAIL_CONFIG_INVALID_APPLICATION);
    }
    var priority = alertPriorityConfigService.findById(collectEmailConfigRequest.getPriorityConfigId());
    if (Objects.isNull(priority)) {
      throw new BusinessException(ErrorCode.EMAIL_CONFIG_INVALID_PRIORITY);
    }
    if (CollectEmailConfigTypeEnum.ABSENCE_ALERT.equals(collectEmailConfigRequest.getType())) {
      if (Objects.isNull(collectEmailConfigRequest.getAbsenceInterval())
          || Objects.isNull(collectEmailConfigRequest.getAlertRepeatInterval())) {
        throw new BusinessException(ErrorCode.EMAIL_CONFIG_INVALID_ABSENCE_INTERVAL);
      }
      if (Objects.isNull(collectEmailConfigRequest.getContentValue())) {
        throw new BusinessException(ErrorCode.EMAIL_CONFIG_INVALID_CONTENT_VALUE);
      }
    } else {
      if (Objects.isNull(collectEmailConfigRequest.getContentType())) {
        throw new BusinessException(ErrorCode.EMAIL_CONFIG_INVALID_CONTENT_TYPE);
      }
      if (CollectEmailAlertContentTypeEnum.CUSTOM_CONTENT.equals(collectEmailConfigRequest.getContentType())
          && Objects.isNull(collectEmailConfigRequest.getContentValue())) {
        throw new BusinessException(ErrorCode.EMAIL_CONFIG_INVALID_CONTENT_VALUE);
      }
    }

  }

  @Override
  public boolean existByName(String name) {
    return collectEmailConfigRepository.existsByNameIgnoreCase(name);
  }

  @Override
  public boolean existByIdNotAndName(Long id, String name) {
    return collectEmailConfigRepository.existsByIdNotAndNameIgnoreCase(id, name);
  }

  @Override
  public CollectEmailConfigModel findCollectEmailConfigById(Long id) throws Exception {
    var collectEmailConfigModel = collectEmailConfigRepository.findCollectEmailConfigById(id);
    collectEmailConfigModel.setRuleGroup(
        RuleConverterUtils.convertStringToRuleGroupType(
            collectEmailConfigModel.getRuleGroupColumn()));
    return collectEmailConfigModel;
  }

  @Override
  public Page<CollectEmailConfigModel> findAll(PaginationRequestDTO paginationRequest) {
    var collectEmailConfigModels = collectEmailConfigRepository.findAll(paginationRequest);
    collectEmailConfigModels.getContent().forEach(model -> model.setRuleGroup(RuleGroupConverter
        .convertToRuleGroup(model.getRuleGroupColumn())));
    return collectEmailConfigModels;
  }

  @Override
  @Transactional
  public CollectEmailConfigResponse updateStatus(Long id) throws BusinessException {
    var collectEmailConfigEntity = collectEmailConfigRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.COLLECT_EMAIL_CONFIG_NOT_FOUND));
    collectEmailConfigEntity.setActive(!collectEmailConfigEntity.isActive());
    if (!collectEmailConfigEntity.isActive()) {
      boolean existed =
          collectEmailConfigRepository.existsByIdNotAndEmailConfigIdAndActiveIsTrue(id,
              collectEmailConfigEntity.getEmailConfigId());
      if (!existed) {
        var emailConfig =
            emailConfigRepository.findById(collectEmailConfigEntity.getEmailConfigId())
                .orElseThrow(() -> new BusinessException(ErrorCode.EMAIL_CONFIG_NOT_FOUND));
        emailConfig.setExecuted(false);
        emailConfigRepository.save(emailConfig);
      }
    }
    if (CollectEmailConfigTypeEnum.ABSENCE_ALERT.equals(collectEmailConfigEntity.getType())) {
      long halfIntervalMillis = (collectEmailConfigEntity.getIntervalTime() * 1000L) / 2;
      Date adjustedDate = new Date(System.currentTimeMillis() - halfIntervalMillis);
      collectEmailConfigEntity.setLastReceivedEmailDate(adjustedDate);
    }
    var collectEmailConfig = collectEmailConfigRepository.save(collectEmailConfigEntity);
    var res = CollectEmailConfigResponseCollectEmailConfigEntityMapper.INSTANCE.map(
        collectEmailConfig);
    sysLogKafkaProducerService.send(
        collectEmailConfigEntity.isActive() ? LogActionEnum.ACTIVE_EMAIL_COLLECT : LogActionEnum.INACTIVE_EMAIL_COLLECT,
        res.getName());
    return res;
  }

  @Override
  public List<CollectEmailConfigResponse> findAllByEmailConfigId(Long emailConfigId) {
    return CollectEmailConfigResponseCollectEmailConfigEntityMapper.INSTANCE.map(
        collectEmailConfigRepository.findAllByEmailConfigId(emailConfigId));
  }

  @Override
  public List<String> findAllByCustomObjectId(Long customObjectId) {
    Set<String> alertGroupConfigNamesSet = new HashSet<>();
    var configs = collectEmailConfigRepository.findAll();
    for (CollectEmailConfigEntity config : configs) {
      if (config.getRuleGroup().checkCustomObject(customObjectId)) {
        alertGroupConfigNamesSet.add(config.getName());
      }
      if (!KanbanCommonUtil.isEmpty(config.getContentValue())
          && checkCustomObjectInCustomContent(customObjectId, config.getContentValue())) {
        alertGroupConfigNamesSet.add(config.getName());
      }
    }
    return new ArrayList<>(alertGroupConfigNamesSet);
  }

  @Override
  public void deleteWithId(Long emailConfigId) throws BusinessException {
    var config = collectEmailConfigRepository.findById(emailConfigId)
        .orElseThrow(() -> new BusinessException(ErrorCode.COLLECT_EMAIL_CONFIG_NOT_FOUND));
    delete(config);
    sysLogKafkaProducerService.send(LogActionEnum.DELETE_EMAIL_COLLECT, config.getName());
  }

  public boolean checkCustomObjectInCustomContent(Long customObjectId, String content) {
    Pattern pattern = Pattern.compile("@(" + customObjectId + "+\\d*)");
    Matcher matcher = pattern.matcher(content);
    if (!matcher.find()) {
      return false;
    }
    matcher.reset();
    while (matcher.find()) {
      String customObjectIdStr = matcher.group(1);
      if (customObjectIdStr.equals(customObjectId.toString())) {
        return true;
      }
    }
    return false;
  }

}
