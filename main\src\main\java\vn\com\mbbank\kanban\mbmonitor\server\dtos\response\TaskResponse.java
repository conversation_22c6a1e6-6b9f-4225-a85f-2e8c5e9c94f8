package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import java.util.Date;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskShiftEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskTimeTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskTypeEnum;

/**
 * Model view task response.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TaskResponse {
  Long id;
  String name;
  String description;
  TaskStatusEnum status;
  Date startTime;
  Date endTime;
  TaskTypeEnum type;
  TaskShiftEnum shift;
  TaskTimeTypeEnum timeType;
  String currentAssigneeUserName;
  String createdBy;
  String createdDate;
  boolean deleted;
  List<TaskResponse> childrenTasks;
  List<TaskUserResponse> handoverUsers;
}