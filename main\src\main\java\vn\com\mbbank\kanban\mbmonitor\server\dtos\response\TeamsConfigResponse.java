package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsConfigEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/23/2025
 */
@Data
public class TeamsConfigResponse extends TeamsConfigEntity {
  private String clientSecretPlaceholder;
  @JsonProperty("username")
  private String email;

  @JsonIgnore
  private String clientSecret;

  @JsonIgnore
  private String password;
}
