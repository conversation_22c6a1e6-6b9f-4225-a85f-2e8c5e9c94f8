package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;

/**
 * DTO for handling filter alert configuration requests.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FilterAlertConfigRequest {
  Long id;

  @NotBlank
  @Size(min = 1, message = "Filter alert config name can not be empty")
  @Size(
      max = CommonConstants.COMMON_NAME_MAX_LENGTH,
      message = "Filter alert config name has max {max} character"
  )
  String name;

  @Size(
      max = CommonConstants.COMMON_DESCRIPTION_MAX_LENGTH,
      message = "Filter alert config name has max {max} character"
  )
  String description;

  @NotNull
  RuleGroupType ruleGroup;


}
