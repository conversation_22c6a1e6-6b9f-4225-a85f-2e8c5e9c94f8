package vn.com.mbbank.kanban.mbmonitor.server.services;

import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.CustomInputRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomInputEntity;

/**
 * interface logic CustomInputService.
 */
public interface CustomInputService extends BaseService<CustomInputEntity, String> {

  /**
   * Find all.
   *
   * @param id id
   * @return page CustomInputEntity
   */
  CustomInputEntity findWithId(String id) throws BusinessException;

  /**
   * create or update.
   *
   * @param request CustomInputRequest
   * @return CustomInputEntity
   */
  CustomInputEntity createOrUpdate(CustomInputRequest request) throws BusinessException;

  /**
   * delete by id.
   *
   * @param id for delete
   */
  void deleteWithId(String id) throws BusinessException;

}