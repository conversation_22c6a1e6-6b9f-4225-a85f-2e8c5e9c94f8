package vn.com.mbbank.kanban.mbmonitor.server.configs;

import java.util.concurrent.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * Configuration class for creating ThreadPool config bean.
 */

@Configuration
@EnableAsync
public class ThreadPoolConfig {

  @Value("${thread-pool.pool-size.core}")
  private Integer corePoolSize;

  @Value("${thread-pool.pool-size.max}")
  private Integer maxPoolSize;

  @Value("${thread-pool.queue-capacity}")
  private Integer queueCapacity;

  @Value("${thread-pool.thread-name-prefix}")
  private String threadNamePrefix;

  @Value("${thread-pool.await-termination-seconds}")
  private int awaitTerminationSeconds;

  /**
   * Creates a taskExecutor bean.
   *
   * @return an Executor object
   */
  @Bean(name = "lightWeighTaskExecutor")
  public TaskExecutor lightWeighTaskExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setDaemon(true);
    executor.setCorePoolSize(corePoolSize);
    executor.setMaxPoolSize(maxPoolSize);
    executor.setQueueCapacity(queueCapacity);
    executor.setAwaitTerminationSeconds(awaitTerminationSeconds);
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    executor.setThreadNamePrefix(threadNamePrefix);
    executor.initialize();
    return executor;
  }

}
