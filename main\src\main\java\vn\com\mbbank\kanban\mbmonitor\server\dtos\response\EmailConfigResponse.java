package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.enums.EmailProtocolSecurityTypeEnum;

/**
 * Model response service to create or update email config.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailConfigResponse {
  Long id;
  String host;
  String description;
  Long port;
  String email;
  String username;
  EmailProtocolSecurityTypeEnum securityType;
  EmailProtocolTypeEnum protocolType;
  boolean active;
}
