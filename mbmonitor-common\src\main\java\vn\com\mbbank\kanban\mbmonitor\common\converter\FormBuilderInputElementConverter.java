package vn.com.mbbank.kanban.mbmonitor.common.converter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.BaseFormBuilderInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.CheckboxElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.DateTimeElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.NumberInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.RadioElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.SelectElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.TableInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.TextInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.TextareaInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.FormBuilderElementTypeEnum;

/**
 * FormBuilderElementInputConverter.
 */
@Converter
public class FormBuilderInputElementConverter implements AttributeConverter<BaseFormBuilderInputElementModel, String> {

  @Override
  public String convertToDatabaseColumn(BaseFormBuilderInputElementModel attribute) {
    if (attribute == null) {
      return null;
    }
    return JSON.toJSONString(attribute);
  }

  @Override
  public BaseFormBuilderInputElementModel convertToEntityAttribute(String dbData) {
    if (KanbanCommonUtil.isEmpty(dbData)) {
      return null;
    }
    JSONObject obj = JSON.parseObject(dbData);
    FormBuilderElementTypeEnum type = obj.getObject("type", FormBuilderElementTypeEnum.class);
    return switch (type) {
      case TEXT -> obj.toJavaObject(TextInputElementModel.class);
      case TEXTAREA -> obj.toJavaObject(TextareaInputElementModel.class);
      case NUMBER -> obj.toJavaObject(NumberInputElementModel.class);
      case CHECKBOX -> obj.toJavaObject(CheckboxElementModel.class);
      case RADIO -> obj.toJavaObject(RadioElementModel.class);
      case SELECT -> obj.toJavaObject(SelectElementModel.class);
      case DATETIME -> obj.toJavaObject(DateTimeElementModel.class);
      case TABLE_INPUT -> obj.toJavaObject(TableInputElementModel.class);
      default -> throw new IllegalArgumentException("Unknown type: " + type);
    };
  }
}
