package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowStatusEnum;

/**
 * BaseFormBuilderElement.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WorkflowModel {
  private String id;
  private Date startTime;
  private Date endTime;
  private WorkflowStatusEnum status;
  List<WorkflowNodeModel> nodes = new ArrayList<>();
  List<WorkflowEdgeModel> edges = new ArrayList<>();
}
