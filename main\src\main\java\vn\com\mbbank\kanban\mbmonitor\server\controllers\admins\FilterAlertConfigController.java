package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import jakarta.validation.Valid;
import java.util.Collections;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.FilterAlertConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.FilterAlertPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.FilterAlertConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.FilterAlertConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.FilterAlertConfigService;

/**
 * Controller logic FilterAlertConfigController.
 */
@RestController
@RequestMapping(ServerUrl.FILTER_ALERT_CONFIG_URL)
@RequiredArgsConstructor
public class FilterAlertConfigController extends BaseController {
  private final FilterAlertConfigService filterAlertConfigService;
  private final FilterAlertConfigResponseMapper filterAlertConfigResponseMapper =
      FilterAlertConfigResponseMapper.INSTANCE;

  /**
   * Api find config by ID.
   *
   * @param id id of config.
   * @return FilterAlertConfigResponse.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.FILTER_ALERT_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping(value = "/{id}")
  ResponseData<FilterAlertConfigResponse> findById(@PathVariable Long id)
      throws BusinessException {
    return ResponseUtils.success(filterAlertConfigService.findByIdWithDetail(id));
  }

  /**
   * Api find a list of config.
   *
   * @param request search
   * @return List of FilterAlertConfigResponse
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.FILTER_ALERT_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  ResponseData<Page<FilterAlertConfigResponse>> findAll(@ModelAttribute FilterAlertPaginationRequest request) {
    return ResponseUtils.success(filterAlertConfigService.findAllWithSearch(request));
  }

  /**
   * Api save config.
   *
   * @param filterAlertConfigRequest input data.
   * @return FilterAlertConfigResponse.
   * @throws BusinessException exception
   */
  @PostMapping
  ResponseData<FilterAlertConfigResponse> save(
      @Valid @RequestBody FilterAlertConfigRequest filterAlertConfigRequest) throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.FILTER_ALERT_CONFIG, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(filterAlertConfigRequest.getId()));
    return ResponseUtils.success(
        filterAlertConfigResponseMapper.map(filterAlertConfigService.save(filterAlertConfigRequest)));
  }

  /**
   * Api delete config.
   *
   * @param id config id.
   * @return String
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.FILTER_ALERT_CONFIG, action = PermissionActionEnum.DELETE)
  })
  @DeleteMapping(value = "/{id}")
  ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    filterAlertConfigService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }


  /**
   * Api delete config.
   *
   * @param id     config id.
   * @param active active status
   * @return list of config
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.FILTER_ALERT_CONFIG, action = PermissionActionEnum.EDIT),
  })
  @PutMapping("/{id}")
  public ResponseData<FilterAlertConfigResponse> updateActive(@PathVariable Long id,
                                                              @RequestParam(value = "active") Boolean active)
      throws BusinessException {
    return ResponseUtils.success(
        filterAlertConfigResponseMapper.map(filterAlertConfigService.updateActive(id, active)));
  }
}
