package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.util.Arrays;
import java.util.EnumSet;
import java.util.List;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.ModifyField;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.KafkaJobModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorActionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorWebConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ActionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaJobTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MonitorTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.JobKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CronUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;
import vn.com.mbbank.kanban.mbmonitor.common.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.ActionModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.MonitorWebConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.MonitorWebConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.MonitorActionEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.MonitorWebConfigEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.MonitorWebConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.MonitorActionRepository;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.MonitorWebConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.MonitorWebConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class MonitorWebConfigServiceImpl extends BaseServiceImpl<MonitorWebConfigEntity, String>
    implements MonitorWebConfigService {
  
  private static final EnumSet<ActionTypeEnum> IDENTIFIER_SHOULD_DISABLE = EnumSet.of(ActionTypeEnum.WAIT,
      ActionTypeEnum.CLOSE_POPUP, ActionTypeEnum.BACK_TO_MAIN, ActionTypeEnum.SWITCH_TO_POPUP, ActionTypeEnum.GO_TO_URL,
      ActionTypeEnum.ALERT_ACCEPT, ActionTypeEnum.ALERT_DISMISS,  ActionTypeEnum.SWITCH_TO_DEFAULT_FRAME
  );
  
  private static final EnumSet<ActionTypeEnum> VALUE_SHOULD_REQUIRE = EnumSet.of(
      ActionTypeEnum.SEND_KEY, ActionTypeEnum.WAIT, ActionTypeEnum.WAITING_FOR_ELEMENT,
      ActionTypeEnum.SELECT_FROM_DROPDOWN, ActionTypeEnum.GO_TO_URL
  );
  
  private static final EnumSet<ActionTypeEnum> NUMERIC_VALUE_REQUIRED = EnumSet.of(
      ActionTypeEnum.WAIT, ActionTypeEnum.WAITING_FOR_ELEMENT
  );
  
  MonitorWebConfigRepository monitorWebConfigRepository;
  MonitorActionRepository monitorActionRepository;
  JobKafkaProducerService jobKafkaProducerService;
  SysLogKafkaProducerService sysLogKafkaProducerService;
  ServiceService serviceService;
  ApplicationService applicationService;
  AlertPriorityConfigService alertPriorityConfigService;
  
  @Override
  protected JpaCommonRepository<MonitorWebConfigEntity, String> getRepository() {
    return monitorWebConfigRepository;
  }
  
  @Override
  public Page<MonitorWebConfigResponse> findAll(PaginationRequestDTO paginationRequest) {
    return monitorWebConfigRepository.findAll(paginationRequest);
  }
  
  @Override
  @Transactional(rollbackFor = BusinessException.class)
  public MonitorWebConfigResponse createOrUpdate(MonitorWebConfigRequest request) throws BusinessException {
    var isUpdateMode = !KanbanCommonUtil.isEmpty(request.getId());
    var isLoginMode = MonitorTypeEnum.LOGIN.equals(request.getMonitorType());
    
    validateSaveRequest(request);
    MonitorWebConfigEntity monitorWebConfig = MonitorWebConfigEntityMapper.INSTANCE.map(request);
    List<MonitorActionEntity> monitorActions = MonitorActionEntityMapper.INSTANCE.map(request.getActions());
    if (isUpdateMode) {
      // Remove actions and auth actions when update
      MonitorWebConfigEntity monitorWebConfigEntity = getMonitorWebConfigOrThrow(request.getId());
      monitorActionRepository.deleteAllByActionId(monitorWebConfigEntity.getActionId());
      monitorActionRepository.deleteAllByActionId(monitorWebConfigEntity.getAuthActionId());
    }
    
    // Setting authActionId and save all auth action
    if (isLoginMode) {
      List<MonitorActionEntity> authActions = MonitorActionEntityMapper.INSTANCE.map(request.getAuthActions());
      String authActionId = GeneratorUtil.generateId();
      normalizeActions(authActions, authActionId);
      monitorActionRepository.saveAll(authActions);
      monitorWebConfig.setAuthActionId(authActionId);
    } else {
      monitorWebConfig.setAuthActionId(null);
    }
    // Setting actionId and save all monitor action
    String actionId = GeneratorUtil.generateId();
    normalizeActions(monitorActions, actionId);
    monitorActionRepository.saveAll(monitorActions);
    monitorWebConfig.setActionId(actionId);
    monitorWebConfig.setName(StringUtils.capitalizeFirstLetter(request.getName()));
    
    MonitorWebConfigEntity monitorWebConfigEntity = monitorWebConfigRepository.save(monitorWebConfig);
    String cronTime = CronUtils.makeCronTimeByMonitorWebConfigEntity(monitorWebConfigEntity);

    jobKafkaProducerService.notifyJobUpdate(KafkaTypeEnum.MONITOR_WEB_CONFIG,
      new KafkaJobModel<String>().configId(monitorWebConfig.getId()).type(
        monitorWebConfig.getActive() ? KafkaJobTypeEnum.NEW_OR_UPDATE :
          KafkaJobTypeEnum.DELETE).cronTime(cronTime));
    
    MonitorWebConfigResponse res = MonitorWebConfigResponseMapper.INSTANCE.map(monitorWebConfigEntity);
    sysLogKafkaProducerService.send(isUpdateMode
        ? LogActionEnum.EDIT_MONITOR_WEB_CONFIG : LogActionEnum.CREATE_MONITOR_WEB_CONFIG, res.getName(), res);
    
    return res;
  }
  
  @Override
  public MonitorWebConfigResponse findWithId(String id) throws BusinessException {
    MonitorWebConfigEntity monitorWebConfig = getMonitorWebConfigOrThrow(id);
    
    MonitorWebConfigResponse res = MonitorWebConfigResponseMapper.INSTANCE.map(monitorWebConfig);
    // Find all actions by actionId
    List<MonitorActionEntity> monitorActions =
        monitorActionRepository.findByActionIdOrderByOrdersAsc(monitorWebConfig.getActionId());
    if (monitorActions.isEmpty()) {
      throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_FOUND);
    }
    res.setActions(MonitorActionEntityMapper.INSTANCE.mapTo(monitorActions));
    var appNameAndServiceName =  monitorWebConfigRepository.findAppNameAndServiceNameWithId(monitorWebConfig.getId());
    res.setApplicationName(appNameAndServiceName.getApplicationName());
    res.setServiceName(appNameAndServiceName.getServiceName());
    
    // Find all auth actions if a monitor type is login
    if (MonitorTypeEnum.LOGIN.equals(monitorWebConfig.getMonitorType())) {
      List<MonitorActionEntity> authActions =
          monitorActionRepository.findByActionIdOrderByOrdersAsc(monitorWebConfig.getAuthActionId());
      if (authActions.isEmpty()) {
        throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_AUTH_ACTION_NOT_FOUND);
      }
      res.setAuthActions(MonitorActionEntityMapper.INSTANCE.mapTo(authActions));
    }

    return res;
  }
  
  @Override
  @Transactional(rollbackFor = BusinessException.class)
  public void deleteWithId(String monitorWebConfigId) throws BusinessException {
    MonitorWebConfigEntity monitorWebConfig = getMonitorWebConfigOrThrow(monitorWebConfigId);
    
    jobKafkaProducerService.notifyJobUpdate(KafkaTypeEnum.MONITOR_WEB_CONFIG,
      new KafkaJobModel<String>().type(KafkaJobTypeEnum.DELETE).configId(monitorWebConfigId));
    
    monitorActionRepository.deleteAllByActionId(monitorWebConfig.getActionId());
    monitorActionRepository.deleteAllByActionId(monitorWebConfig.getAuthActionId());
    delete(monitorWebConfig);

    sysLogKafkaProducerService.send(LogActionEnum.DELETE_MONITOR_WEB_CONFIG, monitorWebConfig.getName());
  
  }
  
  @Override
  @Transactional
  public MonitorWebConfigResponse updateStatus(String id) throws BusinessException {
    MonitorWebConfigEntity monitorWebConfig = getMonitorWebConfigOrThrow(id);
    monitorWebConfig.setActive(!monitorWebConfig.getActive());
    
    MonitorWebConfigEntity monitorWebConfigEntity = monitorWebConfigRepository.save(monitorWebConfig);
    MonitorWebConfigResponse res = MonitorWebConfigResponseMapper.INSTANCE.map(monitorWebConfigEntity);
    
    String cronTime = CronUtils.makeCronTimeByMonitorWebConfigEntity(monitorWebConfigEntity);
    
    jobKafkaProducerService.notifyJobUpdate(KafkaTypeEnum.MONITOR_WEB_CONFIG,
      new KafkaJobModel<String>().cronTime(cronTime).type(
        monitorWebConfig.getActive() ? KafkaJobTypeEnum.NEW_OR_UPDATE : KafkaJobTypeEnum.DELETE).configId(id));
    
    sysLogKafkaProducerService.send(
        monitorWebConfig.getActive() ? LogActionEnum.ACTIVE_MONITOR_WEB_CONFIG :
          LogActionEnum.INACTIVE_MONITOR_WEB_CONFIG, res.getName());
    
    return res;
  }
  
  @Override
  public boolean existByName(String name) {
    return monitorWebConfigRepository.existsByNameIgnoreCase(name);
  }
  
  @Override
  public boolean existByIdNotAndName(String id, String name) {
    return monitorWebConfigRepository.existsByIdNotAndNameIgnoreCase(id, name);
  }
  
  @Override
  public List<MonitorWebConfigEntity> findAllByServiceId(String id) {
    return monitorWebConfigRepository.findAllByServiceId(id);
  }
  
  @Override
  public List<MonitorWebConfigEntity> findAllByApplicationId(String id) {
    return monitorWebConfigRepository.findAllByApplicationId(id);
  }
  
  @Override
  public List<MonitorWebConfigEntity> findAllByPriorityId(Long id) {
    return monitorWebConfigRepository.findAllByPriorityId(id);
  }
  
  private void validateSaveRequest(MonitorWebConfigRequest request) throws BusinessException {
    String id = request.getId();
    var isCreate = KanbanCommonUtil.isEmpty(id);
    String name = StringUtils.capitalizeFirstLetter(request.getName());
    boolean isNameExists = isCreate ? existByName(name) : existByIdNotAndName(id, name);
    if (isNameExists) {
      throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_NAME_IS_EXISTED);
    }
    validateInterval(request.getMonths(), CommonConstants.INTERVAL_MONTH_MIN_VALUE,
        CommonConstants.INTERVAL_MONTH_MAX_VALUE, CommonConstants.INTERVAL_MONTH);
    validateInterval(request.getDayOfMonths(), CommonConstants.INTERVAL_DAY_OF_MONTH_MIN_VALUE,
        CommonConstants.INTERVAL_DAY_OF_MONTH_MAX_VALUE, CommonConstants.INTERVAL_DAY_OF_MONTH);
    validateInterval(request.getDayOfWeeks(), CommonConstants.INTERVAL_DAY_OF_WEEK_MIN_VALUE,
        CommonConstants.INTERVAL_DAY_OF_WEEK_MAX_VALUE, CommonConstants.INTERVAL_DAY_OF_WEEK);
    validateInterval(request.getHours(), CommonConstants.INTERVAL_HOUR_MIN_VALUE,
        CommonConstants.INTERVAL_HOUR_MAX_VALUE, CommonConstants.INTERVAL_HOUR);
    validateInterval(request.getMinutes(), CommonConstants.INTERVAL_MINUTE_MIN_VALUE,
        CommonConstants.INTERVAL_MINUTE_MAX_VALUE, CommonConstants.INTERVAL_MINUTE);
    
    if (KanbanCommonUtil.isEmpty(request.getActions())) {
      throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_ACTION_INVALID);
    }
    var service = serviceService.findById(request.getServiceId());
    if (Objects.isNull(service)) {
      throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_SERVICE_INVALID);
    }
    var application = applicationService.findById(request.getApplicationId());
    if (Objects.isNull(application)) {
      throw new BusinessException(ErrorCode. MONITOR_WEB_CONFIG_APPLICATION_INVALID);
    }
    var priority = alertPriorityConfigService.findById(request.getPriorityId());
    if (Objects.isNull(priority)) {
      throw new BusinessException(ErrorCode. MONITOR_WEB_CONFIG_PRIORITY_INVALID);
    }
    authActionsValidation(request.getAuthActions(), request.getMonitorType());
    validateActions(request.getActions());
  }
  
  private void authActionsValidation(List<ActionModel> authActions, MonitorTypeEnum monitorType)
      throws BusinessException {
    if (MonitorTypeEnum.LOGIN.equals(monitorType)) {
      if (authActions == null || authActions.size() != 3) {
        throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_AUTH_ACTION_INVALID);
      }
      ActionModel username = authActions.get(0);
      ActionModel password = authActions.get(1);
      ActionModel loginButton = authActions.get(2);
      
      if (!ActionTypeEnum.SEND_KEY.equals(username.getActionType()) || KanbanCommonUtil.isEmpty(username.getValue())
          || KanbanCommonUtil.isEmpty(username.getIdentifier())) {
        throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_USERNAME_INVALID);
      }
      if (!ActionTypeEnum.SEND_KEY.equals(password.getActionType()) || KanbanCommonUtil.isEmpty(password.getValue())
           || KanbanCommonUtil.isEmpty(password.getIdentifier())) {
        throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_PASSWORD_INVALID);
      }
      if (!ActionTypeEnum.CLICK.equals(loginButton.getActionType())
          || KanbanCommonUtil.isEmpty(loginButton.getIdentifier())) {
        throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_LOGIN_BUTTON_INVALID);
      }
    }
  }
  
  private void validateInterval(Integer[] values, int min, int max, String constants) throws BusinessException {
    boolean isNotValid = !KanbanCommonUtil.isEmpty(values)
        && !Arrays.stream(values).allMatch(v -> v != null && v >= min && v <= max);
    if (isNotValid) {
      throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_INTERVAL_INVALID, constants);
    }
    
  }
  
  public void normalizeActions(@ModifyField List<MonitorActionEntity> actions, String actionId) {
    // Setting actionId and set order 1->n
    for (int i = 0; i < actions.size(); i++) {
      MonitorActionEntity action = actions.get(i);
      action.setActionId(actionId);
      action.setOrders(i + 1);
    }
  }
  
  private MonitorWebConfigEntity getMonitorWebConfigOrThrow(String id) throws BusinessException {
    return monitorWebConfigRepository.findById(id)
      .orElseThrow(() -> new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_NOT_FOUND));
  }
  
  private void validateActions(List<ActionModel> actions) throws BusinessException {
    for (ActionModel action : actions) {
      ActionTypeEnum actionType = action.getActionType();
      String identifier = action.getIdentifier();
      String value = action.getValue();
      
      // Validate identifier is not null or empty when required
      if (!IDENTIFIER_SHOULD_DISABLE.contains(actionType) && KanbanCommonUtil.isNullOrEmpty(identifier)) {
        throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID, action.toString());
      }
      
      // Action types that require non-empty value
      if (VALUE_SHOULD_REQUIRE.contains(actionType) && KanbanCommonUtil.isNullOrEmpty(value)) {
        throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID, action.toString());
      }
      
      // WAIT and WAITING_FOR_ELEMENT must have a valid numeric value between 1 and 600
      if (NUMERIC_VALUE_REQUIRED.contains(actionType)) {
        try {
          double waitValue = Double.parseDouble(value);
          if (waitValue < CommonConstants.WAIT_SECOND_MIN_VALUE || waitValue > CommonConstants.WAIT_SECOND_MAX_VALUE) {
            throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID, action.toString());
          }
        } catch (NumberFormatException e) {
          throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID, action.toString());
        }
      }
      // Max length check
      if (!KanbanCommonUtil.isNullOrEmpty(value)) {
        int length = value.length();
        switch (actionType) {
          case GO_TO_URL:
            if (length > CommonConstants.MAX_LENGTH_2000_VALUE) {
              throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID, action.toString());
            }
            break;
          case SELECT_FROM_DROPDOWN:
            if (length > CommonConstants.MAX_LENGTH_1000_VALUE) {
              throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID, action.toString());
            }
            break;
          default:
            if (length > CommonConstants.MAX_LENGTH_500_VALUE) {
              throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID, action.toString());
            }
        }
      }
      // Max length of note
      if (!KanbanCommonUtil.isEmpty(action.getNote())
          && action.getNote().length() > CommonConstants.MAX_LENGTH_OF_NOTE) {
        throw new BusinessException(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID, action.toString());
      }
    }
  }
}
