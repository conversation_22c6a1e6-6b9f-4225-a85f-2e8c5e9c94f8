package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import java.util.List;
import lombok.Builder;
import lombok.Data;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/18/2024
 */

@Data
@Builder
public class SqlExecutionDataResponse {
  List<String> listColumns;
  private List<SqlExecutionResponse.SqlDataMapping>
      listDataMappings;
}
