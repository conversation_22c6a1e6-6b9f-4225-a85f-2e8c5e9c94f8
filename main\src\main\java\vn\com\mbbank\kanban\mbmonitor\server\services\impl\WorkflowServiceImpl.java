package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import jakarta.transaction.Transactional;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowNodeDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowNodeEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.WorkflowModelMapper;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.WorkflowRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.WorkflowNodeDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.WorkflowNodeService;
import vn.com.mbbank.kanban.mbmonitor.server.services.WorkflowService;

@Service
@RequiredArgsConstructor
public class WorkflowServiceImpl extends BaseServiceImpl<WorkflowEntity, String>
    implements WorkflowService {
  private final WorkflowRepository workflowRepository;
  private final WorkflowNodeService workflowNodeService;
  private final WorkflowNodeDependencyService workflowNodeDependencyService;

  private final WorkflowModelMapper workflowModelMapper = WorkflowModelMapper.INSTANCE;

  @Override
  protected JpaCommonRepository<WorkflowEntity, String> getRepository() {
    return workflowRepository;
  }

  @Override
  @Transactional
  public WorkflowEntity create(WorkflowModel workflow) throws BusinessException {
    var workflowEntity = new WorkflowEntity();
    var id = GeneratorUtil.generateId();
    workflowEntity.setId(id);
    workflowEntity.setStatus(WorkflowStatusEnum.WAITING);
    workflowNodeService.save(id, workflow);
    return workflowRepository.save(workflowEntity);
  }

  @Override
  @Transactional
  public void deleteWithId(String workflowId) throws BusinessException {
    var workflow = workflowRepository.findById(workflowId)
        .orElseThrow(() -> new BusinessException(ErrorCode.WORKFLOW_NOT_FOUND));
    // Delete old nodes and dependencies
    workflowNodeService.deleteAllByWorkflowId(workflow.getId());
    workflowRepository.delete(workflow);
  }

  @Override
  public WorkflowModel findWithId(String workflowId) throws BusinessException {
    var workflow = workflowRepository.findById(workflowId)
        .orElseThrow(() -> new BusinessException(ErrorCode.WORKFLOW_NOT_FOUND));
    List<WorkflowNodeEntity> nodes =
        workflowNodeService.findAllByWorkflowId(workflowId);
    List<WorkflowNodeDependencyEntity> dependencies =
        workflowNodeDependencyService.findAllByWorkflowId(workflowId);
    return workflowModelMapper.map(workflow, nodes, dependencies);
  }
}
