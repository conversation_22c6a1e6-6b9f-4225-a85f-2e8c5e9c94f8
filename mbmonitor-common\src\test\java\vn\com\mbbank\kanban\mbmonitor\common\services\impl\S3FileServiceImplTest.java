package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import vn.com.mbbank.kanban.mbmonitor.common.configs.S3Properties;
import vn.com.mbbank.kanban.mbmonitor.common.services.S3FileService;

@ExtendWith(MockitoExtension.class)
public class S3FileServiceImplTest {

  private S3Client s3Client;
  private S3FileService s3FileService;
  private S3Properties s3Properties;

  private final String BUCKET = "test-bucket";
  private final String KEY = "file.txt";
  private final byte[] CONTENT = "Hello".getBytes();

  @BeforeEach
  void setUp() {
    s3Properties = new S3Properties();
    s3Properties.setAccessKey("dummy-key");
    s3Properties.setSecretKey("dummy-secret");
    s3Properties.setEndpoint("http://localhost:9000");
    s3Properties.setRegion("ap-southeast-1");
    s3Properties.setBucketName(BUCKET);

    s3Client = mock(S3Client.class);
    s3FileService = new S3FileServiceImpl(s3Properties, s3Client);
  }

  @Test
  void putObject_success() {
    String key = "test/file.txt";
    byte[] content = "test content".getBytes();
    String contentType = "text/plain";

    when(s3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class))).thenReturn(null);

    assertDoesNotThrow(() -> s3FileService.putObject(key, content, contentType));
    verify(s3Client, times(1)).putObject(any(PutObjectRequest.class), any(RequestBody.class));
  }

  @Test
  void getObject_success() {
    String key = "test/file.txt";
    byte[] expectedContent = "test content".getBytes();
    ResponseBytes<GetObjectResponse> responseBytes = mock(ResponseBytes.class);
    when(responseBytes.asByteArray()).thenReturn(expectedContent);
    when(s3Client.getObjectAsBytes(any(GetObjectRequest.class))).thenReturn(responseBytes);

    byte[] result = s3FileService.getObject(key);

    assertArrayEquals(expectedContent, result);
    verify(s3Client, times(1)).getObjectAsBytes(any(GetObjectRequest.class));
  }

  @Test
  void delete_Object_success() {
    String key = "test/file.txt";
    when(s3Client.deleteObject(any(DeleteObjectRequest.class))).thenReturn(null);

    assertDoesNotThrow(() -> s3FileService.deleteObject(key));
    verify(s3Client, times(1)).deleteObject(any(DeleteObjectRequest.class));
  }

  @Test
  void exists_success_fileHeadObject() {
    String key = "test/file.txt";
    when(s3Client.headObject(any(HeadObjectRequest.class))).thenReturn(null);

    boolean result = s3FileService.headObject(key);

    assertTrue(result);
    verify(s3Client, times(1)).headObject(any(HeadObjectRequest.class));
  }

  @Test
  void exists_success_fileNotHeadObject() {
    String key = "test/nonexistent.txt";
    doThrow(S3Exception.class).when(s3Client).headObject(any(HeadObjectRequest.class));

    boolean result = s3FileService.headObject(key);

    assertFalse(result);
    verify(s3Client, times(1)).headObject(any(HeadObjectRequest.class));
  }

  @Test
  void getS3Client_success() {
    S3Client result = s3FileService.getS3Client();
    assertEquals(s3Client, result);
  }

  @Test
  void getBucketName_success() {
    String result = s3FileService.getBucketName();
    assertEquals(BUCKET, result);
  }

  @Test
  void beginMultipartPutObject_success() {
    String key = "test/large-file.txt";
    String contentType = "text/plain";
    String expectedUploadId = "upload-123";

    CreateMultipartUploadResponse response = mock(CreateMultipartUploadResponse.class);
    when(response.uploadId()).thenReturn(expectedUploadId);
    when(s3Client.createMultipartUpload(any(CreateMultipartUploadRequest.class))).thenReturn(response);

    String result = s3FileService.createMultipartUpload(key, contentType);

    assertEquals(expectedUploadId, result);
    verify(s3Client, times(1)).createMultipartUpload(any(CreateMultipartUploadRequest.class));
  }

  @Test
  void abortMultipartPutObject_success() {
    String key = "test/large-file.txt";
    String uploadId = "upload-123";
    when(s3Client.abortMultipartUpload(any(AbortMultipartUploadRequest.class))).thenReturn(null);

    assertDoesNotThrow(() -> s3FileService.abortMultipartUpload(key, uploadId));
    verify(s3Client, times(1)).abortMultipartUpload(any(AbortMultipartUploadRequest.class));
  }

  @Test
  void completeMultipartPutObject_success() {
    String key = "test/large-file.txt";
    String uploadId = "upload-123";
    List<String> etags = Arrays.asList("etag1", "etag2", "etag3");

    when(s3Client.completeMultipartUpload(any(CompleteMultipartUploadRequest.class))).thenReturn(null);

    assertDoesNotThrow(() -> s3FileService.completeMultipartUpload(key, uploadId, etags));
    verify(s3Client, times(1)).completeMultipartUpload(any(CompleteMultipartUploadRequest.class));
  }

  @Test
  void putObjectPart_success() {
    String key = "test/large-file.txt";
    String uploadId = "upload-123";
    int partNumber = 1;
    byte[] partBytes = "part content".getBytes();
    String expectedETag = "etag-123";

    UploadPartResponse response = mock(UploadPartResponse.class);
    when(response.eTag()).thenReturn(expectedETag);
    when(s3Client.uploadPart(any(UploadPartRequest.class), any(RequestBody.class))).thenReturn(response);

    String result = s3FileService.uploadPart(key, uploadId, partNumber, partBytes);

    assertEquals(expectedETag, result);
    verify(s3Client, times(1)).uploadPart(any(UploadPartRequest.class), any(RequestBody.class));
  }

  @Test
  void completeMultipartPutObject_emptyEtags_success() {
    String key = "test/large-file.txt";
    String uploadId = "upload-123";
    List<String> etags = Arrays.asList();

    when(s3Client.completeMultipartUpload(any(CompleteMultipartUploadRequest.class))).thenReturn(null);

    assertDoesNotThrow(() -> s3FileService.completeMultipartUpload(key, uploadId, etags));
    verify(s3Client, times(1)).completeMultipartUpload(any(CompleteMultipartUploadRequest.class));
  }

  @Test
  void putObject_withS3Exception_throwsException() {
    String key = "test/file.txt";
    byte[] content = "test content".getBytes();
    String contentType = "text/plain";

    doThrow(S3Exception.class).when(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));

    assertThrows(S3Exception.class, () -> s3FileService.putObject(key, content, contentType));
    verify(s3Client, times(1)).putObject(any(PutObjectRequest.class), any(RequestBody.class));
  }

  @Test
  void getObject_withS3Exception_throwsException() {
    String key = "test/nonexistent.txt";
    doThrow(S3Exception.class).when(s3Client).getObjectAsBytes(any(GetObjectRequest.class));

    assertThrows(S3Exception.class, () -> s3FileService.getObject(key));
    verify(s3Client, times(1)).getObjectAsBytes(any(GetObjectRequest.class));
  }

  @Test
  void delete_Object_withS3Exception_throwsException() {
    String key = "test/file.txt";
    doThrow(S3Exception.class).when(s3Client).deleteObject(any(DeleteObjectRequest.class));

    assertThrows(S3Exception.class, () -> s3FileService.deleteObject(key));
    verify(s3Client, times(1)).deleteObject(any(DeleteObjectRequest.class));
  }

  @Test
  void beginMultipartPutObject_withS3Exception_throwsException() {
    String key = "test/large-file.txt";
    String contentType = "text/plain";
    doThrow(S3Exception.class).when(s3Client).createMultipartUpload(any(CreateMultipartUploadRequest.class));

    assertThrows(S3Exception.class, () -> s3FileService.createMultipartUpload(key, contentType));
    verify(s3Client, times(1)).createMultipartUpload(any(CreateMultipartUploadRequest.class));
  }

  @Test
  void abortMultipartPutObject_withS3Exception_throwsException() {
    String key = "test/large-file.txt";
    String uploadId = "upload-123";
    doThrow(S3Exception.class).when(s3Client).abortMultipartUpload(any(AbortMultipartUploadRequest.class));

    assertThrows(S3Exception.class, () -> s3FileService.abortMultipartUpload(key, uploadId));
    verify(s3Client, times(1)).abortMultipartUpload(any(AbortMultipartUploadRequest.class));
  }

  @Test
  void completeMultipartPutObject_withS3Exception_throwsException() {
    String key = "test/large-file.txt";
    String uploadId = "upload-123";
    List<String> etags = Arrays.asList("etag1", "etag2");

    doThrow(S3Exception.class).when(s3Client).completeMultipartUpload(any(CompleteMultipartUploadRequest.class));

    assertThrows(S3Exception.class, () -> s3FileService.completeMultipartUpload(key, uploadId, etags));
    verify(s3Client, times(1)).completeMultipartUpload(any(CompleteMultipartUploadRequest.class));
  }

  @Test
  void putObjectPart_withS3Exception_throwsException() {
    String key = "test/large-file.txt";
    String uploadId = "upload-123";
    int partNumber = 1;
    byte[] partBytes = "part content".getBytes();

    doThrow(S3Exception.class).when(s3Client).uploadPart(any(UploadPartRequest.class), any(RequestBody.class));

    assertThrows(S3Exception.class, () -> s3FileService.uploadPart(key, uploadId, partNumber, partBytes));
    verify(s3Client, times(1)).uploadPart(any(UploadPartRequest.class), any(RequestBody.class));
  }

  @Test
  void completeMultipartPutObject_singleEtag_success() {
    String key = "test/large-file.txt";
    String uploadId = "upload-123";
    List<String> etags = Arrays.asList("etag1");

    when(s3Client.completeMultipartUpload(any(CompleteMultipartUploadRequest.class))).thenReturn(null);

    assertDoesNotThrow(() -> s3FileService.completeMultipartUpload(key, uploadId, etags));
    verify(s3Client, times(1)).completeMultipartUpload(any(CompleteMultipartUploadRequest.class));
  }

  @Test
  void putObjectPart_largePartNumber_success() {
    String key = "test/large-file.txt";
    String uploadId = "upload-123";
    int partNumber = 10000;
    byte[] partBytes = "part content".getBytes();
    String expectedETag = "etag-10000";

    UploadPartResponse response = mock(UploadPartResponse.class);
    when(response.eTag()).thenReturn(expectedETag);
    when(s3Client.uploadPart(any(UploadPartRequest.class), any(RequestBody.class))).thenReturn(response);

    String result = s3FileService.uploadPart(key, uploadId, partNumber, partBytes);

    assertEquals(expectedETag, result);
    verify(s3Client, times(1)).uploadPart(any(UploadPartRequest.class), any(RequestBody.class));
  }

  @Test
  void putObject_emptyContent_success() {
    String key = "test/empty-file.txt";
    byte[] content = new byte[0];
    String contentType = "text/plain";

    when(s3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class))).thenReturn(null);

    assertDoesNotThrow(() -> s3FileService.putObject(key, content, contentType));
    verify(s3Client, times(1)).putObject(any(PutObjectRequest.class), any(RequestBody.class));
  }

  @Test
  void putObjectPart_emptyContent_success() {
    String key = "test/large-file.txt";
    String uploadId = "upload-123";
    int partNumber = 1;
    byte[] partBytes = new byte[0];
    String expectedETag = "etag-empty";

    UploadPartResponse response = mock(UploadPartResponse.class);
    when(response.eTag()).thenReturn(expectedETag);
    when(s3Client.uploadPart(any(UploadPartRequest.class), any(RequestBody.class))).thenReturn(response);

    String result = s3FileService.uploadPart(key, uploadId, partNumber, partBytes);

    assertEquals(expectedETag, result);
    verify(s3Client, times(1)).uploadPart(any(UploadPartRequest.class), any(RequestBody.class));
  }
}
