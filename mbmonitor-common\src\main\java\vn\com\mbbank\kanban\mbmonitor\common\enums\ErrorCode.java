package vn.com.mbbank.kanban.mbmonitor.common.enums;

import org.springframework.http.HttpStatus;
import vn.com.mbbank.kanban.core.common.BaseErrorCode;

/**
 * Enum representing the ErrorCode of system with code , message, status.
 */
public enum ErrorCode implements BaseErrorCode {
  /**
   * List Error Code In System.
   */
  CUSTOM_MESSAGE("001", "custom.message", HttpStatus.UNPROCESSABLE_ENTITY),
  SERVER_ERROR("002", "server.error", HttpStatus.UNPROCESSABLE_ENTITY),
  INSUFFICIENT_SCOPE("003", "insufficient.scope", HttpStatus.FORBIDDEN),
  ITEM_EXIST("004", "item.data.exist", HttpStatus.NOT_ACCEPTABLE),
  NOT_FOUND_ITEM("005", "not.found.item.data", HttpStatus.NOT_FOUND),
  CREATED_ITEM_ERROR("006", "created.item.error", HttpStatus.UNPROCESSABLE_ENTITY),
  TOKEN_INVALID("007", "token.invalid", HttpStatus.UNAUTHORIZED),
  TOKEN_EXPIRED("008", "token.expired", HttpStatus.UNAUTHORIZED),
  CALL_API_HTTP_ERROR("009", "call.api.http.error", HttpStatus.BAD_REQUEST),
  ILLEGAL_ACCESS("010", "not.allowed.access.field", HttpStatus.BAD_REQUEST),
  MESSAGE_ERROR_CUSTOM("011", "{0}", HttpStatus.BAD_REQUEST),
  KAFKA_SEND_FAILED("012", "Unable to send Kafka message. Broker may be unavailable or timeout occurred.",
      HttpStatus.SERVICE_UNAVAILABLE),
  UNAUTHORIZED("013", "unauthorized", HttpStatus.UNAUTHORIZED),
  REFRESH_TOKEN_INVALID("014", "Refresh token invalid. ", HttpStatus.BAD_REQUEST),

  /**
   * List Error Code In User.
   */
  USER_DONT_HAVE_PERMISSIONS("100", "You don't have permissions. ", HttpStatus.FORBIDDEN),
  USER_NOT_FOUND("101", "User not found. ", HttpStatus.FORBIDDEN),
  USER_IS_EXISTS("102", "User {0} exists. ", HttpStatus.BAD_REQUEST),
  USER_CANNOT_SETTING_YOURSELF("103", "You cannot setting yourself up", HttpStatus.BAD_REQUEST),
  PASSWORD_BE_NULL("104", "Password cannot be null or empty. ", HttpStatus.BAD_REQUEST),

  /**
   * List Error Code Alert.
   */
  ALERT_NOT_FOUND("200", "Alert not found. ", HttpStatus.NOT_FOUND),
  ALERT_DO_NOT_HAVE_COMMENT_IN_PAST("201", "The last comment was {0} minute ago.",
      HttpStatus.BAD_REQUEST),

  /**
   * List Error Code Service.
   */
  SERVICE_NOT_FOUND("300", "Service not found. ", HttpStatus.NOT_FOUND),
  SERVICE_NAME_IS_NOT_EMPTY("301", "Service name cannot be empty. ", HttpStatus.BAD_REQUEST),
  SERVICE_NAME_EXCEEDS_LENGTH("302", "Service name exceeds 100 characters", HttpStatus.BAD_REQUEST),
  SERVICE_DESCRIPTION_EXCEEDS_LENGTH("303", "Description exceeds 300 characters",
      HttpStatus.BAD_REQUEST),
  SERVICE_NAME_EXIST("304", "Service name already exists.", HttpStatus.BAD_REQUEST),
  SERVICE_CANNOT_BE_DELETED("305", "This service cannot be deleted.", HttpStatus.BAD_REQUEST),
  SERVICE_CANNOT_BE_CHANGED("306", "This service cannot be changed.", HttpStatus.BAD_REQUEST),
  SERVICE_HAS_BEEN_DELETED("307", "The requested service has been deleted.", HttpStatus.NOT_FOUND),
  SERVICE_IS_EXISTING_IN_TRIGGER("308", "Service exists in trigger.", HttpStatus.BAD_REQUEST),

  /**
   * List Error Code Application.
   */
  APPLICATION_NOT_FOUND("400", "Application not found. ", HttpStatus.NOT_FOUND),
  APPLICATION_NAME_IS_NOT_EMPTY("401", "Application name cannot be empty. ",
      HttpStatus.BAD_REQUEST),
  APPLICATION_NAME_EXCEEDS_LENGTH("402", "Application name exceeds 100 characters",
      HttpStatus.BAD_REQUEST),
  APPLICATION_DESCRIPTION_EXCEEDS_LENGTH("403", "Description exceeds 300 characters",
      HttpStatus.BAD_REQUEST),
  APPLICATION_NAME_EXIST("404", "Application name already exists.", HttpStatus.BAD_REQUEST),
  APPLICATION_CANNOT_BE_DELETED("405", "This application cannot be deleted.",
      HttpStatus.BAD_REQUEST),
  APPLICATION_IS_EXISTING_IN_TRIGGER("406", "Application exists in trigger.", HttpStatus.BAD_REQUEST),

  /**
   * List Error Code Custom object.
   */
  CUSTOM_OBJECT_NOT_FOUND("500", "Custom object not found. ", HttpStatus.NOT_FOUND),
  CUSTOM_OBJECT_NAME_IS_NOT_EMPTY("501", "Custom object name cannot be empty. ",
      HttpStatus.BAD_REQUEST),
  CUSTOM_OBJECT_NAME_EXIST("502", "Custom object name already exists.", HttpStatus.BAD_REQUEST),
  CUSTOM_OBJECT_CANNOT_BE_DELETED("503", "This custom object cannot be deleted.", HttpStatus.BAD_REQUEST),
  /**
   * List Error Code Collect Email Config.
   */
  COLLECT_EMAIL_CONFIG_NOT_FOUND("600", "Collect email config not found. ", HttpStatus.NOT_FOUND),
  COLLECT_EMAIL_CONFIG_NAME_EXIST("601", "Collect email config  already exists. ",
      HttpStatus.BAD_REQUEST),
  COLLECT_EMAIL_CONFIG_NOTIFY_KAFKA_ERROR("602",
      "Failed to initialize or reset the job configuration due to a system error.",
      HttpStatus.BAD_REQUEST),

  /**
   * List Error Code Export Data.
   */
  EXPORT_DATA_NOT_FOUND("700", "File not found. ", HttpStatus.NOT_FOUND),
  EXPORT_DATA_IN_PROCESS_OR_FAIL("701", "File export is in progress or failed.", HttpStatus.BAD_REQUEST),
  EXPORT_DATA_NO_PERMISSION("702", "You don't have permission to access this file.",
      HttpStatus.FORBIDDEN),

  /**
   * List Error Code Query SQL.
   */
  QUERY_SQL_NOT_FOUND("800", "Query sql not found. ", HttpStatus.NOT_FOUND),
  QUERY_SQL_SYNTAX_ERROR("801", "The SQL statement is not allowed or contains syntax errors.",
      HttpStatus.BAD_REQUEST),


  /**
   * List Error Code For Superiors.
   */
  SUPERIORS_EXECUTE_SQL_OMIT_WHERE_CLAUSE("1301", "Where clause is required",
      HttpStatus.BAD_REQUEST),
  SUPERIORS_EXECUTE_SQL_PRIVATE_PASSWORD_WRONG("1302", "Private password is wrong",
      HttpStatus.BAD_REQUEST),
  SUPERIORS_EXECUTE_SQL_ERROR_CONNECTION("1303", "Could not connect to database",
      HttpStatus.BAD_REQUEST),
  SUPERIORS_EXECUTE_SQL_ERROR_SYNTAX("1304", "SQL syntax exception", HttpStatus.BAD_REQUEST),
  SUPERIORS_EXECUTE_SQL_NOT_SUPPORT("1305", "SQL is not supported", HttpStatus.BAD_REQUEST),
  SUPERIORS_EXECUTE_SQL_MISS_PARAM("1306", "Missing parameter value for {0}",
      HttpStatus.BAD_REQUEST),

  /**
   * List Error WEBHOOK.
   */
  WEB_HOOK_CONFIG_SERVICE_NAME_NOT_EXIST("1400", "Service name is not exist",
      HttpStatus.BAD_REQUEST),
  WEB_HOOK_CONFIG_APPLICATION_NAME_NOT_EXIST("1401", "Application name is not exist",
      HttpStatus.BAD_REQUEST),
  WEB_HOOK_CONFIG_NOT_EXIST("1401", "WebHook is not exist",
      HttpStatus.NOT_FOUND),
  WEB_HOOK_CONFIG_PRIORITY_NOT_EXIST("1402", "Priority is not exist",
      HttpStatus.BAD_REQUEST),
  WEB_HOOK_CONFIG_NAME_EXIST("1403", "WebHook name {0} is exist",
      HttpStatus.BAD_REQUEST),
  WEB_HOOK_CONFIG_CONTENT_EMPTY("1404", "Content Alert is not empty!",
      HttpStatus.BAD_REQUEST),
  WEB_HOOK_CONFIG_PRIORITY_CONFIG_ID_IS_INVALID("1405", "Alert Priority Config Id is invalid.",
      HttpStatus.BAD_REQUEST),
  WEB_HOOK_CONFIG_SERVICE_ID_IS_INVALID("1406", "Service Id is invalid.",
      HttpStatus.BAD_REQUEST),
  WEB_HOOK_CONFIG_APPLICATION_ID_IS_INVALID("1407", "Application Id is invalid.",
      HttpStatus.BAD_REQUEST),

  /**
   * List Error Code In Excel Format.
   */
  EXCEL_FORMAT_IS_INVALID_HEADER_EMPTY("1500", "Header can not be empty.", HttpStatus.BAD_REQUEST),
  EXCEL_FORMAT_IS_INVALID("1501", "Header is not valid.", HttpStatus.BAD_REQUEST),
  EXCEL_FORMAT_IS_INVALID_HEADER_COLUMN_OVERLAP("1502", "Header column can not overlap. ",
      HttpStatus.BAD_REQUEST),
  NOT_EXCEL("1502", "The specified file is not Excel file", HttpStatus.BAD_REQUEST),
  FILE_IS_NOT_VALID("1503",
      "Can not open file special, please choose other file or modified file current. ",
      HttpStatus.BAD_REQUEST),
  FILE_EXPORT_IS_NOT_VALID("1504", "Can not export file special, please choose other file. ",
      HttpStatus.BAD_REQUEST),
  FILE_EXPORT_LIMIT_EXCEEDED("1505", "Maximum record limit for XLSX export is {0}",
      HttpStatus.BAD_REQUEST),

  /**
   * List Error Alert Priority Alert.
   */
  ALERT_PRIORITY_CONFIG_RAW_VALUE_EMPTY("1600", "RawValue can not be empty. ",
      HttpStatus.BAD_REQUEST),
  ALERT_PRIORITY_CONFIG_NOT_FOUND("1601", "Alert Priority Config not found. ",
      HttpStatus.NOT_FOUND),
  ALERT_PRIORITY_CONFIG_NAME_IS_EXISTED("1602", "Alert Priority Config name is existed. ",
      HttpStatus.BAD_REQUEST),
  INVALID_UPDATE_POSITION_REQUEST("1603", "Invalid update position request. ",
      HttpStatus.BAD_REQUEST),
  ALERT_PRIORITY_CONFIG_CANNOT_BE_DELETED("1604", "This alert priority config cannot be deleted.",
      HttpStatus.BAD_REQUEST),

  /**
   * List Role Error Code.
   */
  ROLE_NOT_FOUND("1700", "Role is not exist!", HttpStatus.NOT_FOUND),
  ROLE_NAME_IS_EXISTS("1701", "Role name is exist!", HttpStatus.NOT_FOUND),

  /**
   * List Error Email.
   */
  EMAIL_LIST_BE_EMPTY("1800", "Email list cannot be empty. ", HttpStatus.BAD_REQUEST),
  EMAIL_BE_NULL("1801", "Email cannot be null or empty. ", HttpStatus.BAD_REQUEST),
  EMAIL_IS_NOT_VALID("1802", "Email is not valid. ", HttpStatus.BAD_REQUEST),
  EMAIL_PARTNER_NOT_FOUND("1803", "Email partner not found. ", HttpStatus.BAD_REQUEST),
  EMAIL_PARTNER_NAME_EXIST("1804", "Partner name {0} is exist", HttpStatus.BAD_REQUEST),
  EMAIL_TEMPLATE_NOT_FOUND("1805", "Email template not found. ", HttpStatus.NOT_FOUND),
  EMAIL_TEMPLATE_NAME_EXIST("1806", "Template name {0} already exist", HttpStatus.BAD_REQUEST),
  EMAIL_ADDRESS_IS_NOT_VALID("1807", "Email address is not valid", HttpStatus.BAD_REQUEST),
  EMAIL_ADDRESS_NOT_EXIST("1808", "Email address is not exist", HttpStatus.BAD_REQUEST),
  EMAIL_SEND_MAIL_TEST_ERROR("1809", "Send email to test error", HttpStatus.BAD_REQUEST),
  EMAIL_IMAP_CONNECTION_FAIL("1810", "IMAP connection failed", HttpStatus.BAD_REQUEST),
  EMAIL_CONFIG_NOT_FOUND("1811", "Email config not found", HttpStatus.NOT_FOUND),
  EMAIL_CONFIG_EXIST("1812", "Email config  already exists.", HttpStatus.BAD_REQUEST),
  EMAIL_READ_MAIL_FAIL("1813", "Read list mail failed", HttpStatus.BAD_REQUEST),
  EMAIL_TEST_CONNECT_PROTOCOL_FAIL("1814", "Unsupported email protocol", HttpStatus.BAD_REQUEST),
  EMAIL_RECEIVER_TOO_MUCH("1815", "Email receiver with partner {} too much",
      HttpStatus.BAD_REQUEST),
  EMAIL_COMPOSED_SIZE_TOO_LARGE("1816", "Email too large to send", HttpStatus.BAD_REQUEST),
  EMAIL_SENDING_ERROR("1817", "An error occurred while sending email {}", HttpStatus.BAD_REQUEST),
  EMAIL_RECEIVER_IS_EMPTY("1818", "Email receiver is empty", HttpStatus.BAD_REQUEST),
  EMAIL_ADDRESS_NOT_FOUND("1819", "Email {} in partner {} not found", HttpStatus.NOT_FOUND),
  EMAIL_SENDER_NOT_FOUND("1820", "Email sender not found", HttpStatus.NOT_FOUND),
  EMAIL_CONFIG_INACTIVE("1821", "Email config is inactive", HttpStatus.BAD_REQUEST),
  EMAIL_CONFIG_DEPENDENCY_ERROR("1822", "Cannot update protocol as it is linked"
      + " to dependent collect email configurations.",
      HttpStatus.BAD_REQUEST),
  EMAIL_CONFIG_INVALID_SERVICE("1823", "Invalid service for Collect Email Config",
      HttpStatus.BAD_REQUEST),
  EMAIL_CONFIG_INVALID_APPLICATION("1824", "Invalid application for Collect Email Config",
      HttpStatus.BAD_REQUEST),
  EMAIL_CONFIG_INVALID_PRIORITY("1825", "Invalid priority for Collect Email Config",
      HttpStatus.BAD_REQUEST),
  EMAIL_CONFIG_INVALID_ABSENCE_INTERVAL("1826", "Invalid absence interval or alert repeat interval "
      + "for Collect Email Config", HttpStatus.BAD_REQUEST),
  EMAIL_CONFIG_INVALID_CONTENT_TYPE("1827", "Invalid content type "
      + "for Collect Email Config", HttpStatus.BAD_REQUEST),
  EMAIL_CONFIG_INVALID_CONTENT_VALUE("1828", "Invalid content value "
      + "for Collect Email Config", HttpStatus.BAD_REQUEST),

  /**
   * List Error File.
   */
  FILE_IS_EMPTY("1900", "The file {0} is empty", HttpStatus.BAD_REQUEST),
  FILE_DELETE_ERROR("1901", "An error occurred while deleting the file {0}",
      HttpStatus.BAD_REQUEST),
  FILE_PATH_NOT_FOUND("1902", "File path not found", HttpStatus.NOT_FOUND),
  FILE_SIZE_ERROR("1903", "The size of file {0} is exceeded {1}", HttpStatus.BAD_REQUEST),
  FILE_SIZE_TOTAL_ERROR("1904", "The size of total files is exceeded {1}", HttpStatus.BAD_REQUEST),
  INVALID_FILE_NAME("1905", "Invalid file name {0}", HttpStatus.BAD_REQUEST),


  /**
   * List Error Database connection.
   */
  DATABASE_CONNECT_FALSE("2000", "Connection Failed. ", HttpStatus.BAD_REQUEST),
  DATABASE_CONNECTION_NOT_EXISTS("2001", "Database connection not exists. ",
      HttpStatus.BAD_REQUEST),
  DATABASE_CONNECTION_EXISTS("2002", "Database connection is exist!", HttpStatus.BAD_REQUEST),

  DATABASE_CONNECTION_NOT_FOUND("2003", "Database connection is not exist!", HttpStatus.NOT_FOUND),
  DATABASE_CONNECTION_NOT_DELETE("2004",
      "There are configurations including {0} currently using the connection.",
      HttpStatus.NOT_FOUND),
  DATABASE_CONNECTION_NAME_EXISTS("2005", "Database connection name is exist!",
      HttpStatus.BAD_REQUEST),
  DATABASE_CONNECTION_IS_INACTIVE("2006", "Database connection is inactive!",
      HttpStatus.BAD_REQUEST),
  DATABASE_TYPE_NOT_SUPPORTED("2007", "Database connection type is not supported", HttpStatus.BAD_REQUEST),
  DATABASE_CONNECTION_ORACLE_TYPE_NOT_NULL("2008", "Oracle connection type must not be null", HttpStatus.BAD_REQUEST),
  DATABASE_CONNECTION_ORACLE_VALUE_NOT_NULL("2009", "Oracle connection SID or Service name must not be null",
      HttpStatus.BAD_REQUEST),
  DATABASE_CONNECTION_MS_SQL_DATABASE_NAME_NOT_NULL("2010", "MS SQL database name must not be null",
      HttpStatus.BAD_REQUEST),
  /**
   * List Error AlertGroupConfig.
   */
  ALERT_GROUP_CONFIG_NOT_FOUND("2100", "Alert group config not found. ", HttpStatus.NOT_FOUND),
  ALERT_GROUP_CONFIG_NAME_IS_EXISTED("2101", "Alert Group Config name is existed. ",
      HttpStatus.BAD_REQUEST),
  ALERT_GROUP_CONFIG_RULE_CAN_NOT_EMPTY("2102", "Alert Group Config rule can not empty. ",
      HttpStatus.BAD_REQUEST),
  ALERT_GROUP_CONFIG_CUSTOM_OBJECT_CAN_NOT_EMPTY("2103",
      "Alert Group Config custom object can not empty. ",
      HttpStatus.BAD_REQUEST),
  ALERT_GROUP_CONFIG_CUSTOM_ALERT_OUTPUT_INFO_CAN_NOT_EMPTY("2104",
      "Alert Group Config custom alert output info can not empty. ",
      HttpStatus.BAD_REQUEST),
  ALERT_GROUP_CONFIG_ALERT_OUTPUT_CUSTOM_SERVICE_INVALID("2105",
      "Alert Group Config custom alert output service is invalid. ",
      HttpStatus.BAD_REQUEST),
  ALERT_GROUP_CONFIG_ALERT_OUTPUT_CUSTOM_APPLICATION_INVALID("2106",
      "Alert Group Config custom alert output application is invalid. ",
      HttpStatus.BAD_REQUEST),
  ALERT_GROUP_CONFIG_ALERT_OUTPUT_CUSTOM_PRIORITY_INVALID("2107",
      "Alert Group Config custom alert output priority is invalid. ",
      HttpStatus.BAD_REQUEST),

  /**
   * Database Collect.
   */
  DATABASE_COLLECT_CREATE_DATE_FIELD_NOT_MAPPING("2201",
      "Database collect create date filed is not mapping!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_ALERT_ID_FIELD_NOT_MAPPING("2202",
      "Database collect alert id filed is not mapping!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_SERVICE_NAME_FIELD_NOT_MAPPING("2203",
      "Database collect service name filed is not mapping!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_APPLICATION_NAME_FIELD_NOT_MAPPING("2204",
      "Database collect application name filed is not mapping!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_PRIORITY_NAME_FIELD_NOT_MAPPING("2205",
      "Database collect priority name filed is not exist!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_ALERT_CONTENT_NAME_FIELD_NOT_MAPPING("2206",
      "Database collect alert content name filed is not mapping!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_CONTACT_NAME_FIELD_NOT_MAPPING("2207",
      "Database collect contact name filed is not mapping!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_SQL_PARAMETER_NOT_MAPPING("2208",
      "The query must include createDatefiled >= :createDate as a condition!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_NAME_IS_EXISTS("2209",
      "Database collect name is exists!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_IS_NOT_EXISTS("2210",
      "Database collect is not exists!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_COLUMN_DATA_NOT_FOUND("2211",
      "Column {0} not found!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_ROW_DATA_NOT_FOUND("2212",
      "row data not found!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_CONNECTION_INACTIVE("2213",
      "Connection is inactive!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_IS_INACTIVE("2214",
      "Database collect is inactive!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_ALERT_CONTENT_EMPTY("2215",
      "Database collect alert content is empty!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_ALERT_SERVICE_IS_INVALID("2216",
      "Database collect alert service is invalid!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_ALERT_APPLICATION_IS_INVALID("2217",
      "Database collect alert application is invalid!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_ALERT_PRIORITY_IS_INVALID("2218",
      "Database collect alert priority is invalid!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_ALERT_CONTENT_FIELD_NOT_MAPPING("2219",
      "Database collect alert content filed is not mapping!",
      HttpStatus.NOT_FOUND),
  DATABASE_COLLECT_ONLY_SELECT("2220",
      "The SQL command supports SELECT statements only!",
      HttpStatus.NOT_FOUND),

  /**
   * Maintenance time config.
   */
  MAINTENANCE_TIME_CONFIG_NOT_FOUND("2300", "Maintenance time config not found. ", HttpStatus.NOT_FOUND),
  MAINTENANCE_TIME_CONFIG_NAME_IS_EXISTED("2301", "Maintenance time config name is existed. ",
      HttpStatus.BAD_REQUEST),
  MAINTENANCE_TIME_CONFIG_TYPE_TIME_INFO_CAN_NOT_EMPTY("2302",
      "Maintenance time config type time info can not empty. ",
      HttpStatus.BAD_REQUEST),
  MAINTENANCE_TIME_CONFIG_CAN_NOT_BE_DELETED("2303", "Maintenance time config can not be deleted. ",
      HttpStatus.BAD_REQUEST),
  /**
   * Task.
   */
  TASK_START_TIME_CAN_NOT_EMPTY("2400", "Task start time can not be empty", HttpStatus.BAD_REQUEST),
  TASK_END_TIME_CAN_NOT_EMPTY("2401", "Task end time can not be empty", HttpStatus.BAD_REQUEST),
  END_TIME_CAN_NOT_BEFORE_START_TIME("2402", "End time can not before Start time", HttpStatus.BAD_REQUEST),
  TASK_NOT_IS_NOT_EXISTED("2403", "Task is not existed", HttpStatus.NOT_FOUND),
  CAN_NOT_UPDATE_TASK_TYPE("2404", "Can not update task type", HttpStatus.BAD_REQUEST),
  SHIFT_CAN_NOT_BE_EMPTY("2405", "Shift handover can not be empty", HttpStatus.BAD_REQUEST),
  SHIFT_HANDOVER_NEED_AT_LEAST_ONE_TASK("2406", "Shift handover need have at least one task", HttpStatus.BAD_REQUEST),
  HANDOVER_USER_CAN_NOT_BE_EMPTY("2407", "Handover user can not be empty", HttpStatus.BAD_REQUEST),
  TASK_NAME_CAN_NOT_BE_BLANK("2408", "Task name can not be blank", HttpStatus.BAD_REQUEST),
  FROM_DATE_DO_NOT_ALLOW_AFTER_TO_DATE("2409", "From Date do no allow after to date", HttpStatus.BAD_REQUEST),
  TASK_HAS_BEEN_COMPLETED_CAN_NOT_DELETE("2410", "The task has been completed and cannot be deleted.",
      HttpStatus.BAD_REQUEST),
  CHILDREN_TASKS_NOT_FULLY_ASSIGN_YET("2411", "The children tasks have not been fully assigned to me yet.",
      HttpStatus.BAD_REQUEST),
  ALREADY_HAS_HANDOVER_TASK_THAT_IS_IN_PROGRESS_STATUS("2412",
      "There is a handover task that is in progress status.",
      HttpStatus.BAD_REQUEST),
  CAN_NOT_ASSIGN_TASK_TO_INVALID_USER("2413", "Can not assign task to invalid user.",
      HttpStatus.BAD_REQUEST),
  TASK_HAS_BEEN_COMPLETED_CAN_NOT_BE_EDITED("2414", "The task has been completed and cannot be edited.",
      HttpStatus.BAD_REQUEST),
  TASK_HAS_BEEN_COMPLETED_CAN_NOT_BE_ASSIGN("2415", "The task has been completed and cannot be assign.",
      HttpStatus.BAD_REQUEST),
  TASK_ALREADY_COMPLETED("2416", "Task already completed.",
      HttpStatus.BAD_REQUEST),
  TASK_NEED_TO_BE_INPROGRESS_BEFORE_COMPLETE("2417", "Task need to be inprogress before complete.",
      HttpStatus.BAD_REQUEST),
  /**
   * Filter alert config.
   */
  FILTER_ALERT_CONFIG_NOT_FOUND("2500", "Filter alert config not found. ", HttpStatus.NOT_FOUND),
  FILTER_ALERT_CONFIG_NAME_IS_EXISTED("2501", "Filter alert config name is existed. ",
      HttpStatus.BAD_REQUEST),
  FILTER_ALERT_CONFIG_CAN_NOT_BE_DELETED("2502", "Filter alert config can not be deleted. ",
      HttpStatus.BAD_REQUEST),

  /**
   * Modify alert config.
   */
  MODIFY_ALERT_CONFIG_NOT_FOUND("2600", "Modify alert config not found. ", HttpStatus.NOT_FOUND),
  MODIFY_ALERT_CONFIG_NAME_IS_EXISTED("2601", "Modify alert config name is existed. ",
      HttpStatus.BAD_REQUEST),
  MODIFY_ALERT_CONFIG_CAN_NOT_BE_DELETED("2602", "Modify alert config can not be deleted. ",
      HttpStatus.BAD_REQUEST),
  MODIFY_ALERT_CONFIG_INVALID_UPDATE_POSITION_REQUEST("2603", "Invalid update position request. ",
      HttpStatus.BAD_REQUEST),
  SERVICE_IN_MODIFY_BE_DELETED("2604", "Existing service has already been deleted. ", HttpStatus.BAD_REQUEST),
  APPLICATION_IN_MODIFY_BE_DELETED("2605", "Existing application has already been deleted. ", HttpStatus.BAD_REQUEST),
  /**
   * Telegram.
   */
  TELEGRAM_ALERT_SEND_FALSE("2700", "Send message false!", HttpStatus.BAD_REQUEST),
  TELEGRAM_BOT_TOKEN_INVALID("2701", "Bot token invalid!", HttpStatus.BAD_REQUEST),
  ALERT_FILTERED_DENIED("2701", "Alert denied.", HttpStatus.BAD_REQUEST),
  ALERT_CONTENT_EMPTY("2702", "Alert content is empty!", HttpStatus.NOT_FOUND),

  /**
   * Syslog.
   */
  LOG_ALREADY_EXISTED("2800", "Log already existed!", HttpStatus.BAD_REQUEST),

  /**
   * Execution Group.
   */
  EXECUTION_GROUP_NOT_FOUND("2901", "Execution Group not found.", HttpStatus.NOT_FOUND),
  EXECUTION_GROUP_NAME_IS_EXISTED("2902", "Execution Group name is existed.", HttpStatus.BAD_REQUEST),

  /**
   * Execution.
   */
  EXECUTION_NOT_FOUND("3001", "Execution not found.", HttpStatus.NOT_FOUND),
  EXECUTION_DATABASE_CONNECTION_ID_CAN_NOT_BE_EMPTY("3002", "Database Connection Id can not be empty.",
      HttpStatus.BAD_REQUEST),
  EXECUTION_DATABASE_CONNECTION_NOT_FOUND("3003", "Execution database connection not found", HttpStatus.BAD_REQUEST),
  EXECUTION_TYPE_CAN_NOT_BE_UPDATE("3004", "Execution type can not be change.", HttpStatus.BAD_REQUEST),
  EXECUTION_GROUP_ID_CAN_NOT_BE_EMPTY("3005", "Execution group not found", HttpStatus.BAD_REQUEST),
  EXECUTION_NAME_IS_EXISTED("3006", "Execution name is existed.", HttpStatus.BAD_REQUEST),
  EXECUTION_SCRIPT_IS_INVALID("3007", "The execution script is invalid.",
      HttpStatus.BAD_REQUEST),
  EXECUTION_PYTHON_SCRIPT_USE_DISALLOWED_MODULE("3008", "The execution python script uses a disallowed module ({0})",
      HttpStatus.BAD_REQUEST),
  EXECUTION_PYTHON_SCRIPT_USE_DISALLOWED_FUNCTION("3008",
      "The execution python script uses a disallowed function ({0})",
      HttpStatus.BAD_REQUEST),
  EXECUTION_PARAM_IS_INVALID("3009", "Execution param is invalid", HttpStatus.BAD_REQUEST),
  EXECUTION_TIMED_OUT("3010", "Execution timed out", HttpStatus.BAD_REQUEST),
  EXECUTION_DATABASE_CONNECTION_IS_INACTIVE("3011", "Execution database connection is inactive",
      HttpStatus.BAD_REQUEST),
  // Use to check in FE
  EXECUTION_HAS_BEEN_UPDATED("3012", "Execution has been updated",
      HttpStatus.BAD_REQUEST),
  EXECUTION_RUN_FAILED("3013", "Execution run failed.",
      HttpStatus.BAD_REQUEST),
  // Use to check in FE
  EXECUTION_IS_RUNNING_LONGER_THAN_EXPECTED("3014", "Execution is running longer than expected.",
      HttpStatus.BAD_REQUEST),
  EXECUTION_IS_EXISTING_IN_TRIGGER("3015", "Execution exists in trigger.", HttpStatus.BAD_REQUEST),
  EXECUTION_API_INFO_INVALID("3016", "Execution api info invalid.", HttpStatus.BAD_REQUEST),

  /**
   * Variable.
   */
  VARIABLE_NOT_FOUND("3100", "Variable not found.", HttpStatus.NOT_FOUND),
  VARIABLE_NAME_IS_EXISTED("3101", "Variable name is existed.", HttpStatus.BAD_REQUEST),
  VARIABLE_VALUE_CAN_NOT_BE_EMPTY("3102", "Variable value can not be empty.", HttpStatus.BAD_REQUEST),

  /**
   * Execution History.
   */
  EXECUTION_HISTORY_NOT_FOUND("3200", "Execution history not found.", HttpStatus.NOT_FOUND),
  EXECUTION_WAS_BE_CANCELED("3200", "Execution was be cancelled.", HttpStatus.NOT_FOUND),

  /**
   * List Error Database Threshold.
   */
  DATABASE_THRESHOLD_CONFIG_NOT_FOUND("3300", "Database threshold config not found. ",
      HttpStatus.NOT_FOUND),
  DATABASE_THRESHOLD_CONFIG_NAME_IS_EXISTED("3301", "Database threshold config name is existed. ",
      HttpStatus.BAD_REQUEST),
  DATABASE_THRESHOLD_CONFIG_CAN_NOT_BE_DELETED("3302", "Database threshold config can not be deleted. ",
      HttpStatus.BAD_REQUEST),
  DATABASE_THRESHOLD_CONFIG_INVALID_SERVICE("3303", "Invalid service for database threshold config",
      HttpStatus.BAD_REQUEST),
  DATABASE_THRESHOLD_CONFIG_INVALID_APPLICATION("3304", "Invalid application for database threshold config",
      HttpStatus.BAD_REQUEST),
  DATABASE_THRESHOLD_CONFIG_INVALID_PRIORITY("3305", "Invalid priority for database threshold config",
      HttpStatus.BAD_REQUEST),
  DATABASE_THRESHOLD_CONFIG_NOTIFY_KAFKA_ERROR("3306",
      "Failed to initialize or reset the job configuration due to a system error.",
      HttpStatus.BAD_REQUEST),
  DATABASE_THRESHOLD_SQL_RESULT_IS_EMPTY("3307",
      "Unable to parse SQL result to number in database threshold.",
      HttpStatus.INTERNAL_SERVER_ERROR),
  DATABASE_THRESHOLD_SQL_COMMAND_INVALID("3308",
      "SQL command is invalid.",
      HttpStatus.BAD_REQUEST),

  /**
   * Teams.
   */
  TEAMS_CONFIG_DATA_INVALID("3400", "Teams data config invalid!", HttpStatus.BAD_REQUEST),
  TEAMS_CREATE_GROUP_ERROR("3401", "Teams create group invalid!", HttpStatus.BAD_REQUEST),

  TEAMS_CONFIG_NOT_FOUND("3402", "Teams config not found!", HttpStatus.BAD_REQUEST),
  TEAMS_CONTACT_INVALID("3403", "Teams contact invalid!", HttpStatus.BAD_REQUEST),
  TEAMS_GROUP_NOT_FOUND("3404", "Teams group not found!", HttpStatus.BAD_REQUEST),
  TEAMS_GROUP_COLLECT_ERROR("3405", "Teams collect group error!", HttpStatus.BAD_REQUEST),
  TEAMS_SEND_MESSAGE_ERROR("3406", "Teams send message fail please check config!",
      HttpStatus.BAD_REQUEST),

  /**
   * Auto Trigger Action.
   */
  AUTO_TRIGGER_ACTION_CONFIG_NOT_FOUND("3500", "Auto trigger action config not found!",
      HttpStatus.BAD_REQUEST),
  AUTO_TRIGGER_ACTION_CONFIG_NAME_IS_EXISTED("3501", "Auto trigger action config name is existed. ",
      HttpStatus.BAD_REQUEST),
  AUTO_TRIGGER_APPLICATION_HAS_BE_DELETED("3502", "Existing application has already been deleted. ",
      HttpStatus.BAD_REQUEST),
  SERVICE_IN_TRIGGER_BE_DELETED("3503", "Existing service has already been deleted. ",
      HttpStatus.BAD_REQUEST),
  EXECUTION_IN_TRIGGER_BE_DELETED("3504", "Existing execution has already been deleted. ",
      HttpStatus.BAD_REQUEST),

  /**
   * Notification.
   */
  NOTIFICATION_NOT_FOUND("3600", "Notification not found!", HttpStatus.BAD_REQUEST),
  NOTIFICATION_USERNAME_CAN_NOT_BE_EMPTY("3601", "Notification username can not be empty!", HttpStatus.BAD_REQUEST),

  /**
   * List Error RPA Monitor Web.
   */
  RPA_CONFIG_NOT_FOUND("3700", "Rpa config not found!", HttpStatus.NOT_FOUND),
  RPA_CONFIG_IS_INACTIVE("3701", "Rpa config is inactive!", HttpStatus.NOT_FOUND),
  MONITOR_WEB_CONFIG_NOT_FOUND("3702", "Monitor web config not found!", HttpStatus.NOT_FOUND),
  MONITOR_WEB_CONFIG_INTERVAL_INVALID("3703", "Interval {0} invalid!", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_CONFIG_ACTION_NOT_FOUND("3704", "Monitor action not found!", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_CONFIG_ACTION_INVALID("3705", "Monitor action invalid!", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_CONFIG_AUTH_ACTION_NOT_FOUND("3706", "Authentication action not found!", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_CONFIG_AUTH_ACTION_INVALID("3707", "Authentication action invalid!", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_CONFIG_USERNAME_INVALID("3708", "Authentication action.username invalid!", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_CONFIG_PASSWORD_INVALID("3709", "Authentication action.password invalid!", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_CONFIG_LOGIN_BUTTON_INVALID("3710", "Authentication action.login invalid!", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_CONFIG_NAME_IS_EXISTED("3711", "Monitor web config name is existed. ", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_JOB_ACTION_NOT_SUPPORTED("3712", "The action : {0} not supported!", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_JOB_CLOSE_POPUP_INVALID("3713", "No popup page to close or already in main page!",
    HttpStatus.BAD_REQUEST),
  MONITOR_WEB_JOB_BACK_TO_MAIN_INVALID("3714", "Main page is not initialized!", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_JOB_PERFORM_ERROR("3715", "Error performing action!", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_CONFIG_SERVICE_INVALID("3716", "Invalid service for monitor web config", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_CONFIG_APPLICATION_INVALID("3717", "Invalid application for monitor web config", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_CONFIG_PRIORITY_INVALID("3718", "Invalid priority for monitor web config", HttpStatus.BAD_REQUEST),
  RPA_CONFIG_ID_INVALID("3719", "RPA config already exists, please fill the RPA config id!", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_CONFIG_ACTION_NOT_VALID("3720", "The action : {0} invalid!", HttpStatus.BAD_REQUEST),
  MONITOR_WEB_JOB_ELEMENT_NOT_FOUND("3721", "Element {0} not visible!", HttpStatus.BAD_REQUEST),


  /**
   * Notification Event.
   */
  NOTIFICATION_EVENT_NOT_FOUND("3800", "Notification event not found!", HttpStatus.BAD_REQUEST),
  NOTIFICATION_EVENT_CRON_EXPRESSION_CAN_NOT_BE_EMPTY("3801", "Notification event cron expression can not be empty!",
      HttpStatus.BAD_REQUEST),
  NOTIFICATION_EVENT_TRIGGERED_DATE_CAN_NOT_BE_EMPTY("3802", "Notification event triggered date can not be empty!",
      HttpStatus.BAD_REQUEST),
  NOTIFICATION_EVENT_TARGET_CAN_NOT_BE_EMPTY("3803", "Notification event target can not be empty!",
      HttpStatus.BAD_REQUEST),
  NOTIFICATION_EVENT_TARGET_INVALID_USER_NAME("3804", "Notification event target invalid user name!",
      HttpStatus.BAD_REQUEST), NOTIFICATION_EVENT_TARGET_INVALID_ROLE_ID("3805",
      "Notification event target invalid role id!",
      HttpStatus.BAD_REQUEST),

  /**
   * List Error Code Alert Request.
   */
  ALERT_REQUEST_NOT_FOUND("3900", "This alert request not found!", HttpStatus.BAD_REQUEST),
  ALERT_REQUEST_CAN_NOT_REJECTED("3901", "You can not reject this alert request in status: {0}.",
      HttpStatus.BAD_REQUEST),
  ALERT_REQUEST_CAN_NOT_APPROVED("3902", "You can not approve this alert request in status: {0}.",
      HttpStatus.BAD_REQUEST),
  ALERT_REQUEST_CAN_NOT_UPDATE("3903", "You can not update this alert request in status: {0}.",
      HttpStatus.BAD_REQUEST),
  ALERT_REQUEST_INVALID_SERVICE("3904", "Invalid service for this alert request",
      HttpStatus.BAD_REQUEST),
  ALERT_REQUEST_INVALID_APPLICATION("3905", "Invalid application for this alert request",
      HttpStatus.BAD_REQUEST),
  ALERT_REQUEST_INVALID_PRIORITY("3906", "Invalid priority for this alert request",
      HttpStatus.BAD_REQUEST),
  ALERT_REQUEST_INVALID_STATUS("3907", "Invalid status for this alert request",
      HttpStatus.BAD_REQUEST),
  ALERT_REQUEST_UNAUTHORIZED_ACCESS("3908", "You are not authorized to modify this alert request.",
      HttpStatus.BAD_REQUEST),
  ALERT_REQUEST_WAITING_APPROVAL_FORBIDDEN("3909", "You are not allowed to modify an alert request that is waiting "
      + "for approval.", HttpStatus.BAD_REQUEST),
  ALERT_REQUEST_INVALID_NAME_DATABASE_CONFIG("3910", "Invalid name of database threshold config for this alert request",
      HttpStatus.BAD_REQUEST),
  ALERT_REQUEST_HAS_BEEN_APPROVED("3911", "The alert request has been approved",
      HttpStatus.BAD_REQUEST);

  private final String code;

  private final String message;

  private final HttpStatus httpStatus;

  ErrorCode(String code, String message, HttpStatus httpStatus) {
    this.code = code;
    this.httpStatus = httpStatus;
    this.message = message;
  }

  @Override
  public String getCode() {
    return this.code;
  }

  @Override
  public String getMessage() {
    return message;
  }

  @Override
  public HttpStatus getHttpStatus() {
    return httpStatus;
  }
}
