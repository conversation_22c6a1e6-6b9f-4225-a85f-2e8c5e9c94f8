package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertPriorityConfigConstants;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WebHookEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WebHookConfigMapTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.WebHookDto;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.WebHookRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;
import vn.com.mbbank.kanban.test.ApplicationTest;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 8/28/2024
 */
class WebHookServiceImplTest extends ApplicationTest {
  @Mock
  WebHookRepository webHookRepository;
  @Mock
  ServiceService serviceService;
  @Mock
  ApplicationService applicationService;
  @Mock
  AlertService alertService;

  @Mock
  AlertPriorityConfigService alertPriorityConfigService;
  @InjectMocks
  WebHookServiceImpl webHookServiceImpl;
  @Mock
  SysLogKafkaProducerService sysLogKafkaProducerService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void getRepository() {
    JpaCommonRepository<WebHookEntity, Long> result = webHookServiceImpl.getRepository();
    Assertions.assertEquals(webHookRepository, result);
  }


  void saveConfig_addNew() throws BusinessException {
    var data = new WebHookEntity();
    data.setId(1L);
    when(webHookRepository.save(any())).thenReturn(data);
    WebHookEntity result = webHookServiceImpl.saveConfig(new WebHookDto());
    Assertions.assertEquals(1, result.getId());
  }

  @Test
  void saveConfig_success() throws BusinessException {
    var dataInput = new WebHookDto();
    dataInput.setName("name");
    //service
    dataInput.setServiceId("abc");
    dataInput.setServiceNameType(WebHookConfigMapTypeEnum.CUSTOM);
    //application
    dataInput.setApplicationId("abc");
    dataInput.setApplicationType(WebHookConfigMapTypeEnum.CUSTOM);

    //alert
    dataInput.setAlertContentCustomValue("abc");
    dataInput.setAlertContentType(WebHookConfigMapTypeEnum.CUSTOM);
    //priority
    dataInput.setPriorityType(WebHookConfigMapTypeEnum.CUSTOM);
    dataInput.setAlertPriorityConfigId(1L);
    // contact
    dataInput.setContactCustomValue("abc");
    dataInput.setContactType(WebHookConfigMapTypeEnum.CUSTOM);
    var data = new WebHookEntity();
    data.setId(1L);
    data.setName("name");
    data.setPriorityType(WebHookConfigMapTypeEnum.CUSTOM);

    var config = new AlertPriorityConfigEntity();
    config.setDeleted(false);
    config.setPosition(1);
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(config);
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());

    when(webHookRepository.save(any())).thenReturn(data);
    WebHookEntity result = webHookServiceImpl.saveConfig(dataInput);
    Assertions.assertEquals(1, result.getId());
  }

  @Test
  void saveConfig_update() throws BusinessException {
    var dataInput = new WebHookDto();
    dataInput.setId(1L);
    dataInput.setName("name");
    //service
    dataInput.setServiceId("abc");
    dataInput.setServiceNameType(WebHookConfigMapTypeEnum.FROM_SOURCE);
    //application
    dataInput.setApplicationId("abc");
    dataInput.setApplicationType(WebHookConfigMapTypeEnum.FROM_SOURCE);

    //alert
    dataInput.setAlertContentCustomValue("abc");
    dataInput.setAlertContentType(WebHookConfigMapTypeEnum.FROM_SOURCE);
    //priority
    dataInput.setPriorityType(WebHookConfigMapTypeEnum.FROM_SOURCE);
    dataInput.setAlertPriorityConfigId(1L);
    // contact
    dataInput.setContactCustomValue("abc");
    dataInput.setContactType(WebHookConfigMapTypeEnum.FROM_SOURCE);
    var data = new WebHookEntity();
    data.setId(1L);
    data.setName("name");
    data.setPriorityType(WebHookConfigMapTypeEnum.FROM_SOURCE);
    when(webHookRepository.save(any())).thenReturn(data);
    when(webHookRepository.findById(any())).thenReturn(Optional.of(data));

    WebHookEntity result = webHookServiceImpl.saveConfig(dataInput);
    Assertions.assertEquals(1, result.getId());
  }

  @Test
  void saveConfig_failed_casePriorityConfigDeleted() throws BusinessException {
    var dataInput = new WebHookDto();
    dataInput.setId(1L);
    dataInput.setPriorityType(WebHookConfigMapTypeEnum.CUSTOM);
    dataInput.setAlertPriorityConfigId(1L);
    dataInput.setName("name");
    var data = new WebHookEntity();
    data.setId(1L);
    data.setAlertPriorityConfigId(AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID);
    var config = new AlertPriorityConfigEntity();
    config.setDeleted(true);
    config.setPosition(1);
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(config);

    Assertions.assertThrows(BusinessException.class,
        () -> webHookServiceImpl.saveConfig(dataInput));
  }

  @Test
  void saveConfig_failed_casePriorityConfigNotFound() throws BusinessException {
    var dataInput = new WebHookDto();
    dataInput.setId(1L);
    dataInput.setPriorityType(WebHookConfigMapTypeEnum.CUSTOM);
    dataInput.setAlertPriorityConfigId(1L);
    var data = new WebHookEntity();
    data.setId(1L);
    data.setAlertPriorityConfigId(AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID);
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(null);

    Assertions.assertThrows(BusinessException.class,
        () -> webHookServiceImpl.saveConfig(dataInput));
  }

  @Test
  void saveConfig_failed_caseServiceDeleted() throws BusinessException {
    var dataInput = new WebHookDto();
    dataInput.setId(1L);
    dataInput.setName("name");
    //service
    dataInput.setServiceId("abc");
    dataInput.setServiceNameType(WebHookConfigMapTypeEnum.CUSTOM);
    //application
    dataInput.setApplicationId("abc");
    dataInput.setApplicationType(WebHookConfigMapTypeEnum.CUSTOM);

    //alert
    dataInput.setAlertContentCustomValue("abc");
    dataInput.setAlertContentType(WebHookConfigMapTypeEnum.FROM_SOURCE);
    //priority
    dataInput.setPriorityType(WebHookConfigMapTypeEnum.FROM_SOURCE);
    dataInput.setAlertPriorityConfigId(1L);
    // contact
    dataInput.setContactCustomValue("abc");
    dataInput.setContactType(WebHookConfigMapTypeEnum.FROM_SOURCE);

    var data = new WebHookEntity();
    data.setId(1L);
    data.setAlertPriorityConfigId(AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID);

    ServiceEntity service = new ServiceEntity();
    service.setDeleted(true);
    when(serviceService.findById(any())).thenReturn(service);

    Assertions.assertThrows(BusinessException.class,
        () -> webHookServiceImpl.saveConfig(dataInput));
  }

  @Test
  void saveConfig_failed_caseServiceNotFound() throws BusinessException {
    var dataInput = new WebHookDto();
    dataInput.setId(1L);
    dataInput.setName("name");
    //service
    dataInput.setServiceId("abc");
    dataInput.setServiceNameType(WebHookConfigMapTypeEnum.CUSTOM);
    //application
    dataInput.setApplicationId("abc");
    dataInput.setApplicationType(WebHookConfigMapTypeEnum.CUSTOM);

    //alert
    dataInput.setAlertContentCustomValue("abc");
    dataInput.setAlertContentType(WebHookConfigMapTypeEnum.FROM_SOURCE);
    //priority
    dataInput.setPriorityType(WebHookConfigMapTypeEnum.FROM_SOURCE);
    dataInput.setAlertPriorityConfigId(1L);
    // contact
    dataInput.setContactCustomValue("abc");
    dataInput.setContactType(WebHookConfigMapTypeEnum.FROM_SOURCE);

    var data = new WebHookEntity();
    data.setId(1L);
    data.setAlertPriorityConfigId(AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID);

    when(serviceService.findById(any())).thenReturn(null);

    Assertions.assertThrows(BusinessException.class,
        () -> webHookServiceImpl.saveConfig(dataInput));
  }

  @Test
  void saveConfig_failed_caseApplicationDeleted() throws BusinessException {
    var dataInput = new WebHookDto();
    dataInput.setId(1L);
    dataInput.setName("name");
    //service
    dataInput.setServiceId("abc");
    dataInput.setServiceNameType(WebHookConfigMapTypeEnum.CUSTOM);
    //application
    dataInput.setApplicationId("abc");
    dataInput.setApplicationType(WebHookConfigMapTypeEnum.CUSTOM);

    //alert
    dataInput.setAlertContentCustomValue("abc");
    dataInput.setAlertContentType(WebHookConfigMapTypeEnum.FROM_SOURCE);
    //priority
    dataInput.setPriorityType(WebHookConfigMapTypeEnum.FROM_SOURCE);
    dataInput.setAlertPriorityConfigId(1L);
    // contact
    dataInput.setContactCustomValue("abc");
    dataInput.setContactType(WebHookConfigMapTypeEnum.FROM_SOURCE);

    var data = new WebHookEntity();
    data.setId(1L);
    data.setAlertPriorityConfigId(AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID);

    ServiceEntity service = new ServiceEntity();
    service.setDeleted(false);
    when(serviceService.findById(any())).thenReturn(service);

    ApplicationEntity application = new ApplicationEntity();
    application.setDeleted(true);
    when(applicationService.findById(any())).thenReturn(application);

    Assertions.assertThrows(BusinessException.class,
        () -> webHookServiceImpl.saveConfig(dataInput));
  }

  @Test
  void saveConfig_failed_caseApplicationNotFound() throws BusinessException {
    var dataInput = new WebHookDto();
    dataInput.setId(1L);
    dataInput.setName("name");
    //service
    dataInput.setServiceId("abc");
    dataInput.setServiceNameType(WebHookConfigMapTypeEnum.CUSTOM);
    //application
    dataInput.setApplicationId("abc");
    dataInput.setApplicationType(WebHookConfigMapTypeEnum.CUSTOM);

    //alert
    dataInput.setAlertContentCustomValue("abc");
    dataInput.setAlertContentType(WebHookConfigMapTypeEnum.FROM_SOURCE);
    //priority
    dataInput.setPriorityType(WebHookConfigMapTypeEnum.FROM_SOURCE);
    dataInput.setAlertPriorityConfigId(1L);
    // contact
    dataInput.setContactCustomValue("abc");
    dataInput.setContactType(WebHookConfigMapTypeEnum.FROM_SOURCE);

    var data = new WebHookEntity();
    data.setId(1L);
    data.setAlertPriorityConfigId(AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID);
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setDeleted(false);
    when(serviceService.findById(any())).thenReturn(serviceEntity);

    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());

    Assertions.assertThrows(BusinessException.class,
        () -> webHookServiceImpl.saveConfig(dataInput));
  }
  @Test
  void saveConfig_update_Not_Found() throws BusinessException {
    var dataInput = new WebHookDto();
    dataInput.setId(1L);
    var data = new WebHookEntity();
    data.setId(1L);
    when(webHookRepository.save(any())).thenReturn(data);
    when(webHookRepository.findById(any())).thenReturn(Optional.empty());
    Assertions.assertThrows(BusinessException.class, () -> {
      webHookServiceImpl.saveConfig(dataInput);
    });

  }

  @Test
  void saveConfig_update_notFoundAlertPriorityConfig() throws BusinessException {
    var dataInput = new WebHookDto();
    dataInput.setId(1L);
    dataInput.setAlertPriorityConfigId(1L);
    dataInput.setPriorityType(WebHookConfigMapTypeEnum.CUSTOM);
    var data = new WebHookEntity();
    data.setId(1L);
    when(webHookRepository.save(any())).thenReturn(data);
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(null);
    when(webHookRepository.findById(any())).thenReturn(Optional.empty());
    Assertions.assertThrows(BusinessException.class, () -> {
      webHookServiceImpl.saveConfig(dataInput);
    });

  }

  @Test
  void saveConfig_create_exist_name() throws BusinessException {
    var dataInput = new WebHookDto();
    dataInput.setName("thangnv");
    when(webHookRepository.existsWebHookEntitiesByNameIgnoreCase(any())).thenReturn(true);
    Assertions.assertThrows(BusinessException.class, () -> {
      webHookServiceImpl.saveConfig(dataInput);
    });

  }

  @Test
  void refreshToken() throws BusinessException {
    when(webHookRepository.findById(any())).thenReturn(Optional.of(new WebHookEntity()));
    webHookServiceImpl.refreshToken(1L);
    verify(webHookRepository).save(any());
  }

  @Test
  void refreshToken_exception() throws BusinessException {
    when(webHookRepository.findById(any())).thenReturn(Optional.empty());
    Assertions.assertThrows(BusinessException.class, () -> {
      webHookServiceImpl.refreshToken(1L);
    });

  }

  @Test
  void findAllByServiceId_success() {
    when(webHookRepository.findAllByServiceId(any())).thenReturn(List.of(new WebHookEntity()));
    var res = webHookServiceImpl.findAllByServiceId("1L");
    verify(webHookRepository).findAllByServiceId(any());
    assertEquals(1, res.size());
  }

  @Test
  void findAllByApplicationId_success() {
    when(webHookRepository.findAllByApplicationId(any())).thenReturn(List.of(new WebHookEntity()));
    var res = webHookServiceImpl.findAllByApplicationId("1L");
    verify(webHookRepository).findAllByApplicationId(any());
    assertEquals(1, res.size());
  }
  @TestForDev
  void findAllByAlertPriorityConfigId_success() {
    when(webHookRepository.findAllByAlertPriorityConfigId(any())).thenReturn(List.of(new WebHookEntity()));
    var res = webHookServiceImpl.findAllByAlertPriorityConfigId(1L);
    verify(webHookRepository).findAllByAlertPriorityConfigId(any());
    assertEquals(1, res.size());
  }
}
