package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TeamsConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TeamsIntervalTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/23/2025
 */
@Getter
@Setter
public class TeamsConfigRequest {
  private String id;

  private String tenantId;

  private String clientId;

  private String clientSecret;

  @JsonProperty("username")
  private String email;

  private String password;

  private TeamsConfigTypeEnum type;

  private String messageTemplate;

  private String interval;

  private TeamsIntervalTypeEnum intervalType;

  private String description;
}
