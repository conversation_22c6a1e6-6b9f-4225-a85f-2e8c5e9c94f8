package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

/**
 * Response DTO for auto trigger action configurations.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AutoTriggerActionConfigResponse {
  String id;
  String name;
  String description;
  Long timeSinceLastTrigger;
  @Builder.Default
  Boolean active = true;
  AlertSourceTypeEnum type;
  RuleGroupType ruleGroup;
  String ruleGroupColumn;
  @Builder.Default
  List<ServiceResponse> services = new ArrayList<>();
  @Builder.Default
  List<ApplicationResponse> applications = new ArrayList<>();
  @Builder.Default
  List<ExecutionResponse> executions = new ArrayList<>();
}
