package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.BaseFormBuilderInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.FormBuilderElementTypeEnum;

/**
 * CustomInputResponse.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomInputResponse {
  String id;
  String name;
  String description;
  FormBuilderElementTypeEnum type;
  BaseFormBuilderInputElementModel configuration;
}
