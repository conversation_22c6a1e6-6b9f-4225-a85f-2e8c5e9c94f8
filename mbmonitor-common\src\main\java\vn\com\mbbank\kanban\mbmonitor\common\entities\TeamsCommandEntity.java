package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;

@Getter
@Setter
@Entity
@Table(name = TableName.ALERT_REQUEST_DATABASE)
@DynamicInsert
@KanbanAutoGenerateUlId
public class TeamsCommandEntity extends BaseEntity<String> {
  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "COMMAND")
  private String command;

  @Column(name = "EXECUTION_ID")
  private String executionId;

  @Column(name = "DESCRIPTION")
  private String description;
}