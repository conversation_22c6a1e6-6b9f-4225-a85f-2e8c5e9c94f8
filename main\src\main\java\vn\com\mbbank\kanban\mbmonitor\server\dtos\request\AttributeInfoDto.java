package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Note Information of Author.
 *
 * <AUTHOR>
 * @created_date 09/26/2024
 */

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AttributeInfoDto {
  private Integer position;
  private String attributeId;
  private String attributeName;
  private String value;
  private Integer width;
  private boolean defaultAttribute;

  /**
   * Constructs a new AttributeInfoDto object.
   *
   * @param position      The position of the attribute.
   * @param attributeId   The ID of the attribute.
   * @param attributeName The name of the attribute.
   */
  public AttributeInfoDto(Integer position, String attributeId, String attributeName) {
    this.position = position;
    this.attributeId = attributeId;
    this.attributeName = attributeName;
  }

  /**
   * Constructs a new AttributeInfoDto object.
   *
   * @param position      The position of the attribute.
   * @param attributeId   The ID of the attribute.
   * @param attributeName The name of the attribute.
   * @param value         The name of the attribute.
   */
  public AttributeInfoDto(Integer position, String attributeId, String attributeName, String value) {
    this.position = position;
    this.attributeId = attributeId;
    this.attributeName = attributeName;
    this.value = value;
  }

  /**
   * Constructs a new AttributeInfoDto object.
   *
   * @param attributeId   The ID of the attribute.
   * @param attributeName The name of the attribute.
   * @param value         The name of the attribute.
   */
  public AttributeInfoDto(String attributeId, String attributeName, String value) {
    this.attributeId = attributeId;
    this.attributeName = attributeName;
    this.value = value;
  }

}
