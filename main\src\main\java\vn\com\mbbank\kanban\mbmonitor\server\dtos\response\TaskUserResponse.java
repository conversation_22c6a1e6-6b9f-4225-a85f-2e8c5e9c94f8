package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import java.util.Date;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskUserTypeEnum;

/**
 * Model view task user response.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TaskUserResponse {
  Long id;
  Long taskId;
  String userName;
  Date createdDate;
  TaskUserTypeEnum type;
  String createdBy;
}