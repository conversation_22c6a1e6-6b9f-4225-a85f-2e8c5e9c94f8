package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.mbmonitor.server.services.BrowserSyncService;
import vn.com.mbbank.kanban.test.ApplicationTest;

@ExtendWith(MockitoExtension.class)
class BrowserSyncControllerTest extends ApplicationTest {
  
  @Mock
  BrowserSyncService browserSyncService;
  
  @InjectMocks
  BrowserSyncController browserSyncController;
  
  {
    browserSyncController = new BrowserSyncController(browserSyncService) {
      @Override public void makeSureUserAdmin() {}
    };
  }
  
  @Test
  void extractResources_success() throws Exception {
    // Given
    boolean force = true;
    String expected = "Sync completed";
    
    when(browserSyncService.checkAndSyncBrowsers(force)).thenReturn(expected);
    
    // When
    String result = browserSyncController.extractResources(force);
    
    // Then
    verify(browserSyncService, times(1)).checkAndSyncBrowsers(force);
    assertEquals(expected, result);
  }
  
  @Test
  void extractResources_exceptionThrown() throws Exception {
    // Given
    boolean force = false;
    when(browserSyncService.checkAndSyncBrowsers(force)).thenThrow(new RuntimeException("Permission denied"));
    
    // When
    String result = browserSyncController.extractResources(force);
    
    // Then
    verify(browserSyncService, times(1)).checkAndSyncBrowsers(force);
    assertEquals("Has an error when copy file : Permission denied", result);
  }
}
