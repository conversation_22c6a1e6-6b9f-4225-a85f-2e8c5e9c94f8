package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * ServiceDependenciesResponse.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = false)
public class ServiceDependenciesResponse {
  List<String> applications;
  List<String> webHooks;
  List<String> collectEmailConfigs;
  List<String> alertGroupConfigs;
  List<String> databaseCollects;
  List<String> maintenanceTimeConfigs;
  List<String> modifyAlertConfigs;
  List<String> databaseThresholdConfigs;
  List<String> triggerConfigs;
  List<String> monitorWebConfigs;
  List<String> alertRequests;
}
