package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.enums.FormBuilderElementTypeEnum;

/**
 * TextInputElement.
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TextInputElementModel extends BaseFormBuilderInputElementModel {
  FormBuilderElementTypeEnum type = FormBuilderElementTypeEnum.TEXT;
  String placeholder;
  Integer maxLength;
  String defaultValue;
}
