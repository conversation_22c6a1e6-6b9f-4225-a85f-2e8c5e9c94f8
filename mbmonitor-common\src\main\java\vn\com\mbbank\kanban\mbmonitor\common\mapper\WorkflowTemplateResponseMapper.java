package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.WorkflowTemplateResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowTemplateEntity;

/**
 * WorkflowTemplateResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WorkflowTemplateResponseMapper
    extends KanbanBaseMapper<WorkflowTemplateResponse, WorkflowTemplateEntity> {
  WorkflowTemplateResponseMapper INSTANCE = Mappers.getMapper(WorkflowTemplateResponseMapper.class);

  /**
   * map from WorkflowTemplateEntity to WorkflowTemplateResponse.
   *
   * @param entity WorkflowTemplateEntity.
   * @return WorkflowTemplateResponse.
   */
  WorkflowTemplateResponse map(WorkflowTemplateEntity entity);
}
