package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.core.services.common.BaseSoftServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigConditionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupOutputEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.log.AlertGroupConfigLogModelMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupConverter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.AlertGroupConditionDependenciesModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertGroupConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertGroupConfigUpdatePositionRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AlertGroupConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.AlertGroupConfigConditionEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.AlertGroupConfigEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.AlertGroupConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AlertGroupConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertGroupConfigConditionService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertGroupConfigDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertGroupConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.CustomObjectService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;

/**
 * Service Logic AlertGroupConfigServiceImpl.
 */
@Service
public class AlertGroupConfigServiceImpl extends BaseSoftServiceImpl<AlertGroupConfigEntity, Long>
    implements AlertGroupConfigService {

  private final AlertGroupConfigLogModelMapper alertGroupConfigLogModelMapper =
      AlertGroupConfigLogModelMapper.INSTANCE;
  private final AlertGroupConfigConditionEntityMapper alertGroupConfigConditionEntityMapper =
      AlertGroupConfigConditionEntityMapper.INSTANCE;
  private final AlertGroupConfigResponseMapper alertGroupConfigModelMapper =
      AlertGroupConfigResponseMapper.INSTANCE;
  private final AlertGroupConfigEntityMapper alertGroupConfigEntityMapper =
      AlertGroupConfigEntityMapper.INSTANCE;
  @Autowired
  private AlertGroupConfigRepository alertGroupConfigRepository;
  @Autowired
  private AlertGroupConfigConditionService alertGroupConfigConditionService;
  @Autowired
  private ServiceService serviceService;
  @Autowired
  private ApplicationService applicationService;
  @Autowired
  private AlertGroupConfigDependencyService alertGroupConfigDependencyService;
  @Autowired
  private SysLogKafkaProducerService sysLogKafkaProducerService;
  @Lazy
  @Autowired
  private AlertPriorityConfigService alertPriorityConfigService;
  @Lazy
  @Autowired
  private CustomObjectService customObjectService;

  @Override
  protected BaseSoftRepository<AlertGroupConfigEntity, Long> getRepository() {
    return alertGroupConfigRepository;
  }

  @Override
  public List<AlertGroupConfigEntity> findAllWithDeletedAndSearch(Boolean withDeleted,
                                                                  String search) {
    if (withDeleted) {
      return alertGroupConfigRepository.findAllByDeletedAndSearch(null, search);
    }
    return alertGroupConfigRepository.findAllByDeletedAndSearch(false, search);
  }

  @Override
  @Transactional
  public AlertGroupConfigEntity save(AlertGroupConfigRequest request) throws BusinessException {
    var isCreateMode = Objects.isNull(request.getId());
    List<CustomObjectEntity> customObjects = AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE.equals(request.getType())
        && CollectionUtils.isNotEmpty(request.getCustomObjectIds())
        ? customObjectService.findAllById(request.getCustomObjectIds()) : List.of();
    validateAlertGroupRequest(request, customObjects);
    AlertGroupConfigEntity alertGroupConfigEntity;
    var formatName = StringUtils.capitalizeFirstLetter(request.getName());
    var logName = formatName;
    if (isCreateMode) {
      alertGroupConfigEntity = alertGroupConfigEntityMapper.map(request);
      alertGroupConfigEntity.setActive(true);
      alertGroupConfigEntity.setPosition(alertGroupConfigRepository.getNextPositionValue());
    } else {
      var entity =
          alertGroupConfigRepository.findById(request.getId())
              .orElseThrow(() -> new BusinessException(
                  ErrorCode.ALERT_GROUP_CONFIG_NOT_FOUND));
      logName = entity.getName();
      var oldDependencies =
          alertGroupConfigDependencyService.findAllByAlertGroupConfigId(request.getId());

      // Create new Config when condition update, delete old config.
      // when name and description update just update old config
      if (isNeedToCreateNewConfig(request, entity, oldDependencies)) {
        alertGroupConfigEntity = alertGroupConfigEntityMapper.map(request);
        alertGroupConfigEntity.setId(null);
        alertGroupConfigEntity.setActive(entity.getActive());
        alertGroupConfigEntity.setPosition(entity.getPosition());
        delete(entity);
      } else {
        alertGroupConfigEntity = entity;
        alertGroupConfigEntity.setDescription(request.getDescription());
        alertGroupConfigConditionService.deleteAllByAlertGroupConfigId(request.getId());
        alertGroupConfigDependencyService.deleteAllByAlertGroupConfigId(request.getId());
      }
    }
    alertGroupConfigEntity.setName(formatName);
    save(alertGroupConfigEntity);
    var services = serviceService.findAllByIdInAndDeleted(request.getServiceIds(), false);
    if (KanbanCommonUtil.isEmpty(services)) {
      throw new BusinessException(ErrorCode.SERVICE_HAS_BEEN_DELETED);
    }
    var applications = applicationService.findAllByIdInAndServiceIdInAndDeleted(
        request.getApplicationIds(),
        services.stream().map(ServiceEntity::getId).toList(),
        false);
    var applicationMap = applications.stream().collect(Collectors.groupingBy(ApplicationEntity::getServiceId));
    var dependencyServices = new ArrayList<ServiceEntity>();
    var serviceWithAllApplications = new ArrayList<ServiceEntity>();
    for (ServiceEntity service : services) {
      if (applicationMap.containsKey(service.getId())) {
        dependencyServices.add(service);
      } else {
        serviceWithAllApplications.add(service);
      }
    }
    var newDependencies = new ArrayList<AlertGroupConfigDependencyEntity>();
    dependencyServices.forEach(service -> {
      var dependency = new AlertGroupConfigDependencyEntity();
      dependency.setAlertGroupConfigId(alertGroupConfigEntity.getId());
      dependency.setDependencyId(service.getId());
      dependency.setType(AlertGroupConfigDependencyTypeEnum.SERVICE);
      newDependencies.add(dependency);
    });
    applications.forEach(application -> {
      var dependency = new AlertGroupConfigDependencyEntity();
      dependency.setAlertGroupConfigId(alertGroupConfigEntity.getId());
      dependency.setDependencyId(application.getId());
      dependency.setType(AlertGroupConfigDependencyTypeEnum.APPLICATION);
      newDependencies.add(dependency);
    });
    serviceWithAllApplications.forEach(service -> {
      var dependency = new AlertGroupConfigDependencyEntity();
      dependency.setAlertGroupConfigId(alertGroupConfigEntity.getId());
      dependency.setDependencyId(service.getId());
      dependency.setType(AlertGroupConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);
      newDependencies.add(dependency);
    });
    alertGroupConfigDependencyService.saveAll(newDependencies);
    var alertGroupConfigConditions =
        alertGroupConfigConditionEntityMapper.map(request, alertGroupConfigEntity, customObjects);
    if (CollectionUtils.isNotEmpty(alertGroupConfigConditions)) {
      alertGroupConfigConditionService.saveAll(alertGroupConfigConditions);
    }
    var isCustomOutput = AlertGroupOutputEnum.CUSTOM.equals(alertGroupConfigEntity.getAlertOutput());

    ServiceEntity customService = null;
    ApplicationEntity customApplication = null;
    AlertPriorityConfigEntity customPriorityConfig = null;
    if (isCustomOutput) {
      customService = serviceService.findById(request.getCustomServiceId());
      customApplication = applicationService.findById(request.getCustomApplicationId());
      customPriorityConfig = alertPriorityConfigService.findById(request.getCustomPriorityConfigId());
    }
    // Send log
    sysLogKafkaProducerService.send(
        isCreateMode ? LogActionEnum.CREATE_ALERT_GROUP_CONFIG : LogActionEnum.EDIT_ALERT_GROUP_CONFIG,
        logName,
        alertGroupConfigLogModelMapper.map(alertGroupConfigEntity, services, serviceWithAllApplications, applications,
            alertGroupConfigConditions,
            customService,
            customApplication,
            customPriorityConfig,
            customObjects));
    return alertGroupConfigEntity;
  }

  public AlertGroupConfigResponse findByIdWithDetail(Long id) throws BusinessException {
    var alertGroupConfig = alertGroupConfigRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.ALERT_GROUP_CONFIG_NOT_FOUND));
    var alertGroupConditions = alertGroupConfigConditionService.findAllByAlertGroupConfigId(id);
    var alertGroupConfigDependencies =
        alertGroupConfigDependencyService.findAllByAlertGroupConfigId(id);

    var serviceIds = new ArrayList<String>();
    var applicationIds = new ArrayList<String>();
    for (AlertGroupConfigDependencyEntity dependency : alertGroupConfigDependencies) {
      var type = dependency.getType();
      if (AlertGroupConfigDependencyTypeEnum.SERVICE.equals(type)
          || AlertGroupConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION.equals(type)) {
        serviceIds.add(dependency.getDependencyId());
      }
      if (AlertGroupConfigDependencyTypeEnum.APPLICATION.equals(type)) {
        applicationIds.add(dependency.getDependencyId());
      }
    }
    if (AlertGroupOutputEnum.CUSTOM.equals(alertGroupConfig.getAlertOutput())) {
      if (Objects.nonNull(alertGroupConfig.getCustomServiceId())) {
        serviceIds.add(alertGroupConfig.getCustomServiceId());
      }
      if (Objects.nonNull(alertGroupConfig.getCustomApplicationId())) {
        applicationIds.add(alertGroupConfig.getCustomApplicationId());
      }
    }
    List<ServiceEntity> services =
        CollectionUtils.isNotEmpty(serviceIds) ? serviceService.findAllByIdIn(serviceIds) :
            new ArrayList<>();
    List<ApplicationResponse> applications =
        CollectionUtils.isNotEmpty(applicationIds)
            ? applicationService.findAllByIdIn(applicationIds) :
            new ArrayList<>();
    return alertGroupConfigModelMapper.map(alertGroupConfig, alertGroupConditions, services,
        applications, alertGroupConfigDependencies);
  }

  @Override
  public List<AlertGroupConfigEntity> findAllByServiceIdAndApplicationIdAndDeletedAndActive(
      String serviceId,
      String applicationId,
      Boolean deleted,
      Boolean active) {
    return alertGroupConfigRepository.findAllByServiceIdAndApplicationIdAndDeletedAndActive(
        serviceId, applicationId,
        deleted, active);
  }

  @Override
  public AlertGroupConfigEntity updateActive(Long id, Boolean active) throws BusinessException {
    var alertGroupConfig = alertGroupConfigRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.ALERT_GROUP_CONFIG_NOT_FOUND));
    alertGroupConfig.setActive(active);
    var res = alertGroupConfigRepository.save(alertGroupConfig);
    sysLogKafkaProducerService.send(
        active ? LogActionEnum.ACTIVE_ALERT_GROUP_CONFIG : LogActionEnum.INACTIVE_ALERT_GROUP_CONFIG, res.getName());
    return res;
  }

  @Override
  public List<AlertGroupConfigEntity> findAllByDeletedAndActive(Boolean deleted, Boolean active) {
    return alertGroupConfigRepository.findAllByDeletedAndActive(deleted, active);
  }

  @Override
  public List<AlertGroupConfigEntity> updatePosition(
      AlertGroupConfigUpdatePositionRequest request)
      throws BusinessException {
    var fromId = request.getAlertGroupConfigFromId();
    var toId = request.getAlertGroupConfigToId();
    var fromPosition = request.getFromPosition();
    var toPosition = request.getToPosition();

    // validate request
    if (Objects.equals(fromPosition, toPosition) || Objects.equals(fromId, toId)) {
      throw new BusinessException(ErrorCode.INVALID_UPDATE_POSITION_REQUEST);
    }


    var lowerPriority = fromPosition > toPosition ? toPosition : fromPosition;
    var greaterPriority = fromPosition > toPosition ? fromPosition : toPosition;
    // find all config in range (fromPosition -> toPosition) to update
    var entities = alertGroupConfigRepository
        .findAllByPositionBetween(lowerPriority, greaterPriority);

    if (CollectionUtils.isEmpty(entities) || entities.size() < 2) {
      throw new BusinessException(ErrorCode.INVALID_UPDATE_POSITION_REQUEST);
    }

    var fromEntity = entities.stream()
        .filter(entity -> entity.getId().equals(fromId))
        .findFirst()
        .orElseThrow(() -> new BusinessException(ErrorCode.INVALID_UPDATE_POSITION_REQUEST));
    var toEntity = entities.stream()
        .filter(entity -> entity.getId().equals(toId))
        .findFirst()
        .orElseThrow(() -> new BusinessException(ErrorCode.INVALID_UPDATE_POSITION_REQUEST));

    // check if fromId and toId valid with request
    if (!Objects.equals(fromPosition, fromEntity.getPosition())
        || !Objects.equals(toPosition, toEntity.getPosition())) {
      throw new BusinessException(ErrorCode.INVALID_UPDATE_POSITION_REQUEST);
    }

    // move item 1 -> 10 update position of item (2...10) -> (1 ->9)
    // move item 10 -> 1 update position of item (1 -> 9) -> (2 -> 10)
    var step = fromPosition > toPosition ? 1 : -1;
    for (var entity : entities) {
      entity.setPosition(entity.getPosition() + step);
    }
    fromEntity.setPosition(toPosition);
    var oldRealPosition = alertGroupConfigRepository.findRealPositionById(fromId);
    var res = alertGroupConfigRepository.saveAll(entities);
    var newRealPosition = alertGroupConfigRepository.findRealPositionById(fromId);
    sysLogKafkaProducerService.send(LogActionEnum.CHANGE_ORDER_OF_ALERT_GROUP_CONFIG,
        fromEntity.getName(), oldRealPosition, newRealPosition);
    return res;
  }

  protected boolean isNeedToCreateNewConfig(AlertGroupConfigRequest request,
                                            AlertGroupConfigEntity entity,
                                            List<AlertGroupConfigDependencyEntity> dependencies) {
    if (!Objects.equals(request.getType(), entity.getType())
        || !Objects.equals(request.getAlertOutput(), entity.getAlertOutput())
        || !Objects.equals(request.getCustomServiceId(), entity.getCustomServiceId())
        || !Objects.equals(request.getCustomApplicationId(), entity.getCustomApplicationId())
        || !Objects.equals(request.getCustomRecipient(), entity.getCustomRecipient())
        || !Objects.equals(request.getCustomContent(), entity.getCustomContent())
        || !Objects.equals(request.getCustomPriorityConfigId(), entity.getCustomPriorityConfigId())) {
      return true;
    }

    var serviceDependencyIds =
        dependencies.stream()
            .filter(ele -> AlertGroupConfigDependencyTypeEnum.SERVICE.equals(ele.getType())
                || AlertGroupConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION.equals(ele.getType()))
            .map(AlertGroupConfigDependencyEntity::getDependencyId).toList();

    if (request.getServiceIds().size() != serviceDependencyIds.size()
        || !new HashSet<>(request.getServiceIds()).containsAll(serviceDependencyIds)) {
      return true;
    }
    var applicationDependencyIds =
        dependencies.stream()
            .filter(ele -> AlertGroupConfigDependencyTypeEnum.APPLICATION.equals(ele.getType()))
            .map(AlertGroupConfigDependencyEntity::getDependencyId).toList();
    if (request.getApplicationIds().size() != applicationDependencyIds.size()
        || !new HashSet<>(request.getApplicationIds()).containsAll(applicationDependencyIds)) {
      return true;
    }
    var alertGroupConfigConditions =
        alertGroupConfigConditionService.findAllByAlertGroupConfigId(entity.getId());
    if (AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE.equals(request.getType())) {
      if (alertGroupConfigConditions.size() != request.getCustomObjectIds().size()) {
        return true;
      }
      var customObjectIdSet = alertGroupConfigConditions
          .stream()
          .map(AlertGroupConfigConditionEntity::getCustomObjectId)
          .collect(Collectors.toSet());
      return !IterableUtils.matchesAll(request.getCustomObjectIds(), customObjectIdSet::contains);
    }
    //    MULTIPLE_CONDITION
    if (alertGroupConfigConditions.size() != request.getRuleGroups().size()) {
      return true;
    }
    var ruleSet = alertGroupConfigConditions
        .stream()
        .map(AlertGroupConfigConditionEntity::getRuleGroup)
        .collect(Collectors.toSet());
    return !IterableUtils.matchesAll(request.getRuleGroups(),
        ruleSet::contains);
  }

  protected void validateAlertGroupRequest(AlertGroupConfigRequest request, List<CustomObjectEntity> customObjects)
      throws BusinessException {
    var type = request.getType();
    var ruleGroups = request.getRuleGroups();
    var isUpdateMode = Objects.nonNull(request.getId());
    var formatName = StringUtils.capitalizeFirstLetter(request.getName());
    var isExist = isUpdateMode ? alertGroupConfigRepository
        .existsByIdNotAndNameAndDeleted(request.getId(), formatName, false)
        : alertGroupConfigRepository.existsByNameAndDeleted(formatName, false);
    if (isExist) {
      throw new BusinessException(ErrorCode.ALERT_GROUP_CONFIG_NAME_IS_EXISTED);
    }
    if (AlertGroupConfigTypeEnum.MULTIPLE_CONDITION.equals(type)
        && CollectionUtils.isEmpty(ruleGroups)) {
      throw new BusinessException(ErrorCode.ALERT_GROUP_CONFIG_RULE_CAN_NOT_EMPTY);
    }
    if (AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE.equals(type) && CollectionUtils.isEmpty(customObjects)) {
      throw new BusinessException(ErrorCode.ALERT_GROUP_CONFIG_CUSTOM_OBJECT_CAN_NOT_EMPTY);
    }
    if (AlertGroupOutputEnum.CUSTOM.equals(request.getAlertOutput())) {
      if (KanbanCommonUtil.isEmpty(request.getCustomServiceId())
          || KanbanCommonUtil.isEmpty(request.getCustomApplicationId())
          || KanbanCommonUtil.isEmpty(request.getCustomContent())
          || KanbanCommonUtil.isEmpty(request.getCustomRecipient())
          || Objects.isNull(request.getCustomPriorityConfigId())) {
        throw new BusinessException(
            ErrorCode.ALERT_GROUP_CONFIG_CUSTOM_ALERT_OUTPUT_INFO_CAN_NOT_EMPTY);
      }

      var service = serviceService.findById(request.getCustomServiceId());
      if (Objects.isNull(service) || service.getDeleted()) {
        throw new BusinessException(ErrorCode.ALERT_GROUP_CONFIG_ALERT_OUTPUT_CUSTOM_SERVICE_INVALID);
      }
      var application = applicationService.findById(request.getCustomApplicationId());
      if (Objects.isNull(application) || application.getDeleted()) {
        throw new BusinessException(ErrorCode.ALERT_GROUP_CONFIG_ALERT_OUTPUT_CUSTOM_APPLICATION_INVALID);
      }
      var priority = alertPriorityConfigService.findById(request.getCustomPriorityConfigId());
      if (Objects.isNull(priority) || priority.getDeleted()) {
        throw new BusinessException(ErrorCode.ALERT_GROUP_CONFIG_ALERT_OUTPUT_CUSTOM_PRIORITY_INVALID);
      }
    }
  }

  @Override
  public List<String> findAllByPriorityConfigId(Long id) {
    Set<String> alertGroupConfigNamesSet = new HashSet<>();
    var configs = alertGroupConfigRepository.findAllByNotDeletedAndCustomObjectNull();
    for (AlertGroupConditionDependenciesModel config : configs) {
      RuleGroupType rules =
          RuleGroupConverter.convertToRuleGroup(
              config.getRules());
      if (!KanbanCommonUtil.isEmpty(rules) && rules.checkPriority(id)) {
        alertGroupConfigNamesSet.add(config.getName());
      }
    }
    return new ArrayList<>(alertGroupConfigNamesSet);
  }

  @Override
  public List<String> findAllByCustomObjectId(Long id) {
    Set<String> alertGroupConfigNamesSet = new HashSet<>();
    var configs = alertGroupConfigRepository.findAllByNotDeleted();
    for (AlertGroupConditionDependenciesModel config : configs) {
      if (KanbanCommonUtil.isEmpty(config.getCustomObjectId())) {
        var rules = RuleGroupConverter.convertToRuleGroup(config.getRules());
        if (!KanbanCommonUtil.isEmpty(rules) && rules.checkCustomObject(id)) {
          alertGroupConfigNamesSet.add(config.getName());
        }
      } else {
        if (Objects.equals(config.getCustomObjectId(), id)) {
          alertGroupConfigNamesSet.add(config.getName());
        }
      }
    }
    return new ArrayList<>(alertGroupConfigNamesSet);
  }

  @Override
  public void deleteWithId(Long id) throws BusinessException {
    var alertGroupConfig = alertGroupConfigRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.ALERT_GROUP_CONFIG_NOT_FOUND));
    delete(alertGroupConfig);
    sysLogKafkaProducerService.send(LogActionEnum.DELETE_ALERT_GROUP_CONFIG, alertGroupConfig.getName());
  }

  @Override
  public List<String> findDependencyNameByDependencyId(String dependencyId,
                                                       List<AlertGroupConfigDependencyTypeEnum> type) {
    return alertGroupConfigRepository.findDependencyNameByDependencyId(dependencyId, type);
  }
}
