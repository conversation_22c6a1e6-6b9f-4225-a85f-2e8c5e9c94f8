package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import java.lang.reflect.UndeclaredThrowableException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorActionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorWebConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ActionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MonitorTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.JobKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.server.annotations.TestForUser;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.ActionModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.MonitorWebConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.MonitorWebConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.MonitorActionRepository;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.MonitorWebConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;
import vn.com.mbbank.kanban.test.ApplicationTest;

@ExtendWith(MockitoExtension.class)
class MonitorWebConfigServiceImplTest extends ApplicationTest {

    @Mock
    private MonitorWebConfigRepository monitorWebConfigRepository;
    
    @Mock
    private MonitorActionRepository monitorActionRepository;
    
    @Mock
    private JobKafkaProducerService jobKafkaProducerService;
    
    @Mock
    private SysLogKafkaProducerService sysLogKafkaProducerService;
    
    @Mock
    ServiceService serviceService;
    
    @Mock
    ApplicationService applicationService;
    
    @Mock
    AlertPriorityConfigService alertPriorityConfigService;
    
    @InjectMocks
    private MonitorWebConfigServiceImpl monitorWebConfigService;

    @Test
    void getRepository_success() {
        var result = monitorWebConfigService.getRepository();
        assertEquals(monitorWebConfigRepository, result);
    }
    
    @TestForUser
    void existByName_success() {
        when(monitorWebConfigRepository.existsByNameIgnoreCase(any())).thenReturn(true);
        var res = monitorWebConfigService.existByName("1");
        verify(monitorWebConfigRepository, times(1)).existsByNameIgnoreCase(any());
        assertTrue(res);
    }
    
    @TestForUser
    void existByName_success_returnsFalse() {
        when(monitorWebConfigRepository.existsByNameIgnoreCase(any())).thenReturn(false);
        var res = monitorWebConfigService.existByName("1");
        verify(monitorWebConfigRepository, times(1)).existsByNameIgnoreCase(any());
        assertFalse(res);
    }
    
    @TestForUser
    void existByIdNotAndName_success() {
        when(monitorWebConfigRepository.existsByIdNotAndNameIgnoreCase(any(), any())).thenReturn(true);
        var res = monitorWebConfigService.existByIdNotAndName("1L", "1");
        verify(monitorWebConfigRepository, times(1)).existsByIdNotAndNameIgnoreCase(any(), any());
        assertTrue(res);
    }
    
    @TestForUser
    void existByIdNotAndName_success_returnsFalse() {
        when(monitorWebConfigRepository.existsByIdNotAndNameIgnoreCase(any(), any())).thenReturn(
          false);
        var res = monitorWebConfigService.existByIdNotAndName("1L", "1");
        verify(monitorWebConfigRepository, times(1)).existsByIdNotAndNameIgnoreCase(any(), any());
        assertFalse(res);
    }
    
    @TestForUser
    void validateSaveCustomObjectRequest_error_nameOfConfigIsExists() {
        MonitorWebConfigRequest request = createValidRequest();
        when(monitorWebConfigRepository.existsByNameIgnoreCase(any())).thenReturn(true);
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_NAME_IS_EXISTED.getCode(), exception.getCode());
    }
    
    @Test
    void findAll_success() {
        when(monitorWebConfigRepository.findAll(any(PaginationRequestDTO.class)))
            .thenReturn(Page.empty());
        var res = monitorWebConfigService.findAll(new PaginationRequestDTO());
        assertNotNull(res);
    }

    @Test
    void createOrUpdate_success_createMode() throws BusinessException {
        MonitorWebConfigRequest request = createValidRequest();
        request.setId(null);
        
        var saveRes = new MonitorWebConfigEntity();
        saveRes.setName("TestConfig");
        
        when(monitorWebConfigRepository.save(any())).thenReturn(saveRes);
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
        
        var res = monitorWebConfigService.createOrUpdate(request);
        assertNotNull(res);
        
        verify(monitorWebConfigRepository, times(1)).save(any(MonitorWebConfigEntity.class));
        verify(sysLogKafkaProducerService, times(1)).send(
          eq(LogActionEnum.CREATE_MONITOR_WEB_CONFIG),
          eq("TestConfig"),
          eq(res)
        );
        
        verify(jobKafkaProducerService).notifyJobUpdate(eq(KafkaTypeEnum.MONITOR_WEB_CONFIG), any());
    }

    @Test
    void createOrUpdate_success_updateMode() throws BusinessException {
        MonitorWebConfigRequest request = createValidRequest();
        request.setId("existing-id");
        request.setMonitorType(MonitorTypeEnum.DISCOVER);
        request.setActive(false);
        
        when(monitorWebConfigRepository.findById(anyString()))
            .thenReturn(Optional.of(new MonitorWebConfigEntity()));
        
        var saveRes = new MonitorWebConfigEntity();
        saveRes.setName("TestConfig");
        
        when(monitorWebConfigRepository.save(any())).thenReturn(saveRes);
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
        
        var res = monitorWebConfigService.createOrUpdate(request);
        assertNotNull(res);
        
        verify(monitorWebConfigRepository, times(1)).save(any(MonitorWebConfigEntity.class));
        verify(sysLogKafkaProducerService, times(1)).send(
          eq(LogActionEnum.EDIT_MONITOR_WEB_CONFIG),
          eq("TestConfig"),
          any(MonitorWebConfigResponse.class)
        );
        
        verify(jobKafkaProducerService).notifyJobUpdate(eq(KafkaTypeEnum.MONITOR_WEB_CONFIG), any());
    }

    
    @Test
    void createOrUpdate_failed_invalidMonth() {
        MonitorWebConfigRequest request = createValidRequest();
        request.setMonths(new Integer[]{13}); // Invalid month
        
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_INTERVAL_INVALID.getCode(), exception.getCode());
    }
    
    @Test
    void createOrUpdate_failed_invalidDom() {
        MonitorWebConfigRequest request = createValidRequest();
        request.setDayOfMonths(new Integer[]{99}); // Invalid day of the month
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_INTERVAL_INVALID.getCode(), exception.getCode());
    }
    
    @Test
    void createOrUpdate_failed_invalidDow() {
        MonitorWebConfigRequest request = createValidRequest();
        request.setDayOfWeeks(new Integer[]{99}); // Invalid day of the week
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_INTERVAL_INVALID.getCode(), exception.getCode());
    }
    
    @Test
    void createOrUpdate_failed_invalidHour() {
        MonitorWebConfigRequest request = createValidRequest();
        request.setHours(new Integer[]{99}); // Invalid hour
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_INTERVAL_INVALID.getCode(), exception.getCode());
    }
    
    @Test
    void createOrUpdate_failed_invalidMinute() {
        MonitorWebConfigRequest request = createValidRequest();
        request.setMinutes(new Integer[]{99}); // Invalid hour
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_INTERVAL_INVALID.getCode(), exception.getCode());
    }
    
    @Test
    void createOrUpdate_failed_invalidAction() {
        MonitorWebConfigRequest request = createValidRequest();
        request.setActions(null);
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_ACTION_INVALID.getCode(), exception.getCode());
    }
    
    @Test
    void createOrUpdate_failed_invalidAuthActions() {
        MonitorWebConfigRequest request = createValidRequest();
        request.setAuthActions(null);
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_AUTH_ACTION_INVALID.getCode(), exception.getCode());
    }
    
    @Test
    void createOrUpdate_failed_invalidUsername() {
        MonitorWebConfigRequest request = createValidRequest();
        request.setAuthActions(Arrays.asList(
          createAuthAction(ActionTypeEnum.SEND_KEY, null),
          createAuthAction(ActionTypeEnum.SEND_KEY, "password"),
          createAuthAction(ActionTypeEnum.CLICK, "login")
        ));
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_USERNAME_INVALID.getCode(), exception.getCode());
    }
    
    @Test
    void createOrUpdate_failed_invalidPassword() {
        MonitorWebConfigRequest request = createValidRequest();
        request.setAuthActions(Arrays.asList(
          createAuthAction(ActionTypeEnum.SEND_KEY, "username"),
          createAuthAction(ActionTypeEnum.SEND_KEY, null),
          createAuthAction(ActionTypeEnum.CLICK, "login")
        ));
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_PASSWORD_INVALID.getCode(), exception.getCode());
    }
    
    @Test
    void createOrUpdate_failed_invalidService() {
        MonitorWebConfigRequest request = createValidRequest();
        when(serviceService.findById(anyString())).thenReturn(null);
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_SERVICE_INVALID.getCode(), exception.getCode());
    }
    
    @Test
    void createOrUpdate_failed_invalidApplication() {
        MonitorWebConfigRequest request = createValidRequest();
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(null);
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_APPLICATION_INVALID.getCode(), exception.getCode());
    }
    
    @Test
    void createOrUpdate_failed_invalidPriority() {
        MonitorWebConfigRequest request = createValidRequest();
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(null);
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_PRIORITY_INVALID.getCode(), exception.getCode());
    }
    
    @Test
    void createOrUpdate_failed_invalidLogin() {
        MonitorWebConfigRequest request = createValidRequest();
        request.setAuthActions(Arrays.asList(
          createAuthAction(ActionTypeEnum.SEND_KEY, "username"),
          createAuthAction(ActionTypeEnum.SEND_KEY, "password"),
          createAuthAction(ActionTypeEnum.CLICK, null)
        ));
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_LOGIN_BUTTON_INVALID.getCode(), exception.getCode());
    }
    
    @Test
    void findWithId_success() throws BusinessException {
        MonitorWebConfigEntity config = new MonitorWebConfigEntity();
        config.setActionId("action-id");
        config.setMonitorType(MonitorTypeEnum.LOGIN);
        config.setAuthActionId("auth-action-id");
        config.setApplicationId("application-id");
        config.setServiceId("service-id");
        
        MonitorWebConfigResponse appNameAndServiceName = new MonitorWebConfigResponse();
        appNameAndServiceName.setServiceName("service-name");
        appNameAndServiceName.setApplicationName("app-name");
        
        when(monitorWebConfigRepository.findById(anyString())).thenReturn(Optional.of(config));
        when(monitorActionRepository.findByActionIdOrderByOrdersAsc(anyString()))
            .thenReturn(Collections.singletonList(new MonitorActionEntity()));
        when(monitorWebConfigRepository.findAppNameAndServiceNameWithId(any())).thenReturn(appNameAndServiceName);
        
        var res = monitorWebConfigService.findWithId("test-id");
        assertNotNull(res);
        assertNotNull(res.getActions());
        assertNotNull(res.getAuthActions());
    }
    
    @Test
    void findWithId_failed_actionNotFound() {
        MonitorWebConfigEntity config = new MonitorWebConfigEntity();
        config.setActionId(null);
        
        when(monitorWebConfigRepository.findById(anyString())).thenReturn(Optional.of(config));
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.findWithId("test-id"));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_FOUND.getCode(), exception.getCode());
    }
    
    @Test
    void findWithId_failed_monitorActionsIsEmpty() {
        MonitorWebConfigEntity config = new MonitorWebConfigEntity();
        config.setActionId("action-id");
        
        when(monitorWebConfigRepository.findById(anyString())).thenReturn(Optional.of(config));
        when(monitorActionRepository.findByActionIdOrderByOrdersAsc(anyString()))
          .thenReturn(List.of());
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.findWithId("test-id"));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_FOUND.getCode(), exception.getCode());
    }

    @Test
    void findWithId_failed_authActionsIsEmpty() {
        MonitorWebConfigEntity config = new MonitorWebConfigEntity();
        config.setActionId("action-id");
        config.setAuthActionId("auth-action-id");
        config.setMonitorType(MonitorTypeEnum.LOGIN);
        MonitorWebConfigResponse appNameAndServiceName = new MonitorWebConfigResponse();
        appNameAndServiceName.setServiceName("service-name");
        appNameAndServiceName.setApplicationName("app-name");
        
        when(monitorWebConfigRepository.findById(anyString())).thenReturn(Optional.of(config));
        when(monitorActionRepository.findByActionIdOrderByOrdersAsc(config.getActionId()))
          .thenReturn(Collections.singletonList(new MonitorActionEntity()));
        when(monitorWebConfigRepository.findAppNameAndServiceNameWithId(any())).thenReturn(appNameAndServiceName);
        when(monitorActionRepository.findByActionIdOrderByOrdersAsc(config.getAuthActionId()))
          .thenReturn(List.of());
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.findWithId("test-id"));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_AUTH_ACTION_NOT_FOUND.getCode(), exception.getCode());
    }

    @Test
    void findWithId_failed_notFound() {
        when(monitorWebConfigRepository.findById(anyString())).thenReturn(Optional.empty());
        
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> monitorWebConfigService.findWithId("test-id"));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_NOT_FOUND.getCode(), exception.getCode());
    }

    @Test
    void deleteWithId_success() throws BusinessException {
        MonitorWebConfigEntity config = new MonitorWebConfigEntity();
        config.setActionId("action-id");
        config.setAuthActionId("auth-action-id");
        config.setName("TestConfig");
        
        when(monitorWebConfigRepository.findById(anyString())).thenReturn(Optional.of(config));
        
        monitorWebConfigService.deleteWithId("test-id");
        
        verify(monitorActionRepository).deleteAllByActionId("action-id");
        verify(monitorActionRepository).deleteAllByActionId("auth-action-id");
        verify(monitorWebConfigRepository).delete(config);
        verify(sysLogKafkaProducerService).send(eq(LogActionEnum.DELETE_MONITOR_WEB_CONFIG), eq("TestConfig"));
        verify(jobKafkaProducerService).notifyJobUpdate(eq(KafkaTypeEnum.MONITOR_WEB_CONFIG), any());
    }

    @Test
    void updateStatus_success_activate() throws BusinessException {
        MonitorWebConfigEntity config = new MonitorWebConfigEntity();
        config.setActive(false);
        config.setName("TestConfig");
        
        when(monitorWebConfigRepository.findById(anyString())).thenReturn(Optional.of(config));
        when(monitorWebConfigRepository.save(any())).thenReturn(config);
        
        var res = monitorWebConfigService.updateStatus("test-id");
        assertNotNull(res);
        
        verify(sysLogKafkaProducerService).send(eq(LogActionEnum.ACTIVE_MONITOR_WEB_CONFIG), eq("TestConfig"));
        verify(jobKafkaProducerService).notifyJobUpdate(eq(KafkaTypeEnum.MONITOR_WEB_CONFIG), any());
    }

    @Test
    void updateStatus_success_deactivate() throws BusinessException {
        MonitorWebConfigEntity config = new MonitorWebConfigEntity();
        config.setActive(true);
        config.setName("TestConfig");
        
        when(monitorWebConfigRepository.findById(anyString())).thenReturn(Optional.of(config));
        when(monitorWebConfigRepository.save(any())).thenReturn(config);
        
        var res = monitorWebConfigService.updateStatus("test-id");
        assertNotNull(res);
        
        verify(sysLogKafkaProducerService).send(eq(LogActionEnum.INACTIVE_MONITOR_WEB_CONFIG), eq("TestConfig"));
        verify(jobKafkaProducerService).notifyJobUpdate(eq(KafkaTypeEnum.MONITOR_WEB_CONFIG), any());
    }

    @Test
    void findAllByServiceId_success() {
        when(monitorWebConfigRepository.findAllByServiceId(anyString()))
            .thenReturn(Collections.singletonList(new MonitorWebConfigEntity()));
        
        var res = monitorWebConfigService.findAllByServiceId("test-service-id");
        assertFalse(res.isEmpty());
    }

    @Test
    void findAllByApplicationId_success() {
        when(monitorWebConfigRepository.findAllByApplicationId(anyString()))
            .thenReturn(Collections.singletonList(new MonitorWebConfigEntity()));
        
        var res = monitorWebConfigService.findAllByApplicationId("test-app-id");
        assertFalse(res.isEmpty());
    }

    @Test
    void findAllByPriorityId_success() {
        when(monitorWebConfigRepository.findAllByPriorityId(anyLong()))
            .thenReturn(Collections.singletonList(new MonitorWebConfigEntity()));
        
        var res = monitorWebConfigService.findAllByPriorityId(1L);
        assertFalse(res.isEmpty());
    }

    @Test
    void validateInterval_success_validValues() {
        Integer[] validValues = {1, 2, 3};
        boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(
          monitorWebConfigService, "validateInterval", validValues, 1, 12, "INTERVAL_MONTH"));
        assertFalse(result);
    }
    
    @Test
    void validateInterval_failed_invalidValues() {
        Integer[] invalid = {0, 13};
        
        UndeclaredThrowableException wrapper = assertThrows(UndeclaredThrowableException.class, () ->
          ReflectionTestUtils.invokeMethod(
            monitorWebConfigService,
            "validateInterval",
            invalid,
            1,
            12,
            "months"
          )
        );
        
        Throwable cause = wrapper.getCause();
        assertInstanceOf(BusinessException.class, cause);
        BusinessException ex = (BusinessException) cause;
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_INTERVAL_INVALID.getCode(), ex.getCode());
    }
  
    @Test
    void normalizeActions_success() {
        List<MonitorActionEntity> actions = new ArrayList<>();
        actions.add(new MonitorActionEntity());
        actions.add(new MonitorActionEntity());
        
        ReflectionTestUtils.invokeMethod(
            monitorWebConfigService, "normalizeActions", actions, "action-id");
        
        assertEquals("action-id", actions.get(0).getActionId());
        assertEquals(1, actions.get(0).getOrders());
        assertEquals(2, actions.get(1).getOrders());
    }
    
    @Test
    void shouldThrowException_whenIdentifierIsMissingForRequiredType() {
        MonitorWebConfigRequest request = createValidRequest();
        List<ActionModel> actions = new ArrayList<>(request.getActions());
        ActionModel invalid = createAuthAction(ActionTypeEnum.CLICK, null); // CLICK requires identifier
        actions.add(invalid);
        request.setActions(actions);
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID.getCode(), exception.getCode());
    }
    
    @Test
    void shouldThrowException_whenGoToUrlValueExceedsMaxLength() {
        MonitorWebConfigRequest request = createValidRequest();
        List<ActionModel> actions = new ArrayList<>(request.getActions());
        String invalidValue =  "a".repeat(CommonConstants.MAX_LENGTH_2000_VALUE + 1);
        ActionModel invalid = createAuthAction(ActionTypeEnum.GO_TO_URL, "identifier-value");
        invalid.setValue(invalidValue);
        actions.add(invalid);
        request.setActions(actions);
        
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID.getCode(), exception.getCode());
    }
    
    @Test
    void shouldThrowException_whenSelectFromDropdownValueExceedsMaxLength() {
        MonitorWebConfigRequest request = createValidRequest();
        List<ActionModel> actions = new ArrayList<>(request.getActions());
        String invalidValue = "a".repeat(CommonConstants.MAX_LENGTH_1000_VALUE  + 1);
        ActionModel invalid = createAuthAction(ActionTypeEnum.SELECT_FROM_DROPDOWN, "identifier-value");
        invalid.setValue(invalidValue);
        actions.add(invalid);
        request.setActions(actions);
        
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID.getCode(), exception.getCode());
    }
    
    @Test
    void shouldThrowException_whenDefaultActionValueExceedsMaxLength() {
        MonitorWebConfigRequest request = createValidRequest();
        List<ActionModel> actions = new ArrayList<>(request.getActions());
        String invalidValue = "a".repeat(CommonConstants.MAX_LENGTH_500_VALUE   + 1);
        ActionModel invalid = createAuthAction(ActionTypeEnum.CLICK, "identifier-value");
        invalid.setValue(invalidValue);
        actions.add(invalid);
        request.setActions(actions);
        
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID.getCode(), exception.getCode());
    }
    
    @Test
    void shouldThrowException_whenValueIsMissingForRequiredValueType() {
        MonitorWebConfigRequest request = createValidRequest();
        List<ActionModel> actions = new ArrayList<>(request.getActions());
        ActionModel invalid = createAuthAction(ActionTypeEnum.SEND_KEY, "input");
        invalid.setValue(null); // SEND_KEY requires value
        actions.add(invalid);
        request.setActions(actions);
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID.getCode(), exception.getCode());
    }
    
    @Test
    void shouldThrowException_whenWaitValueIsNotNumeric() {
        MonitorWebConfigRequest request = createValidRequest();
        List<ActionModel> actions = new ArrayList<>(request.getActions());
        ActionModel invalid = createAuthAction(ActionTypeEnum.WAIT, "wait-element");
        invalid.setValue("abc"); // not a number
        actions.add(invalid);
        request.setActions(actions);
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID.getCode(), exception.getCode());
    }
    
    @Test
    void shouldThrowException_whenWaitValueOutOfRange() {
        MonitorWebConfigRequest request = createValidRequest();
        List<ActionModel> actions = new ArrayList<>(request.getActions());
        ActionModel invalid = createAuthAction(ActionTypeEnum.WAITING_FOR_ELEMENT, "wait-element");
        invalid.setValue("1000"); // out of 1-600 range
        actions.add(invalid);
        request.setActions(actions);
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID.getCode(), exception.getCode());
    }
    
    @Test
    void shouldThrowException_whenNoteOutOfRange() {
        MonitorWebConfigRequest request = createValidRequest();
        List<ActionModel> actions = new ArrayList<>(request.getActions());
        ActionModel invalid = createAuthAction(ActionTypeEnum.WAITING_FOR_ELEMENT, "wait-element");
        String invalidValue = "a".repeat(CommonConstants.MAX_LENGTH_OF_NOTE   + 1);
        invalid.setValue("100");
        invalid.setNote(invalidValue);
        actions.add(invalid);
        request.setActions(actions);
        when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
        when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
        when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());
        
        BusinessException exception = assertThrows(BusinessException.class,
          () -> monitorWebConfigService.createOrUpdate(request));
        
        assertEquals(ErrorCode.MONITOR_WEB_CONFIG_ACTION_NOT_VALID.getCode(), exception.getCode());
    }
    
    private MonitorWebConfigRequest createValidRequest() {
        MonitorWebConfigRequest request = new MonitorWebConfigRequest();
        request.setName("Test Config");
        request.setMonitorType(MonitorTypeEnum.LOGIN);
        request.setServiceId("service-1");
        request.setApplicationId("app-1");
        request.setPriorityId(1L);
        request.setMonths(new Integer[]{1});
        request.setDayOfMonths(new Integer[]{1});
        request.setDayOfWeeks(new Integer[]{1});
        request.setHours(new Integer[]{1});
        request.setMinutes(new Integer[]{1});
        request.setActions(new ArrayList<>(List.of(createAuthAction(ActionTypeEnum.SEND_KEY, "identifier"))));
        request.setAuthActions(Arrays.asList(
            createAuthAction(ActionTypeEnum.SEND_KEY, "username"),
            createAuthAction(ActionTypeEnum.SEND_KEY, "password"),
            createAuthAction(ActionTypeEnum.CLICK, "login")
        ));
        return request;
    }

    private ActionModel createAuthAction(ActionTypeEnum type, String identifier) {
        ActionModel action = new ActionModel();
        action.setActionType(type);
        action.setIdentifier(identifier);
        if (type == ActionTypeEnum.SEND_KEY) {
            action.setValue("test-value");
        }
        action.setNote("Note");
        return action;
    }
}
