package vn.com.mbbank.kanban.mbmonitor.server.dtos.model;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * AlertNoteAmountModel.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlertGroupConditionDependenciesModel {
  String name;
  Long customObjectId;
  String rules;
}
