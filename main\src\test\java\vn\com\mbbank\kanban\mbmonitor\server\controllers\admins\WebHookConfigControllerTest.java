package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WebHookEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.WebHookDto;
import vn.com.mbbank.kanban.mbmonitor.server.services.WebHookService;
import vn.com.mbbank.kanban.test.ApplicationTest;
import vn.com.mbbank.kanban.test.ControllerTest;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 8/28/2024
 */
class WebHookConfigControllerTest extends ControllerTest {
  @Mock
  WebHookService webHookService;
  @InjectMocks
  @Spy
  WebHookConfigController webHookConfigController;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void getAll_success() throws BusinessException {
    verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.WEBHOOK_CONFIG, PermissionActionEnum.VIEW)),
        "getAll", WebHookConfigController.class);
    HttpServletRequest request = mock(HttpServletRequest.class);
    when(request.getParameter("page")).thenReturn("0");
    when(request.getParameter("size")).thenReturn("5");
    when(request.getParameter("search")).thenReturn("sea");
    when(request.getParameter("sortBy")).thenReturn("createDate");
    when(request.getParameter("isReverse")).thenReturn("false");
    Pageable pageable = mock(Pageable.class);
    Page<WebHookEntity> pageMock =
        new PageImpl<>(List.of(new WebHookEntity()), pageable, 1);

    when(webHookService.findAllWithPaging(any())).thenReturn(pageMock);
    var result = webHookConfigController.getAll(request);
    Assertions.assertEquals(200, result.getStatus());

  }

  @Test
  void save_success() throws BusinessException {
    WebHookDto webHookDto = new WebHookDto();
    doNothing().when(webHookConfigController)
        .makeSureCreateOrUpdate(any(), any(), any(), any());
    when(webHookService.saveConfig(any())).thenReturn(new WebHookEntity());
    var result = webHookConfigController.save(webHookDto);
    Assertions.assertEquals(200, result.getStatus());

  }

  @Test
  void deleteById_success() throws BusinessException {
    verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.WEBHOOK_CONFIG, PermissionActionEnum.DELETE)),
        "deleteById", WebHookConfigController.class);
    var result = webHookConfigController.deleteById(-1L);

    Assertions.assertEquals(200, result.getStatus());

  }

  @Test
  void deleteByIdIn_success() throws BusinessException {
    verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.WEBHOOK_CONFIG, PermissionActionEnum.DELETE)),
        "deleteByIdIn", WebHookConfigController.class);
    var result = webHookConfigController.deleteByIdIn(List.of(-1L));

    Assertions.assertEquals(200, result.getStatus());

  }

  @Test
  void findById_success() throws BusinessException {
    verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.WEBHOOK_CONFIG, PermissionActionEnum.VIEW)),
        "findById", WebHookConfigController.class);
    var result = webHookConfigController.findById(-1L);

    Assertions.assertEquals(200, result.getStatus());

  }

  @Test
  void refreshToken_success() throws BusinessException {
    verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.WEBHOOK_CONFIG, PermissionActionEnum.EDIT)),
        "refreshToken", WebHookConfigController.class);
    var result = webHookConfigController.refreshToken(1L);

    Assertions.assertEquals(200, result.getStatus());

  }


}
