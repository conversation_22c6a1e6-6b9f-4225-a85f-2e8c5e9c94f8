package vn.com.mbbank.kanban.mbmonitor.server.services;

import java.util.List;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CollectEmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.CollectEmailConfigModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.CollectEmailConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CollectEmailConfigResponse;

/**
 * interface logic CollectEmailConfigService .
 */
public interface CollectEmailConfigService extends BaseService<CollectEmailConfigEntity, Long> {
  /**
   * find all collect email config by id of service.
   *
   * @param id The id of service .
   * @return The list of CollectEmailConfigEntity objects.
   */

  List<CollectEmailConfigEntity> findAllByServiceId(String id);

  /**
   * find all collect email config by id of application.
   *
   * @param id The id of application.
   * @return The list of CollectEmailConfigEntity objects.
   */

  List<CollectEmailConfigEntity> findAllByApplicationId(String id);

  /**
   * find all collect email config by id of v.
   *
   * @param id The id of alert priority config.
   * @return The list of CollectEmailConfigEntity objects.
   */

  List<CollectEmailConfigEntity> findAllByPriorityConfigId(Long id);

  /**
   * Saves or updates a collect email config0.
   *
   * @param collectEmailConfigRequest The collect email config data to be saved or updated.
   * @return The saved or updated collect email config.
   * @throws BusinessException If an error occurs during the operation.
   */
  CollectEmailConfigResponse createOrUpdate(CollectEmailConfigRequest collectEmailConfigRequest)
      throws BusinessException;

  /**
   * Checks if any CollectEmailConfigEntity record exists with the given name.
   *
   * @param name The name of the view to check for existence.
   * @return true if at least one CollectEmailConfigEntity record with the specified name exists, false otherwise.
   */
  boolean existByName(String name);

  /**
   * Checks if any CollectEmailConfigEntity record exists with the given name and ID.
   *
   * @param name The name of CollectEmailConfigEntity.
   * @param id   The ID of the CollectEmailConfigEntity record.
   * @return true if a CollectEmailConfigEntity record with the specified name and ID exists, false otherwise.
   */
  boolean existByIdNotAndName(Long id, String name);


  /**
   * find collect email config by id.
   *
   * @param id The id to count by.
   * @return The data of CollectEmailConfigModel objects with id of CollectEmailConfig.
   */
  CollectEmailConfigModel findCollectEmailConfigById(Long id) throws Exception;


  /**
   * find all collect email config by paginationRequest.
   *
   * @param paginationRequest The PaginationRequestDTO.
   * @return The page data of CollectEmailConfigModel objects with id of CollectEmailConfig.
   */
  Page<CollectEmailConfigModel> findAll(PaginationRequestDTO paginationRequest);

  /**
   * update status active or inactive.
   *
   * @param id id of  CollectEmailConfig.
   * @return The data of CollectEmailConfigResponse object with id of CollectEmailConfig.
   */
  CollectEmailConfigResponse updateStatus(Long id) throws BusinessException;

  /**
   * find by emailConfigId.
   *
   * @param emailConfigId if of email config.
   * @return list CollectEmailConfigResponse .
   */
  List<CollectEmailConfigResponse> findAllByEmailConfigId(Long emailConfigId);

  /**
   * find by customObjectId.
   *
   * @param customObjectId if of customObject.
   * @return list CollectEmailConfigResponse .
   */
  List<String> findAllByCustomObjectId(Long customObjectId);

  /**
   * delete by emailConfigId.
   *
   * @param emailConfigId configId.
   */
  void deleteWithId(Long emailConfigId) throws BusinessException;

}
