package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CollectEmailConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.server.constants.KanbanRegexContants;
import vn.com.mbbank.kanban.mbmonitor.server.enums.CollectEmailAlertContentTypeEnum;

/**
 * Model request service to create or update collect email config.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CollectEmailConfigRequest {
  Long id;
  @Size(min = 1, message = "Collect Email Config name can not be empty")
  @Size(
      max = CommonConstants.COLLECT_EMAIL_CONFIG_NAME_MAX_LENGTH,
      message = "Collect Email Config name has max {max} character"
  )
  @Pattern(
      regexp = KanbanRegexContants.COLLECT_EMAIL_CONFIG_NAME_PATTERN,
      message = "Collect Email Config name can only contain A-Z, a-z, 0-9, . -  _"
  )
  @NotNull
  String name;
  @Size(
      max = CommonConstants.COLLECT_EMAIL_CONFIG_DESCRIPTION_MAX_LENGTH,
      message = "Collect Email Config description has max {max} character"
  )
  String description;
  @NotNull
  CollectEmailConfigTypeEnum type;
  @Min(value = CommonConstants.COLLECT_EMAIL_CONFIG_ABSENCE_INTERVAL_MIN)
  @Max(value = CommonConstants.COLLECT_EMAIL_CONFIG_ABSENCE_INTERVAL_MAX)
  Long absenceInterval;
  @Min(value = CommonConstants.COLLECT_EMAIL_CONFIG_ABSENCE_INTERVAL_MIN)
  @Max(value = CommonConstants.COLLECT_EMAIL_CONFIG_ABSENCE_INTERVAL_MAX)
  Long alertRepeatInterval;
  @NotNull
  @Min(value = CommonConstants.COLLECT_EMAIL_CONFIG_INTERVAL_MIN)
  Long intervalTime;
  @NotNull
  Long emailConfigId;
  @NotNull
  RuleGroupType ruleGroup;
  @NotNull
  String serviceId;
  @NotNull
  String applicationId;
  @NotNull
  Long priorityConfigId;
  @Size(min = 1, message = "Collect Email Config contact alert can not be empty")
  @Size(
      max = CommonConstants.COLLECT_EMAIL_CONFIG_RECIPIENT_ALERT_MAX_LENGTH,
      message = "Collect Email Config contact alert has max {max} character"
  )
  String recipient;
  CollectEmailAlertContentTypeEnum contentType;
  String content;
  String contentValue;
  boolean active;
}
