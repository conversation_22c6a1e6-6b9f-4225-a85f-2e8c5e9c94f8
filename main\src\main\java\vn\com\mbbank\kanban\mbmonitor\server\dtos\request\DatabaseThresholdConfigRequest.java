package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionOperatorEnum;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;

/**
 * Model request service to create or update collect email config.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DatabaseThresholdConfigRequest {
  String id;
  @Size(min = 1)
  @Size(max = CommonConstants.COLLECT_EMAIL_CONFIG_NAME_MAX_LENGTH)
  String name;

  @Size(max = CommonConstants.COLLECT_EMAIL_CONFIG_DESCRIPTION_MAX_LENGTH)
  String description;
  @NotNull
  Long databaseConnectionId;
  @NotNull
  String sqlCommand;
  @NotNull
  String cronTime;
  @NotNull
  Long conditionValue;
  @NotNull
  ConditionOperatorEnum conditionOperator;
  @NotNull
  String serviceId;
  @NotNull
  String applicationId;
  @NotNull
  Long priorityId;
  @Size(min = 1)
  @Size(max = CommonConstants.COLLECT_EMAIL_CONFIG_RECIPIENT_ALERT_MAX_LENGTH)
  String recipient;
  String content;
  String contentJson;
  boolean active;
}
