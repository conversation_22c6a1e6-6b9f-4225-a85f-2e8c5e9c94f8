package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;

@Entity
@Data
@Table(name = TableName.WORKFLOW_NODE_DEPENDENCY)
@EqualsAndHashCode(callSuper = true)
@KanbanAutoGenerateUlId
public class WorkflowNodeDependencyEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "WORKFLOW_NODE_ID")
  private String workflowNodeId;

  @Column(name = "WORKFLOW_ID")
  private String workflowId;

  @Column(name = "REFERENCE_ID")
  private String referenceId;

  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private WorkflowNodeTypeEnum type;

  @Override
  public String getId() {
    return id;
  }
}
