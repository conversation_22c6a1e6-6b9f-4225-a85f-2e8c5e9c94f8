package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.enums.SortType;
import vn.com.mbbank.kanban.core.utils.KanbanSqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;

import java.util.Collections;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ExecutionRepositoryCustomImplTest {

    @Mock
    SqlQueryUtil sqlQueryUtil;

    @Mock
    private KanbanSqlQueryUtil.SqlQueryModel queryModel; // Mock for the chained call

    @InjectMocks
    private ExecutionRepositoryCustomImpl executionRepositoryCustomImpl;

    @Test
    void findAll_success_withSearchAndSortByNameAsc() {
        // Arrange
        PaginationRequestDTO requestDTO = new PaginationRequestDTO();
        requestDTO.setPage(0);
        requestDTO.setSize(10);
        requestDTO.setSearch("testSearch");
        requestDTO.setSortBy("name");
        requestDTO.setSortOrder(SortType.ASC);

        Page<ExecutionResponse> expectedPage = new PageImpl<>(Collections.emptyList());
        when(sqlQueryUtil.queryModel()).thenReturn(queryModel);
        when(queryModel.queryPaging(anyString(), any(Map.class), eq(ExecutionResponse.class), any(Pageable.class)))
            .thenReturn(expectedPage);

        // Act
        Page<ExecutionResponse> actualPage = executionRepositoryCustomImpl.findAll(requestDTO);

        // Assert
        assertNotNull(actualPage);
        assertSame(expectedPage, actualPage);

        // Verify that queryPaging was called, specific argument matching can be done if critical
        verify(queryModel).queryPaging(anyString(), any(Map.class), eq(ExecutionResponse.class), any(Pageable.class));
    }

    @Test
    void findAll_success_withoutSearchAndDefaultSort() {
        // Arrange
        PaginationRequestDTO requestDTO = new PaginationRequestDTO();
        requestDTO.setPage(1);
        requestDTO.setSize(5);
        // No search, no sort specified

        Page<ExecutionResponse> expectedPage = new PageImpl<>(Collections.singletonList(new ExecutionResponse()));
        when(sqlQueryUtil.queryModel()).thenReturn(queryModel);
        when(queryModel.queryPaging(anyString(), any(Map.class), eq(ExecutionResponse.class), any(Pageable.class)))
            .thenReturn(expectedPage);

        // Act
        Page<ExecutionResponse> actualPage = executionRepositoryCustomImpl.findAll(requestDTO);

        // Assert
        assertNotNull(actualPage);
        assertSame(expectedPage, actualPage);

        // Verify that queryPaging was called
        verify(queryModel).queryPaging(anyString(), any(Map.class), eq(ExecutionResponse.class), any(Pageable.class));
    }
    
    @Test
    void findAll_success_withSortByDescriptionDesc() {
        // Arrange
        PaginationRequestDTO requestDTO = new PaginationRequestDTO();
        requestDTO.setPage(0);
        requestDTO.setSize(10);
        requestDTO.setSortBy("description");
        requestDTO.setSortOrder(SortType.DESC);

        Page<ExecutionResponse> expectedPage = new PageImpl<>(Collections.emptyList());
        when(sqlQueryUtil.queryModel()).thenReturn(queryModel);
        when(queryModel.queryPaging(anyString(), any(Map.class), eq(ExecutionResponse.class), any(Pageable.class)))
            .thenReturn(expectedPage);
        
        // Act
        Page<ExecutionResponse> actualPage = executionRepositoryCustomImpl.findAll(requestDTO);

        // Assert
        assertNotNull(actualPage);
        assertSame(expectedPage, actualPage); 
        // Verify that queryPaging was called
        verify(queryModel).queryPaging(anyString(), any(Map.class), eq(ExecutionResponse.class), any(Pageable.class));
    }

    @Test
    void buildQuerySearchLike_success_withSearchTerm() {
        // Arrange
        String searchTerm = "test";

        // Act
        PrepareQuery resultQuery = executionRepositoryCustomImpl.buildQuerySearchLike(searchTerm);

        // Assert
        assertNotNull(resultQuery);
        String queryString = resultQuery.getQueryBuilder().toString();
        assertTrue(queryString.contains("LOWER(execution.NAME) LIKE :search"));
        assertTrue(queryString.contains("LOWER(execution.DESCRIPTION) LIKE :search"));
        Map<String, Object> params = resultQuery.getParams();
        assertEquals("%test%", params.get("search")); 
    }

    @Test
    void buildQuerySearchLike_success_withBlankSearchTermReturnsNull() {
        // Arrange
        String searchTerm = " ";

        // Act
        PrepareQuery resultQuery = executionRepositoryCustomImpl.buildQuerySearchLike(searchTerm);

        // Assert
        assertNull(resultQuery);
    }

    @Test
    void buildQuerySearchLike_success_withNullSearchTermReturnsNull() {
        // Arrange
        String searchTerm = null;

        // Act
        PrepareQuery resultQuery = executionRepositoryCustomImpl.buildQuerySearchLike(searchTerm);

        // Assert
        assertNull(resultQuery);
    }


    @Test
    void buildOrderQuery_success_defaultSortWhenSortByIsBlank() {
        // Arrange
        PaginationRequestDTO requestDTO = new PaginationRequestDTO();
        requestDTO.setSortBy(""); 
        requestDTO.setSortOrder(SortType.ASC);

        // Act
        PrepareQuery resultQuery = executionRepositoryCustomImpl.buildOrderQuery(requestDTO);

        // Assert
        assertNotNull(resultQuery);
        assertEquals("ORDER BY execution.CREATED_DATE ASC", resultQuery.getQueryBuilder().toString().trim());
    }
    
    @Test
    void buildOrderQuery_success_defaultSortWhenSortByIsNull() {
        // Arrange
        PaginationRequestDTO requestDTO = new PaginationRequestDTO();
        requestDTO.setSortBy(null); 
        requestDTO.setSortOrder(SortType.DESC);

        // Act
        PrepareQuery resultQuery = executionRepositoryCustomImpl.buildOrderQuery(requestDTO);

        // Assert
        assertNotNull(resultQuery);
        assertEquals("ORDER BY execution.CREATED_DATE DESC", resultQuery.getQueryBuilder().toString().trim());
    }


    @Test
    void buildOrderQuery_success_sortByNameAndOrderAsc() {
        // Arrange
        PaginationRequestDTO requestDTO = new PaginationRequestDTO();
        requestDTO.setSortBy("name");
        requestDTO.setSortOrder(SortType.ASC);

        // Act
        PrepareQuery resultQuery = executionRepositoryCustomImpl.buildOrderQuery(requestDTO);

        // Assert
        assertNotNull(resultQuery);
        assertEquals("ORDER BY execution.NAME ASC", resultQuery.getQueryBuilder().toString().trim());
    }

    @Test
    void buildOrderQuery_success_sortByDescriptionAndOrderDesc() {
        // Arrange
        PaginationRequestDTO requestDTO = new PaginationRequestDTO();
        requestDTO.setSortBy("description");
        requestDTO.setSortOrder(SortType.DESC);

        // Act
        PrepareQuery resultQuery = executionRepositoryCustomImpl.buildOrderQuery(requestDTO);

        // Assert
        assertNotNull(resultQuery);
        assertEquals("ORDER BY execution.DESCRIPTION DESC", resultQuery.getQueryBuilder().toString().trim());
    }

    @Test
    void buildOrderQuery_success_sortByUnknownDefaultsToCreatedDateAndOrderAsc() {
        // Arrange
        PaginationRequestDTO requestDTO = new PaginationRequestDTO();
        requestDTO.setSortBy("unknownField");
        requestDTO.setSortOrder(SortType.ASC);

        // Act
        PrepareQuery resultQuery = executionRepositoryCustomImpl.buildOrderQuery(requestDTO);

        // Assert
        assertNotNull(resultQuery);
        assertEquals("ORDER BY execution.CREATED_DATE ASC", resultQuery.getQueryBuilder().toString().trim());
    }

    @Test
    void buildOrderQuery_success_sortByUnknownDefaultsToCreatedDateAndOrderDesc() {
        // Arrange
        PaginationRequestDTO requestDTO = new PaginationRequestDTO();
        requestDTO.setSortBy("unknownField");
        requestDTO.setSortOrder(SortType.DESC);

        // Act
        PrepareQuery resultQuery = executionRepositoryCustomImpl.buildOrderQuery(requestDTO);

        // Assert
        assertNotNull(resultQuery);
        assertEquals("ORDER BY execution.CREATED_DATE DESC", resultQuery.getQueryBuilder().toString().trim());
    }
    
    @Test
    void buildOrderQuery_success_sortByValidAndNullOrderDefaultsToDesc() {
        // Arrange
        PaginationRequestDTO requestDTO = new PaginationRequestDTO();
        requestDTO.setSortBy("name");
        requestDTO.setSortOrder(null); 

        // Act
        PrepareQuery resultQuery = executionRepositoryCustomImpl.buildOrderQuery(requestDTO);

        // Assert
        assertNotNull(resultQuery);
        assertEquals("ORDER BY execution.NAME", resultQuery.getQueryBuilder().toString().trim());
    }
}
