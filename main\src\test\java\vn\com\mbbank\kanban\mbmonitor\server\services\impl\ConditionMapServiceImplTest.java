package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OperatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleCondition;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.CustomObjectService;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class ConditionMapServiceImplTest {

  @Mock
  private CustomObjectService customObjectService;

  @Mock
  private AlertPriorityConfigService alertPriorityConfigService;

  @Mock
  private ObjectMapper objectMapper;

  @InjectMocks
  private ConditionMapServiceImpl conditionMapService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void testMapNameCondition_success_withPriority() {
    RuleCondition<Object> condition = new RuleCondition<>();
    condition.setField("priority");
    condition.setOperator(OperatorEnum.IS);
    condition.setValue("1");

    RuleGroupType group = new RuleGroupType();
    group.setCombinator(ConditionCombinatorEnum.AND);
    group.setRules(List.of(condition));
    AlertPriorityConfigEntity entity = new AlertPriorityConfigEntity();
    entity.setId(1L);
    entity.setName("High");

    when(alertPriorityConfigService.findAllById(Set.of(1L)))
            .thenReturn(List.of(entity));
    when(customObjectService.findAllById(Set.of()))
            .thenReturn(Collections.emptyList());
    when(objectMapper.convertValue(group, RuleGroupType.class)).thenReturn(group);

    String result = conditionMapService.mapNameCondition(group, null, true,  Set.of("abc"));

    assertTrue(result.contains("High"));
  }

  @Test
  void testMapNameCondition_success_withCustomObject() {
    RuleCondition<Object> condition = new RuleCondition<>();
    condition.setField("999");
    condition.setOperator(OperatorEnum.IS);
    condition.setValue("some-value");

    RuleGroupType group = new RuleGroupType();
    group.setCombinator(ConditionCombinatorEnum.AND);
    group.setRules(List.of(condition));
    CustomObjectEntity customObjectEntity = new CustomObjectEntity();
    customObjectEntity.setName("Status");
    customObjectEntity.setId(999L);

    when(customObjectService.findAllById(Set.of(999L)))
            .thenReturn(List.of(customObjectEntity));
    when(objectMapper.convertValue(group, RuleGroupType.class)).thenReturn(group);

    String result = conditionMapService.mapNameCondition(group, null, false, Set.of("abc"));

    assertTrue(result.contains("Status"));
  }

  @Test
  void testMapNameListCondition_success_multipleGroups() {
    RuleCondition<Object> c1 = new RuleCondition<>();
    c1.setField("priority");
    c1.setOperator(OperatorEnum.IS);
    c1.setValue("123");

    RuleGroupType g1 = new RuleGroupType();
    g1.setCombinator(ConditionCombinatorEnum.OR);
    g1.setRules(List.of(c1));

    RuleCondition<Object> c2 = new RuleCondition<>();
    c2.setField("456");
    c2.setOperator(OperatorEnum.IS);
    c2.setValue("abc");

    RuleGroupType g2 = new RuleGroupType();
    g2.setCombinator(ConditionCombinatorEnum.AND);
    g2.setRules(List.of(c2));
    AlertPriorityConfigEntity entity = new AlertPriorityConfigEntity();
    entity.setId(123L);
    entity.setName("Urgent");
    CustomObjectEntity customObjectEntity = new CustomObjectEntity();
    customObjectEntity.setName("Location");
    customObjectEntity.setId(456L);

    when(objectMapper.convertValue(any(), ArgumentMatchers.<TypeReference<List<RuleGroupType>>>any()))
            .thenReturn(List.of(g1, g2));
    when(alertPriorityConfigService.findAllById(Set.of(123L)))
            .thenReturn(List.of(entity));
    when(customObjectService.findAllById(Set.of(456L)))
            .thenReturn(List.of(customObjectEntity));

    List<String> result = conditionMapService.mapNameListCondition(List.of(g1, g2), null, true, Set.of("abc"));

    assertEquals(2, result.size());
    assertTrue(result.get(0).contains("Urgent"));
    assertTrue(result.get(1).contains("Location"));
  }

  @Test
  void testMapNameCondition_success_nullGroup() {
    String result = conditionMapService.mapNameCondition(null, null, false, null);
    assertNull(result);
  }

  @Test
  void testMapNameListCondition_success_emptyList() {
    List<String> result = conditionMapService.mapNameListCondition(Collections.emptyList(), null, false, null);
    assertTrue(result.isEmpty());
  }

  @Test
  void testMapNameCondition_success_withClazzAndFieldMap() {
    RuleCondition<Object> c1 = new RuleCondition<>();
    c1.setField("1001");
    c1.setOperator(OperatorEnum.IS);
    c1.setValue("value");

    RuleGroupType group = new RuleGroupType();
    group.setCombinator(ConditionCombinatorEnum.AND);
    group.setRules(List.of(c1));
    CustomObjectEntity customObjectEntity = new CustomObjectEntity();
    customObjectEntity.setName("CustomField");
    customObjectEntity.setId(1001L);
    when(customObjectService.findAllById(Set.of(1001L)))
            .thenReturn(List.of(customObjectEntity));
    when(objectMapper.convertValue(group, RuleGroupType.class)).thenReturn(group);

    String result = conditionMapService.mapNameCondition(group, null, false, Set.of("abc"));

    assertTrue(result.contains("CustomField"));
  }

  @Test
  void testMapNameListCondition_success_withEmptyEntities() {
    RuleCondition<Object> c1 = new RuleCondition<>();
    c1.setField("priority");
    c1.setOperator(OperatorEnum.IS);
    c1.setValue("123");

    RuleGroupType group = new RuleGroupType();
    group.setCombinator(ConditionCombinatorEnum.AND);
    group.setRules(List.of(c1));

    when(customObjectService.findAllById(anySet())).thenReturn(Collections.emptyList());
    when(alertPriorityConfigService.findAllById(anySet())).thenReturn(Collections.emptyList());

    List<String> result = conditionMapService.mapNameListCondition(List.of(group), null, true, null);

    assertEquals(1, result.size());
    assertTrue(result.get(0).contains("123")); // unchanged
  }
}
