package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailComposedModel;
import vn.com.mbbank.kanban.mbmonitor.server.utils.handler.SendEmailHandler;

/**
 * Controller logic email template .
 *
 * <AUTHOR>
 * @created_date 11/04/2024
 */
@RestController
@RequestMapping(ServerUrl.EMAIL_URL)
@RequiredArgsConstructor
public class EmailController {
  private final SendEmailHandler sendEmailHandler;

  /**
   * Sending email.
   *
   * @param request request
   * @param files   files
   * @return sendEmails
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.SEND_EMAIL, action = PermissionActionEnum.SEND_EMAIL)
  })
  @PostMapping(consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE})
  public ResponseData<String> sendingEmail(@RequestPart @Valid EmailComposedModel request,
                                           @RequestPart(value = "files", required = false)
                                           List<MultipartFile> files)
      throws BusinessException, IOException {
    this.sendEmailHandler.sendEmails(request, files);
    return ResponseUtils.success(HttpStatus.OK.getReasonPhrase());
  }
}
