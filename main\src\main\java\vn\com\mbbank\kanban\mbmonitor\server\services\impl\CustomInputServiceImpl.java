package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.CustomInputRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomInputEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;
import vn.com.mbbank.kanban.mbmonitor.common.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.CustomInputEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.CustomInputRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.CustomInputService;

@Service
@RequiredArgsConstructor
public class CustomInputServiceImpl extends BaseServiceImpl<CustomInputEntity, String>
    implements CustomInputService {
  private final CustomInputRepository customInputRepository;
  private final CustomInputEntityMapper customInputEntityMapper = CustomInputEntityMapper.INSTANCE;

  @Override
  protected JpaCommonRepository<CustomInputEntity, String> getRepository() {
    return customInputRepository;
  }

  @Override
  public CustomInputEntity findWithId(String id) throws BusinessException {
    return customInputRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.CUSTOM_INPUT_NOT_FOUND));
  }

  @Override
  public CustomInputEntity createOrUpdate(CustomInputRequest request) throws BusinessException {
    var id = request.getId();
    var name = StringUtils.capitalizeFirstLetter(request.getName().trim());
    var isCreateMode = KanbanCommonUtil.isEmpty(id);
    var isNameExisted =
        isCreateMode ? customInputRepository.existsByNameIgnoreCase(name) :
            customInputRepository.existsByIdNotAndNameIgnoreCase(request.getId(), name);

    if (isNameExisted) {
      throw new BusinessException(ErrorCode.CUSTOM_INPUT_NAME_IS_EXISTED);
    }
    CustomInputEntity customInput = null;
    if (isCreateMode) {
      customInput = customInputEntityMapper.map(request);
      customInput.setId(GeneratorUtil.generateId());
    } else {
      customInput = customInputRepository.findById(id)
          .orElseThrow(() -> new BusinessException(ErrorCode.CUSTOM_INPUT_NOT_FOUND));
      customInputEntityMapper.merge(customInput, request);
    }
    customInput.setName(name);
    customInput.getConfiguration().setLabel(customInput.getName());
    customInput.getConfiguration().setCustomInputId(customInput.getId());
    return customInputRepository.save(customInput);
  }

  @Override
  public void deleteWithId(String id) throws BusinessException {
    var customInput = customInputRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.CUSTOM_INPUT_NOT_FOUND));
    customInputRepository.delete(customInput);
  }
}
