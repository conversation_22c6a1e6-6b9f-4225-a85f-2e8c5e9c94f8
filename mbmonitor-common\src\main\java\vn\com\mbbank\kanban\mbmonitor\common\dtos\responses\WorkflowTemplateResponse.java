package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.BaseFormBuilderElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowModel;

/**
 * WorkflowTemplateResponse.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WorkflowTemplateResponse {
  String id;
  String name;
  String description;
  List<BaseFormBuilderElementModel> formLayout;
  WorkflowModel workflow;
}
