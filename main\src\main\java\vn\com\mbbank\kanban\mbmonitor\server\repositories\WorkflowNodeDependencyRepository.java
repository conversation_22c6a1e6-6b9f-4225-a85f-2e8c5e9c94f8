package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowNodeDependencyEntity;

/**
 * Repository WorkflowNodeDependencyRepository.
 */
@Repository
public interface WorkflowNodeDependencyRepository
    extends JpaCommonRepository<WorkflowNodeDependencyEntity, String>, WorkflowNodeDependencyRepositoryCustom {

  /**
   * delete node by workflowTemplateId.
   *
   * @param workflowId workflowTemplateId
   */
  void deleteAllByWorkflowId(String workflowId);

  /**
   * find all dependencies by workflowId.
   *
   * @param workflowId workflowId
   * @return list of WorkflowNodeDependencyEntity
   */
  List<WorkflowNodeDependencyEntity> findAllByWorkflowId(String workflowId);


}
