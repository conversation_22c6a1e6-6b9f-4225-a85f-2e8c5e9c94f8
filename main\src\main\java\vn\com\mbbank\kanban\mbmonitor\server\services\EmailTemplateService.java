package vn.com.mbbank.kanban.mbmonitor.server.services;

import java.io.IOException;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailTemplateEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailTemplateModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailTemplatePaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailTemplateRequest;

/**
 * interface logic EmailPartner.
 */
public interface EmailTemplateService extends BaseService<EmailTemplateEntity, Long> {
  /**
   * Finds the email template  response by the given ID.
   *
   * @param id the ID of the email template  to retrieve
   * @return the {@link EmailTemplateModel} corresponding to the given ID
   * @throws BusinessException if the email template  cannot be found or an error occurs
   */
  EmailTemplateModel findEmailTemplateById(Long id) throws BusinessException;

  /**
   * Finds all email template responses with pagination based on the given request.
   *
   * @param request the {@link EmailTemplatePaginationRequest} containing pagination and filter details
   * @return a page of {@link EmailTemplateModel} results
   */
  Page<EmailTemplateModel> findAll(EmailTemplatePaginationRequest request);

  /**
   * Creates or updates an email template  based on the given request.
   *
   * @param request the {@link EmailTemplateRequest} containing email template data for creation or update
   * @param files   the {@link MultipartFile} list File upload
   * @return the updated or newly created {@link EmailTemplateModel}
   * @throws BusinessException if there is an error during creation or update
   */
  EmailTemplateModel createOrUpdate(EmailTemplateRequest request, List<MultipartFile> files)
      throws BusinessException, IOException;

  /**
   * download file of template by id of file storage.
   *
   * @param id template id
   * @return steaming response body of file template.
   * @throws BusinessException if there is an error during creation or update
   */
  StreamingResponseBody download(Long id) throws BusinessException;
}