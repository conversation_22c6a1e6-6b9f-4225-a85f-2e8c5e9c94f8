package vn.com.mbbank.kanban.mbmonitor.server.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionApiEntity;

/**
 * interface logic ExecutionApi.
 */
public interface ExecutionApiService extends BaseService<ExecutionApiEntity, String> {
  /**
   * find api info by executionId.
   *
   * @param executionId for find
   * @return ExecutionApiEntity
   */
  ExecutionApiEntity findApiInfoByExecutionId(String executionId);

  /**
   * delete by executionId.
   *
   * @param executionId for delete
   */
  void deleteApiInfoByExecutionId(String executionId);

  /**
   * find list ExecutionApiEntity by executionIds.
   *
   * @param executionIds to find
   * @return list ExecutionApiEntity
   */
  List<ExecutionApiEntity> findAllByExecutionIdIn(List<String> executionIds);
}
