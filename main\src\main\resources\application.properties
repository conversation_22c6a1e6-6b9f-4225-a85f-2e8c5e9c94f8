server.port=9002
server.servlet.context-path=/api
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
#server.connection-timeout=300000
#Database config ORACLE
logging.level.org.hibernate.SQL=debug
#logging.level.org.hibernate.type.descriptor.sql=trace
#database config
spring.datasource.url=********************************************
spring.datasource.hikari.username=mbmonitor
#spring.datasource.hikari.password=ENC(CACtqTjfP/21k8tdVCBpxIi1IpBz55nt)
spring.datasource.hikari.password=mbmonitor
#datasource.oracle.password=Mb123456
#spring.datasource.hikari.password=ENC(xnVJLFY924oxrGb9bT6mhHsGsR5cYpn1)
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=200
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=HikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000
spring.jackson.serialization.fail-on-empty-beans=false
spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.jdbc.batch_size=600
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
#spring.jpa.properties.hibernate.cache.provider_configuration_file_resource_path=classpath:ehcache.xml
server.tomcat.relaxed-query-chars=|,{,},[,]
# swagger config
springdoc.swagger-ui.enabled=false
springdoc.api-docs.enabled=false
#keycloak
kanban.keycloak.client-id=mbmonitor-server
kanban.keycloak.client-secret=QXI1GnXNGFncak5LZAMSTe5sHvQU6rgn
#kanban.keycloak.client-secret=ENC(B59eeKpVbBbbRuK3CSjSeNls8MUH/sskGNtvEXlWU1B+vfYj69LcKn6V5WLrj7Ny)
kanban.keycloak.introspect-uri=https://keycloak-internal-uat.mbbank.com.vn/auth/realms/internal/protocol/openid-connect/token/introspect
#redis config
kanban.redis.url=**************:6379
kanban.redis.host=**************
kanban.redis.port=6379
kanban.redis.isCluster=false
kanban.redis.password=Kanban@2024
#kanban.redis.password=ENC(NtxbSotd25ifMcWE0aGLzISWt8FEwg8G)
kanban.redis.timeout=1000
kanban.redis.maxTotal=8
kanban.redis.maxIdle=8
kanban.redis.maxWait=1000
kanban.redis.maxAttempts=5
#kafka config
kanban.kafka.bootstrap-server=**************:9093,**************:9093,**************:9093,**************:9093,**************:9093,**************:9093
kanban.kafka.producer.timeout=30000
kanban.kafka.consumer.auto-commit=true
kanban.kafka.consumer.group-id=123
kanban.kafka.producer.max-request-size=1000000
kanban.kafka.topic.jobs=APP_MBMONITOR_JOBS
kanban.kafka.username=user04
kanban.kafka.password=AXzwKvfruEaQNZmW
#kanban.kafka.password=ENC(XZRUNJRcdsbDXxMI248VSGH1xGVfVrWIq4tNCE9h8k0=)
kanban.kafka.key=emailConfigId
kanban.general.white-list.origin=http://localhost:8400,http://localhost:9000,http://localhost:9990,http://localhost:9007,https://mbmonitor.tanzu-uat.mbbank.com.vn
#keycloak centrailized
kanban.authentication.keycloak.centrailized.url=http://**********:8831/auth/realms/ms-core/protocol/openid-connect/token
kanban.authentication.keycloak.centrailized.username=mbmonitor_user
kanban.authentication.keycloak.centrailized.password=fb7bfb60-f5e3-4f8d-b4ad-3f22249b1fa0
kanban.authentication.keycloak.centrailized.public-key=-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3sROnOw4rj3ainkFaVHf05ha3yt4njOxJzljdebkJJckOXj1wag47nGSukVcsytXuhq8QrM6z8KpcSsAXNYNBgM7i7/X+ZxTf/b5cDKDpwiOzcWbMazii5/8ljIII9Z2sBOXQAf3Ev2NIgUlXBOuwhkQ9gFWmni+GvLnQxNVdJ/16ABelL1LCAbnDlX0VrhLnMQUihjf0Npy+jv5vm20DMzpk5rTo01XTTirrUmiaE76qILbwILqZDVwd4RlnGWD37N9qbjYTZ6AQ3s/yCaEgeJ0z4kzqP5G2oXvll9joV5P/Jub7WGRh9alj+VfhYp9MusTzlLJmqb8uvY3w7E67QIDAQAB-----END PUBLIC KEY-----
kanban.authentication.keycloak.centrailized.enable=false

#security local config
kanban.authentication.local.config.privateKey= -----BEGIN PRIVATE KEY-----MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC/PNnx+p8TOgTztyKLN8OG+VSeh+TWblChPR2Vj7qfa64Lc4Z6RMrjMqhmleF1aZ0vmtPJNViQ76v00iteYB+B7umG8WXaxnvs1v27Jj0GMxy0getED5adrZ25R/Y+88lpdSLK9PQk8mxYAv4RuLYP5HIs0lkKJngLmWn6tpj1fEHwgGk0YuxV9HfeOtXo1r0SrWsGLQx8KzT4Bu5WEf8ddD5kxHjKni34qtr9yFK8eW+nBukj3Vl0x1tGvdYly4CpQUSwuaHMaFfCda6La1tYUBCaQQXOdVNVj01DbLMSUqmKGRpKZ6wmwbI7Z4JFXtEo43Rk1nVPaU8lgvfBhG5nAgMBAAECggEAGeR5+H1fvxFX96I6wca8aTCmTbXwkefrGtRU28R+FspUN6YIoH1ioFMM4fbhHoM847kpe6YGu85M5fT0J5m0KmTX/Daq/RgVUgKPOsuj74xJaAHp12k/kfxzzSZP0kOHvbOSZmW2uK8/ZBs48y/4gTuuXyCL1bKnsPUF7bV+DZ6YAIZ/zzIVyhRw7zW1tIF9gm41ZnOd0TWfQoqpRvG7AmFwLVFtOTtLDQH+LUNMIDrginaPy6Zns1QMbuWh4DoURNc5g8BydukrPK5vS1SaLOETVARhcNfy4IJIaHJnQQABH1gTIajugbMofiXB1/GXE/tZoPMj2AMYoW6P2LA9AQKBgQDSRUEo2O4aBqidlrlSVOlTWq9oYsHQnpkvRiIWskLYfyx4IZFJJzbtH5P7NilHroWxs2pmx25w6SM6ihd5LFXPtNNwAlK2gDaNZTU2rxK7IuNsEIxegL/BYmwYwFaecCffs0W7hT3npftRrg/pAEDvmPnTzcJdeNcim22Rjc4cYQKBgQDo0/NEzxyCt1MTETDSVV1BmX7rTuB5d2EAV90J6Kflc5GYrRe5Wf4nBxrIacHHDtPA3/r/KYQVimrbX06Y0H5LKbiLFZd6Vm/3+LoaQd1LbCWCu2nyR42IoKFQITiiBKBcOeIIIVQ7D3DNEbhh+1pNkk3xK6fSvKLZqmGHEyC/xwKBgQCZp5gVFGjyRdljx2fHfVs/2S4DFt/Q5oTEYfi5Iyzr7gr+AnGbxndB0mieE5cY1bTHBXgEgUn4YrWmqpC6sKZIcThtSpVM3eZ/buzy9/61F6qfJnyc9nKqC5U4hR0HDKhmtSeI/7YcGFPN/WeM+e156QzvNRYKniqAFJp92eU7IQKBgQDQ1WNrQZXiDUsfXBj9YAIQl/xGVW50pMZFx4mEnMETxtA+MGuKHhUfrniwcGOWB0+ZvYzll7hYldn4wIi/+qp9AoP7hLdkzOkLc3aBnLhAMVWH6FBENzfU57cpJ1Mjx6CKeISsJDDn9JaJ6a4HfKtnzqXIfw69r+Ml93QY8ug5+wKBgGrw5vrzUJp5yGTmwcCuPj7EFQi+KJ0enEavzPA4l/ecj75c4WYcdivRgQGX0BsC2SqGRmG/85YbpL7IBnfYd67ugBAcYJdkx+PhIXRqjw6gxRTrxhxwMggy1m0sA1W7DhFL+p9Gya0cczeFeu3wHwLKiH74jiBGclLg+X8nES1v-----END PRIVATE KEY-----
kanban.authentication.local.config.publicKey= -----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvzzZ8fqfEzoE87ciizfDhvlUnofk1m5QoT0dlY+6n2uuC3OGekTK4zKoZpXhdWmdL5rTyTVYkO+r9NIrXmAfge7phvFl2sZ77Nb9uyY9BjMctIHrRA+Wna2duUf2PvPJaXUiyvT0JPJsWAL+Ebi2D+RyLNJZCiZ4C5lp+raY9XxB8IBpNGLsVfR33jrV6Na9Eq1rBi0MfCs0+AbuVhH/HXQ+ZMR4yp4t+Kra/chSvHlvpwbpI91ZdMdbRr3WJcuAqUFEsLmhzGhXwnWui2tbWFAQmkEFznVTVY9NQ2yzElKpihkaSmesJsGyO2eCRV7RKON0ZNZ1T2lPJYL3wYRuZwIDAQAB-----END PUBLIC KEY-----
kanban.authentication.local.config.accessTokenExpireTime=600000
#refresh token expiration time in milliseconds (3 days)
kanban.authentication.local.config.refreshTokenExpireTime=259200000
kanban.authentication.local.config.enable=true

#config encript
jasypt.encryptor.bean=encryptorBean
jasypt.encryptor.algorithm=PBEWithMD5AndDES
jasypt.encryptor.keyobtentioniterations=1000
jasypt.encryptor.poolsize=1
jasypt.encryptor.providername=SunJCE
jasypt.encryptor.saltgeneratorclassname=org.jasypt.salt.RandomSaltGenerator
jasypt.encryptor.stringoutputtype=base64
#Jackson config
spring.jackson.default-property-inclusion=non_null
#Monitoring
management.server.port=9002
management.endpoint.shutdown.access=NONE
management.endpoints.web.exposure.include=health,metrics,prometheus
management.endpoint.health.show-details=always
spring.jpa.properties.hibernate.format_sql=true
management.endpoint.health.probes.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.endpoint.health.group.liveness.include=livenessState,ping,db,diskSpace
management.endpoint.health.group.readiness.include=readinessState,ping,db
#Monitor
mbmonitor.url=http://localhost:9000/
monitor.external.execution.url=${mbmonitor.url}api/external-execution
monitor.rts.url=${mbmonitor.url}api/rts
# 5 minute
mbmonitor.alert.noCommentThreshold=300
# 30 minute
mbmonitor.alertGroup.alertGroupHandleTriggerInterval=1800
mbmonitor.alertGroup.amountOfAlertProcessAtTheSameTime=300
#30 minute
mbmonitor.execution.timeout=30
mbmonitor.teams.proxy.ip=**********
mbmonitor.teams.proxy.port=8080
#8MB File config
mbmonitor.file-upload.dir=/data/mbmonitor/file-upload
mbmonitor.file-storage.upload-max-file-size=8388608
#10MB Maximum total size of email
mbmonitor.email.max-total-size=10485760
mbmonitor.telegram.local.dns.ip=***********
mbmonitor.telegram.local.dns.port=443
mbmonitor.database.connection.initialization.timeout=1000

# Circuit Breaker Config
mbmonitor.sentinel.degrade.rules[0].resource=getSingleChange
mbmonitor.sentinel.degrade.rules[0].grade=1
mbmonitor.sentinel.degrade.rules[0].minRequestAmount=50
mbmonitor.sentinel.degrade.rules[0].count=0.5
mbmonitor.sentinel.degrade.rules[0].timeWindow=5
mbmonitor.sentinel.degrade.rules[0].statIntervalMs=20000
mbmonitor.sentinel.degrade.rules[1].resource=updateSingleChange
mbmonitor.sentinel.degrade.rules[1].grade=1
mbmonitor.sentinel.degrade.rules[1].minRequestAmount=50
mbmonitor.sentinel.degrade.rules[1].count=0.5
mbmonitor.sentinel.degrade.rules[1].timeWindow=5
mbmonitor.sentinel.degrade.rules[1].statIntervalMs=20000
mbmonitor.sentinel.enabled=true
# setting hikari query sql
mbmonitor.database.hikari.minimumIdle=5
mbmonitor.database.hikari.maximumPoolSize=20
mbmonitor.database.hikari.idleTimeout=30000
mbmonitor.database.hikari.poolName=monitorCP
mbmonitor.database.hikari.maxLifetime=2000000
mbmonitor.database.hikari.connectionTimeout=30000
#trust domain
mbmonitor.trusted.hostnames=mbmonitor.tanzu-uat.mbbank.com.vn

# Thread pool config
thread-pool.pool-size.core=5
thread-pool.pool-size.max=50
thread-pool.queue-capacity=500
thread-pool.thread-name-prefix=Request-
thread-pool.await-termination-seconds=120
superiors.sql-execution.private-password=Kanban@123456

#keycloak config
mbmonitor.security.keycloak.clientId=mbmonitor-frontend
mbmonitor.security.keycloak.url= https://keycloak-internal-uat.mbbank.com.vn/auth/realms/internal/protocol/openid-connect/token
# 1p
mbmonitor.security.keycloak.centralized.timeout=60000
# path variable for playwright
mbmonitor.monitor.web.playwright.path=/data/mbmonitor/ms-playwright
mbmonitor.monitor.web.resource.chromium=chromium-win64.zip
mbmonitor.monitor.web.resource.firefox=firefox-win.zip
mbmonitor.monitor.web.resource.driver=driver-win.zip
# s3 config
mbmonitor.s3.access-key=********************
mbmonitor.s3.secret-key=4GwmIvzUaHgN2FqRYtyB5igdFDeEn91io4Vt6Nsk
mbmonitor.s3.endpoint=https://devobs01.mbbank.com.vn
mbmonitor.s3.bucket-name=mbmonitor
mbmonitor.s3.region=ap-southeast-1
mbmonitor.s3.enabled=true

logging.level.com.fasterxml.jackson.databind=trace
logging.level.org.springframework.http.converter=trace
