package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.dtos.QueryMapDto;
import vn.com.mbbank.kanban.core.enums.SqlOperatorEnum;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.KafkaJobModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaJobTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.log.EmailConnectionLogModelMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.JobKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailConfigPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.EmailConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.EmailConfigEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.EmailConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.CollectEmailConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.EmailConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailConfigService;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EmailConfigServiceImpl extends BaseServiceImpl<EmailConfigEntity, Long>
    implements EmailConfigService {
  private static final String DOMAIN_EMAIL_CONNECT = "@mbbank.com.vn";
  EmailConfigRepository emailConfigRepository;
  CollectEmailConfigRepository collectEmailConfigRepository;
  JobKafkaProducerService jobKafkaProducerService;
  SysLogKafkaProducerService sysLogKafkaProducerService;
  EmailConnectionLogModelMapper emailConnectionLogModelMapper = EmailConnectionLogModelMapper.INSTANCE;


  @Override
  protected JpaCommonRepository<EmailConfigEntity, Long> getRepository() {
    return emailConfigRepository;
  }

  @Override
  public EmailConfigResponse createOrUpdate(EmailConfigRequest emailConfigRequest)
      throws BusinessException {
    validateSaveEmailConfigRequest(emailConfigRequest);
    var logName = emailConfigRequest.getUsername() + DOMAIN_EMAIL_CONNECT + " " + emailConfigRequest.getProtocolType();
    var emailConfig = EmailConfigEntityMapper.INSTANCE.map(emailConfigRequest);
    Long id = emailConfigRequest.getId();
    var isUpdate = !KanbanCommonUtil.isEmpty(id) && id > 0L;
    if (isUpdate) {
      var oldEmailConfig = emailConfigRepository.findById(id)
          .orElseThrow(() -> new BusinessException(ErrorCode.EMAIL_CONFIG_NOT_FOUND));
      logName = getLogName(oldEmailConfig);
      emailConfig.setIntervalTime(oldEmailConfig.getIntervalTime());
      if (!Objects.equals(emailConfigRequest.getProtocolType(), oldEmailConfig.getProtocolType())
          || !Objects.equals(emailConfig.getUsername(), oldEmailConfig.getUsername())) {
        var collectEmailConfigs =
            collectEmailConfigRepository.findAllByEmailConfigId(emailConfigRequest.getId());
        if (!collectEmailConfigs.isEmpty()) {
          throw new BusinessException(ErrorCode.EMAIL_CONFIG_DEPENDENCY_ERROR);
        }
      }
    }
    emailConfig.setPassword(KanbanEncryptorUtils.encrypt(emailConfig.getPassword()));
    emailConfig.setEmail(emailConfigRequest.getUsername() + DOMAIN_EMAIL_CONNECT);
    var email = save(emailConfig);
    sysLogKafkaProducerService.send(
        isUpdate ? LogActionEnum.EDIT_EMAIL_CONNECTION : LogActionEnum.CREATE_EMAIL_CONNECTION,
        logName,
        emailConnectionLogModelMapper.map(emailConfig));
    return EmailConfigResponseMapper.INSTANCE.map(email);
  }

  protected void validateSaveEmailConfigRequest(EmailConfigRequest emailConfigRequest)
      throws BusinessException {
    Long id = emailConfigRequest.getId();
    String username = emailConfigRequest.getUsername();
    var isUpdate = !KanbanCommonUtil.isEmpty(id) && id > 0L;
    List<EmailProtocolTypeEnum> protocolTypes =
        EmailProtocolTypeEnum.SMTP.equals(emailConfigRequest.getProtocolType())
            ? List.of(EmailProtocolTypeEnum.SMTP) :
            List.of(EmailProtocolTypeEnum.IMAP, EmailProtocolTypeEnum.EXCHANGE);
    boolean isExists =
        isUpdate ? existByIdNotAndEmailAndProtocolTypeIn(id, username + DOMAIN_EMAIL_CONNECT,
            protocolTypes) :
            existByEmailAndProtocolTypeIn(username + DOMAIN_EMAIL_CONNECT, protocolTypes);

    if (isExists) {
      throw new BusinessException(ErrorCode.EMAIL_CONFIG_EXIST);
    }
  }

  @Override
  public boolean existByEmailAndProtocolTypeIn(String email,
                                               List<EmailProtocolTypeEnum> protocolTypes) {
    return emailConfigRepository.existsByEmailAndProtocolTypeIn(email, protocolTypes);
  }

  @Override
  public Optional<EmailConfigEntity> findByEmailAndProtocolType(String email,
                                                                EmailProtocolTypeEnum protocolTypes) {
    return emailConfigRepository.findByEmailAndProtocolType(email, protocolTypes);
  }

  @Override
  public boolean existByIdNotAndEmailAndProtocolTypeIn(Long id, String email,
                                                       List<EmailProtocolTypeEnum> protocolTypes) {
    return emailConfigRepository.existsByIdNotAndEmailAndProtocolTypeIn(id, email, protocolTypes);
  }

  @Override
  public List<EmailConfigEntity> findAllByProtocolTypeIn(
      Collection<EmailProtocolTypeEnum> protocolType) {
    return emailConfigRepository.findAllByProtocolTypeIn(protocolType);
  }

  @Override
  public EmailConfigResponse updateStatus(Long id) throws BusinessException {
    var emailConfig = emailConfigRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.COLLECT_EMAIL_CONFIG_NOT_FOUND));
    emailConfig.setActive(!emailConfig.isActive());
    emailConfig.setExecuted(false);
    if (EmailProtocolTypeEnum.SMTP != emailConfig.getProtocolType()) {
      if (!emailConfig.isActive()) {
        jobKafkaProducerService.notifyJobUpdate(KafkaTypeEnum.EMAIL_COLLECT,
            new KafkaJobModel<Long>().type(
                KafkaJobTypeEnum.DELETE).configId(id));
      } else {
        jobKafkaProducerService.notifyJobUpdate(KafkaTypeEnum.EMAIL_COLLECT,
            new KafkaJobModel<Long>().type(
                KafkaJobTypeEnum.NEW_OR_UPDATE).configId(id));
      }
    }
    var emailConfigEntity = emailConfigRepository.save(emailConfig);
    sysLogKafkaProducerService.send(
        emailConfigEntity.isActive() ? LogActionEnum.ACTIVE_EMAIL_CONNECTION : LogActionEnum.INACTIVE_EMAIL_CONNECTION,
        getLogName(emailConfig));
    return EmailConfigResponseMapper.INSTANCE.map(emailConfigEntity);
  }

  @Override
  public void deleteEmailConfigById(Long id) throws BusinessException {
    var emailConfig = emailConfigRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.COLLECT_EMAIL_CONFIG_NOT_FOUND));
    var collectEmailConfigs = collectEmailConfigRepository.findAllByEmailConfigId(id);
    if (!collectEmailConfigs.isEmpty()) {
      throw new BusinessException(ErrorCode.EMAIL_CONFIG_DEPENDENCY_ERROR);
    }
    jobKafkaProducerService.notifyJobUpdate(KafkaTypeEnum.EMAIL_COLLECT,
        new KafkaJobModel<Long>().type(
            KafkaJobTypeEnum.DELETE).configId(id));
    super.delete(emailConfig);
    sysLogKafkaProducerService.send(LogActionEnum.DELETE_EMAIL_CONNECTION, getLogName(emailConfig));
  }

  @Override
  public Page<EmailConfigResponse> findAll(EmailConfigPaginationRequest paginationRequest) {
    List<QueryMapDto> queryMapDtos = new ArrayList<>();
    if (!KanbanCommonUtil.isEmpty(paginationRequest.getEmail())) {
      QueryMapDto<String> nameQueryMapDto = new QueryMapDto<>();
      nameQueryMapDto.setColumn("email");
      nameQueryMapDto.setOperator(SqlOperatorEnum.LIKE);
      nameQueryMapDto.setValue(paginationRequest.getEmail());
      nameQueryMapDto.setAndWithCollectionOther(true);
      queryMapDtos.add(nameQueryMapDto);
    }
    if (!KanbanCommonUtil.isEmpty(paginationRequest.getProtocolTypes())) {
      QueryMapDto<List<EmailProtocolTypeEnum>> queryMapDto = new QueryMapDto<>();
      queryMapDto.setColumn("protocolType");
      queryMapDto.setOperator(SqlOperatorEnum.IN);
      queryMapDto.setValue(paginationRequest.getProtocolTypes());
      queryMapDto.setAndWithCollectionOther(true);
      queryMapDtos.add(queryMapDto);
    }

    if (!paginationRequest.isWithInactived()) {
      QueryMapDto<Boolean> queryMapDto = new QueryMapDto<>();
      queryMapDto.setColumn("active");
      queryMapDto.setOperator(SqlOperatorEnum.EQUALS);
      queryMapDto.setValue(true);
      queryMapDto.setAndWithCollectionOther(true);
      queryMapDtos.add(queryMapDto);
    }
    paginationRequest.setPropertiesSearch(
        List.of("host", "description", "port", "email", "protocolType"));
    return EmailConfigResponseMapper.INSTANCE.map(
        super.findWithPaging(paginationRequest, queryMapDtos));
  }

  private String getLogName(EmailConfigEntity emailConfig) {
    return emailConfig.getEmail() + " " + emailConfig.getProtocolType();
  }
}