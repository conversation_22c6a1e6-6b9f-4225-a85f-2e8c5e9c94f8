package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanStringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.constants.BeanNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.S3FileService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.FileStorageModel;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.FileStorageRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.FileStorageService;


/**
 * FileStorageServiceImpl using S3 storage instead of local filesystem.
 */
@Service(BeanNameConstants.FILE_STORAGE_S3_SERVICE)
public class FileStorageServiceS3Impl extends BaseServiceImpl<FileStorageEntity, Long>
    implements FileStorageService {

  private static final int BYTES_PER_MB = 1024 * 1024;
  private final Logger logger = LoggerFactory.getLogger(FileStorageServiceS3Impl.class);
  @Autowired
  FileStorageRepository fileStorageRepository;
  @Autowired
  S3FileService s3FileService;
  @Qualifier(BeanNameConstants.COMMON_TASK_EXECUTOR)
  @Autowired
  Executor commonTaskExecutor;
  @Value("${mbmonitor.file-storage.upload-max-file-size}")
  private Long maxFileSize;
  @Value("${mbmonitor.email.max-total-size}")
  private long emailMaxTotalSize;

  @Override
  protected JpaCommonRepository<FileStorageEntity, Long> getRepository() {
    return fileStorageRepository;
  }

  @Override
  public void deleteAllByDependencyNameAndDependencyId(String dependencyName, String dependencyId) {
    fileStorageRepository.deleteAllByDependencyNameAndDependencyId(dependencyName, dependencyId);
  }

  @Override
  @Transactional
  public void updateOldFiles(List<FileStorageModel> fileModels, String dependencyName, String dependencyId)
      throws BusinessException {
    var keepIds = CollectionUtils.emptyIfNull(fileModels).stream()
        .map(FileStorageModel::getId)
        .toList();
    var files = fileStorageRepository.findAllByDependencyNameAndDependencyId(dependencyName, dependencyId);
    var filesToDelete = files.stream()
        .filter(f -> !keepIds.contains(f.getId()))
        .toList();
    var paths = filesToDelete.stream().map(FileStorageEntity::getPath).toList();
    var ids = filesToDelete.stream().map(FileStorageEntity::getId).toList();
    if (!paths.isEmpty()) {
      try {
        s3FileService.deleteObjects(paths);
      } catch (Exception e) {
        logger.warn("Failed to delete files from S3: {}", paths, e);
        throw new BusinessException(ErrorCode.FILE_DELETE_ERROR, paths);
      }
    }
    if (!ids.isEmpty()) {
      fileStorageRepository.deleteByIdIn(ids);
    }
  }


  @Override
  public void uploadMultipleFiles(List<FileStorageModel> fileStorages,
                                  List<MultipartFile> fileUploads,
                                  String dependencyName,
                                  String dependencyId) throws IOException, BusinessException {

    String prefix = String.join("/", dependencyName, dependencyId);
    long totalSize = CollectionUtils.emptyIfNull(fileStorages).stream()
        .mapToLong(f -> Optional.ofNullable(f.getSize()).orElse(0L))
        .sum();

    Map<MultipartFile, String> validFiles = new LinkedHashMap<>();
    for (MultipartFile file : fileUploads) {
      if (Objects.isNull(file) || file.isEmpty()) {
        continue;
      }
      String originalFilename = file.getOriginalFilename();
      String cleanFileName = StringUtils.sanitizeFileName(originalFilename);
      if (KanbanStringUtils.isNullOrEmpty(originalFilename)) {
        throw new BusinessException(ErrorCode.INVALID_FILE_NAME, originalFilename);
      }
      if (file.getSize() > maxFileSize) {
        throw new BusinessException(ErrorCode.FILE_SIZE_ERROR, originalFilename, maxFileSize / BYTES_PER_MB);
      }
      totalSize += file.getSize();
      if (totalSize > emailMaxTotalSize) {
        throw new BusinessException(ErrorCode.FILE_SIZE_TOTAL_ERROR, emailMaxTotalSize / BYTES_PER_MB);
      }
      validFiles.put(file, cleanFileName);
    }

    List<CompletableFuture<FileStorageEntity>> futures = validFiles.entrySet().stream()
        .map(entry -> CompletableFuture.supplyAsync(() -> {
          MultipartFile file = entry.getKey();
          String cleanFileName = entry.getValue();
          String s3Path = prefix + "/" + cleanFileName;
          try {
            s3FileService.putObject(s3Path, file.getBytes(), file.getContentType());
          } catch (IOException e) {
            logger.error("Failed to upload: {}", cleanFileName, e);
            throw new RuntimeException("Failed to upload: " + s3Path, e);
          }
          var entity = new FileStorageEntity();
          entity.setPath(s3Path);
          entity.setDependencyName(dependencyName);
          entity.setDependencyId(dependencyId);
          entity.setSize(file.getSize());
          return entity;
        }, commonTaskExecutor)).toList();
    var fileStorageEntities = futures.stream()
        .map(CompletableFuture::join)
        .toList();
    fileStorageRepository.saveAll(fileStorageEntities);
  }


  @Override
  public StreamingResponseBody readFile(String filePath) throws BusinessException {
    if (!s3FileService.headObject(filePath)) {
      throw new BusinessException(ErrorCode.EXPORT_DATA_NOT_FOUND);
    }
    return outputStream -> {
      byte[] fileData = s3FileService.getObject(filePath);
      outputStream.write(fileData);
      outputStream.flush();
    };
  }


  @Override
  @Transactional
  public void deleteWithId(Long id) throws BusinessException {
    FileStorageEntity file = fileStorageRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.FILE_DELETE_ERROR));
    super.deleteById(id);
    try {
      s3FileService.deleteObject(file.getPath());
    } catch (Exception e) {
      logger.error("Error deleting file from S3: {}", file.getPath(), e);
      throw new BusinessException(ErrorCode.FILE_DELETE_ERROR);
    }
  }

  @Override
  public List<FileStorageEntity> findAllFileNameByDependencyNameAndDependencyId(String dependencyName,
                                                                                String dependencyId) {
    return fileStorageRepository.findAllByDependencyNameAndDependencyId(dependencyName, dependencyId);
  }

}
