package vn.com.mbbank.kanban.mbmonitor.server.converter;

import jakarta.persistence.AttributeConverter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/8/2024
 */
public class PermissionModuleConverter implements AttributeConverter<PermissionModuleEnum, String> {
  @Override
  public String convertToDatabaseColumn(PermissionModuleEnum attribute) {
    return attribute != null ? attribute.name() : null;
  }

  @Override
  public PermissionModuleEnum convertToEntityAttribute(String dbData) {
    try {
      return PermissionModuleEnum.valueOf(dbData);
    } catch (IllegalArgumentException | NullPointerException e) {
      // Return a default value if the database value is invalid
      return PermissionModuleEnum.UNKNOWN;  // Replace with your default value
    }
  }
}
