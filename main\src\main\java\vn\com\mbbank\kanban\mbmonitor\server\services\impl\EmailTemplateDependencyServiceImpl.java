package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import jakarta.transaction.Transactional;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailTemplateEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.EmailTemplateDependenciesResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.EmailTemplateRepository;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.WorkflowNodeDependencyRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailTemplateDependencyService;


/**
 * Service Logic email template service.
 */
@RequiredArgsConstructor
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailTemplateDependencyServiceImpl extends BaseServiceImpl<EmailTemplateEntity, Long>
    implements EmailTemplateDependencyService {
  private final EmailTemplateRepository emailTemplateRepository;
  private final WorkflowNodeDependencyRepository workflowNodeDependencyRepository;
  private final EmailTemplateReceiverServiceImpl emailTemplateReceiverService;
  private final SysLogKafkaProducerService sysLogKafkaProducerService;

  @Override
  protected JpaCommonRepository<EmailTemplateEntity, Long> getRepository() {
    return emailTemplateRepository;
  }

  @Override
  @Transactional
  public void deleteWithId(Long id) throws BusinessException {
    var emailTemplate = emailTemplateRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.EMAIL_TEMPLATE_NOT_FOUND));
    var dependencies = findAllDependenciesById(id);
    if (!KanbanCommonUtil.isEmpty(dependencies.getWorkflowTemplates())) {
      throw new BusinessException(ErrorCode.EMAIL_TEMPLATE_CAN_NOT_BE_DELETE);
    }
    emailTemplateReceiverService.deleteAllByEmailTemplateId(id);
    sysLogKafkaProducerService.send(LogActionEnum.DELETE_TEMPLATE, emailTemplate.getName());
    deleteById(emailTemplate.getId());
  }

  @Override
  public EmailTemplateDependenciesResponse findAllDependenciesById(Long id) {
    var res = new EmailTemplateDependenciesResponse();
    res.setWorkflowTemplates(
        workflowNodeDependencyRepository.findWorkflowTemplateNameByReferenceIdAndType(String.valueOf(id),
            WorkflowNodeTypeEnum.EMAIL));
    return res;
  }
}
