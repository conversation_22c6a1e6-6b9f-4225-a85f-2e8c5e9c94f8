package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecuteScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecuteScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.ExecutionResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionService;
import vn.com.mbbank.kanban.test.ControllerTest; // Import ControllerTest

@ExtendWith(MockitoExtension.class)
class ExecutionControllerTest extends ControllerTest { // Extend ControllerTest

  @Mock
  private ExecutionService executionService;

  @Mock
  private ExecutionDependencyService executionDependencyService;

  @Mock
  private CommonAclPermissionService commonAclPermissionService;

  // Mapper is usually tested separately or assumed correct if using MapStruct INSTANCE
  private final ExecutionResponseMapper executionResponseMapper = ExecutionResponseMapper.INSTANCE;

  @InjectMocks
  @Spy
  private ExecutionController executionController;

  private ExecutionResponse executionResponse;
  private ExecutionRequest executionRequest;
  private ExecuteScriptRequest executeScriptRequest;
  private ExecuteScriptResponse executeScriptResponse;
  private String testId;
  private String executionGroupId;

  @BeforeEach
  void setUp() {
    // Arrange common test data
    testId = "test-id-123";
    executionGroupId = "group-id-456";

    executionResponse = new ExecutionResponse();
    executionResponse.setId(testId);
    executionResponse.setName("Test Execution");

    executionRequest = new ExecutionRequest();
    executionRequest.setId(testId);
    executionRequest.setName("Test Execution Request");
    executionRequest.setExecutionGroupId(executionGroupId); // Ensure this is set for save method
    executionRequest.setScript("SELECT 1;");


    executeScriptRequest = new ExecuteScriptRequest();
    executeScriptRequest.setExecutionId(testId);

    executeScriptResponse = new ExecuteScriptResponse();
    executeScriptResponse.setScriptResponse("Script executed successfully");
    executeScriptResponse.setId(testId);
    executeScriptResponse.setName("Test Execution Script Response");
  }

  // Permission Tests
  @Test
  void findById_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.EXECUTION, PermissionActionEnum.VIEW),
        new AclPermissionModel(PermissionModuleEnum.AUTO_TRIGGER_ACTION_CONFIG, PermissionActionEnum.VIEW)

    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "findById", ExecutionController.class);
  }

  @Test
  void findAllWithPaging_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.EXECUTION, PermissionActionEnum.VIEW)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "findAllWithPaging", ExecutionController.class);
  }

  @Test
  void deleteById_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.EXECUTION, PermissionActionEnum.DELETE)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "deleteById", ExecutionController.class);
  }

  @Test
  void findAllByExecutionGroupId_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.EXECUTION_GROUP, PermissionActionEnum.DELETE),
        new AclPermissionModel(PermissionModuleEnum.USER_MANAGEMENT, PermissionActionEnum.VIEW),
        new AclPermissionModel(PermissionModuleEnum.ROLE_MANAGEMENT, PermissionActionEnum.VIEW)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "findAllByExecutionGroupId", ExecutionController.class);
  }


  // Functional Tests
  @Test
  void findById_success_whenFound() throws BusinessException {
    // Arrange
    when(executionService.findWithId(testId)).thenReturn(new ExecutionResponse());

    // Act
    ResponseData<ExecutionResponse> response = executionController.findById(testId);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    verify(executionService, times(1)).findWithId(testId);
  }

  @Test
  void execute_success() throws BusinessException {
    // Arrange
    when(executionService.execute(any(ExecuteScriptRequest.class))).thenReturn(executeScriptResponse);

    // Act
    ResponseData<ExecuteScriptResponse> response = executionController.execute(executeScriptRequest);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals("Script executed successfully", response.getData().getScriptResponse());
    verify(executionService, times(1)).execute(executeScriptRequest);
  }

  @Test
  void findByIdWithVariable_success_whenFound() throws BusinessException {
    // Arrange
    when(executionService.findByIdWithVariable(testId)).thenReturn(executionResponse);

    // Act
    ResponseData<ExecutionResponse> response = executionController.findByIdWithVariable(testId);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(testId, response.getData().getId());
    verify(executionService, times(1)).findByIdWithVariable(testId);
  }

  @Test
  void findAllWithPaging_success() {
    // Arrange
    PaginationRequest requestDTO = new PaginationRequest();
    Page<ExecutionResponse> pageResponse = new PageImpl<>(Collections.singletonList(executionResponse));
    when(executionService.findAll(any(PaginationRequest.class))).thenReturn(pageResponse);

    // Act
    ResponseData<Page<ExecutionResponse>> response = executionController.findAllWithPaging(requestDTO);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(1, response.getData().getTotalElements());
    assertEquals(testId, response.getData().getContent().get(0).getId());
    verify(executionService, times(1)).findAll(requestDTO);
  }

  @Test
  void save_success_whenCreateOrUpdate() throws BusinessException {
    // Arrange
    // Note: Permission for save is handled by makeSureCreateOrUpdate in BaseController,
    // which is not directly tested here via @HasPermission on the controller method itself.
    doNothing().when(executionController).makeSureCreateOrUpdate(any(), any(), any(), any());
    when(executionService.createOrUpdate(any(ExecutionRequest.class))).thenReturn(new ExecutionEntity());

    // Act
    ResponseData<ExecutionResponse> response = executionController.save(executionRequest);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    verify(executionService, times(1)).createOrUpdate(executionRequest);
  }

  @Test
  void deleteById_success() throws BusinessException {
    // Arrange
    doNothing().when(executionService).deleteWithId(testId);

    // Act
    ResponseData<String> response = executionController.deleteById(testId);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertEquals("OK", response.getData());
    verify(executionService, times(1)).deleteWithId(testId);
  }

  @Test
  void findAllByExecutionGroupId_success() {
    // Arrange
    List<ExecutionResponse> listResponse = Collections.singletonList(new ExecutionResponse());
    when(executionService.findAllByExecutionGroupId(executionGroupId)).thenReturn(listResponse);

    // Act
    ResponseData<List<ExecutionResponse>> response = executionController.findAllByExecutionGroupId(executionGroupId);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(1, response.getData().size());
    verify(executionService, times(1)).findAllByExecutionGroupId(executionGroupId);
  }

  @Test
  void findAllWithPermissionByExecutionGroupId_success() throws BusinessException {
    // Arrange
    List<ExecutionEntity> listResponse = Collections.singletonList(new ExecutionEntity());
    when(executionDependencyService.findAllWithPermissionByExecutionGroupId(executionGroupId)).thenReturn(listResponse);

    // Act
    ResponseData<List<ExecutionResponse>> response = executionController.findAllWithPermissionByExecutionGroupId(executionGroupId);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(1, response.getData().size());
    verify(executionDependencyService, times(1)).findAllWithPermissionByExecutionGroupId(executionGroupId);
  }
}
