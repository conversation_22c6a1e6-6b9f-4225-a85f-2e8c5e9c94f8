package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;

/**
 * Model view attribute to filter in alert priority config.
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlertPriorityConfigRequest extends PaginationRequestDTO {

  Long id;

  @NotBlank
  @Size(min = 1, message = "Priority Config name can not be empty")
  @Size(
      max = CommonConstants.ALERT_PRIORITY_CONFIG_NAME_MAX_LENGTH,
      message = "Priority Config name has max {max} character"
  )
  String name;

  @Size(min = 1, message = "Priority Config color can not be empty")
  @Size(
      max = CommonConstants.ALERT_PRIORITY_CONFIG_COLOR_MAX_LENGTH,
      message = "Priority Config color has max {max} character"
  )
  String color;
}
