package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;


import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailConfigPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CollectEmailConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.EmailConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.EmailConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.CollectEmailConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailConfigService;

/**
 * Controller logic email config.
 */
@RestController
@RequestMapping(ServerUrl.EMAIL_CONFIG_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EmailConfigController extends BaseController {
  EmailConfigService emailConfigService;
  CollectEmailConfigService collectEmailConfigService;

  /**
   * Finds all email config.
   *
   * @param paginationRequest the pagination request
   * @return a paginated list of EmailConfigResponse
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.EMAIL_COLLECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.EMAIL_CONNECTION, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.SEND_EMAIL, action = PermissionActionEnum.SEND_EMAIL)
  })
  @GetMapping
  public ResponseData<Page<EmailConfigResponse>> findAll(
      @ModelAttribute EmailConfigPaginationRequest paginationRequest) {
    return ResponseUtils.success(emailConfigService.findAll(paginationRequest));
  }

  /**
   * Create or update email config.
   *
   * @param request request
   * @return new email config EmailConfigResponse
   */
  @PostMapping
  public ResponseData<EmailConfigResponse> createOrUpdate(
      @RequestBody @Valid EmailConfigRequest request
  ) throws BusinessException {
    List<Long> ids = new ArrayList<>();
    if (!KanbanCommonUtil.isEmpty(request.getId()) && request.getId() > 0L) {
      ids.add(request.getId());
    }
    makeSureCreateOrUpdate(PermissionModuleEnum.EMAIL_CONNECTION, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, ids);
    return ResponseUtils.success(emailConfigService.createOrUpdate(request));
  }

  /**
   * Find By id.
   *
   * @param id id
   * @return data
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_CONNECTION, action = PermissionActionEnum.VIEW),
  })
  @GetMapping("{id}")
  public ResponseData<EmailConfigResponse> findById(@PathVariable Long id) {
    var emailResponse = EmailConfigResponseMapper.INSTANCE.map(emailConfigService.findById(id));
    return ResponseUtils.success(emailResponse);
  }


  /**
   * Delete by Id.
   *
   * @param id id
   * @return Object
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_CONNECTION, action = PermissionActionEnum.DELETE),
  })
  @Operation(summary = "Delete email config by id")
  @DeleteMapping("/{id}")
  public ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    emailConfigService.deleteEmailConfigById(id);
    return ResponseUtils.success("OK");
  }

  /**
   * find all config email collect by email config id.
   *
   * @param id email configs
   * @return list  config email collect
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_CONNECTION, action = PermissionActionEnum.EDIT),
      @AclPermission(module = PermissionModuleEnum.EMAIL_CONNECTION, action = PermissionActionEnum.DELETE),
  })
  @Operation(summary = "Find all Collect email configs by id of email configs")
  @GetMapping("{id}/collect-email-configs")
  public ResponseData<List<CollectEmailConfigResponse>> findAllCollectEmailConfigById(
      @PathVariable Long id) {
    return ResponseUtils.success(collectEmailConfigService.findAllByEmailConfigId(id));
  }

  /**
   * Active collect email config by id.
   *
   * @param id id
   * @return Object
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_CONNECTION, action = PermissionActionEnum.EDIT),
  })
  @PutMapping("/{id}/toggle-status")
  public ResponseData<EmailConfigResponse> active(@PathVariable Long id) throws BusinessException {
    return ResponseUtils.success(emailConfigService.updateStatus(id));
  }
}
