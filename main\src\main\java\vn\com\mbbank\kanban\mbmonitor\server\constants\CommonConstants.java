package vn.com.mbbank.kanban.mbmonitor.server.constants;

/**
 * CommonConstants.
 */
public class CommonConstants {

  public static final int SECOND_PER_MINUTE = 60;
  public static final int ALERT_CLOSE_TIME_IF_COMMENT_IN_PAST_SECONDS = SECOND_PER_MINUTE * 5;
  public static final String DEFAULT_TIME_ZONE = "+7";
  /**
   * List constants of service/application.
   */
  public static final int SERVICE_NAME_MAX_LENGTH = 100;
  public static final int APPLICATION_NAME_MAX_LENGTH = 100;
  public static final int USER_NAME_MAX_LENGTH = 100;
  public static final int PASSWORD_MAX_LENGTH = 100;
  public static final int SERVICE_DESCRIPTION_MAX_LENGTH = 300;
  public static final int APPLICATION_DESCRIPTION_MAX_LENGTH = 300;
  public static final int MAX_APPLICATION_EXPORT_ROWS_LIMIT = 10000;
  public static final int MAX_SERVICE_EXPORT_ROWS_LIMIT = 10000;
  public static final int MAX_ALERT_EXPORT_ROWS_LIMIT = 50000;
  public static final int MAX_ALERT_XLSX_EXPORT_ROWS_LIMIT = 50000;
  public static final String CONTENT_DISPOSITION_ATTACHMENT = "attachment";
  /**
   * List constants of custom object.
   */
  public static final int CUSTOM_OBJECT_NAME_MAX_LENGTH = 100;
  public static final int CUSTOM_OBJECT_DESCRIPTION_MAX_LENGTH = 300;
  public static final int CUSTOM_OBJECT_REGEX_MAX_LENGTH = 250;
  public static final int CUSTOM_OBJECT_KEYWORD_MAX_LENGTH = 20;
  public static final int CUSTOM_OBJECT_INDEX_MAX_LENGTH = 1000;
  /**
   * List constants of alert priority.
   */
  public static final int ALERT_PRIORITY_CONFIG_NAME_MAX_LENGTH = 100;
  public static final int ALERT_PRIORITY_CONFIG_COLOR_MAX_LENGTH = 20;
  /**
   * List constants of config email.
   */
  public static final int COLLECT_EMAIL_CONFIG_NAME_MAX_LENGTH = 100;
  public static final int COLLECT_EMAIL_CONFIG_RECIPIENT_ALERT_MAX_LENGTH = 255;
  public static final int COLLECT_EMAIL_CONFIG_DESCRIPTION_MAX_LENGTH = 300;
  public static final int COLLECT_EMAIL_CONFIG_ABSENCE_INTERVAL_MIN = 300;
  public static final int COLLECT_EMAIL_CONFIG_INTERVAL_MIN = 15;
  public static final int COLLECT_EMAIL_CONFIG_ABSENCE_INTERVAL_MAX = 86400;
  public static final int EMAIL_CONFIG_DESCRIPTION_MAX_LENGTH = 300;
  public static final int EMAIL_PARTNER_LIST_MAX_LENGTH = 100;
  public static final int EMAIL_RECEIVER_MAX_LENGTH = 100;
  public static final int EMAIL_SUBJECT_MAX_LENGTH = 150;
  
  /**
   * List constants of RPA monitor web.
   */
  public static final int RPA_MONITOR_WEB_INTERVAL_MIN_VALUE = 0;
  public static final int RPA_MONITOR_WEB_INTERVAL_MAX_VALUE = 10;
  public static final int RPA_MONITOR_WEB_NUMBER_OF_RETRY_MIN_VALUE = 0;
  public static final int RPA_MONITOR_WEB_NUMBER_OF_RETRY_MAX_VALUE = 3;
  
  public static final int MONITOR_WEB_WEB_URL_MAX_LENGTH = 2000;
  public static final int MONITOR_WEB_TIMEOUT_MIN_VALUE = 5;
  public static final int MONITOR_WEB_TIMEOUT_MAX_VALUE = 60;
  public static final int MONITOR_WEB_CONTACT_MAX_LENGTH = 255;
  public static final int INTERVAL_MONTH_MIN_VALUE = 1;
  public static final int INTERVAL_MONTH_MAX_VALUE = 12;
  public static final int INTERVAL_DAY_OF_MONTH_MIN_VALUE = 1;
  public static final int INTERVAL_DAY_OF_MONTH_MAX_VALUE = 31;
  public static final int INTERVAL_DAY_OF_WEEK_MIN_VALUE = 1;
  public static final int INTERVAL_DAY_OF_WEEK_MAX_VALUE = 7;
  public static final int INTERVAL_HOUR_MIN_VALUE = 0;
  public static final int INTERVAL_HOUR_MAX_VALUE = 23;
  public static final int INTERVAL_MINUTE_MIN_VALUE = 0;
  public static final int INTERVAL_MINUTE_MAX_VALUE = 59;
  public static final int WAIT_SECOND_MIN_VALUE = 1;
  public static final int WAIT_SECOND_MAX_VALUE = 600;
  public static final int MAX_LENGTH_500_VALUE = 500;
  public static final int MAX_LENGTH_1000_VALUE = 1000;
  public static final int MAX_LENGTH_2000_VALUE = 2000;
  public static final int MAX_LENGTH_OF_NOTE = 300;
  public static final String INTERVAL_MONTH = "month";
  public static final String INTERVAL_DAY_OF_MONTH = "day of month";
  public static final String INTERVAL_DAY_OF_WEEK = "day of week";
  public static final String INTERVAL_HOUR = "hour";
  public static final String INTERVAL_MINUTE = "minute";

  /**
   * List common.
   */
  public static final int COMMON_NAME_MAX_LENGTH = 100;
  public static final int COMMON_DESCRIPTION_MAX_LENGTH = 300;
  public static final int COMMON_CONTACT_ALERT_MAX_LENGTH = 255;
  public static final int COMMON_CONTENT_ALERT_MAX_LENGTH = 3000;


  public static final int ALERT_GROUP_CONFIG_NAME_MAX_LENGTH = 100;
  public static final int ALERT_GROUP_CONFIG_DESCRIPTION_MAX_LENGTH = 200;
  public static final int EMAIL_CONFIG_HOST_MAX_LENGTH = 253;
  public static final int EMAIL_CONFIG_ADDRESS_MAX_LENGTH = 253;
  public static final int EMAIL_CONFIG_PASSWORD_MAX_LENGTH = 50;
  public static final String DEFAULT_DELIMITER = ",";
  /**
   * List constants of config trigger.
   */
  public static final int MIN_TIME_SINCE_LAST_TRIGGER = 1800;
  public static final int MAX_TIME_SINCE_LAST_TRIGGER = 86400;
}
