package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.constants.BeanNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailTemplateEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailTemplateReceiverEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.log.EmailTemplateLogModelMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailTemplateModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.FileStorageModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailTemplatePaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailTemplateRequest;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.EmailTemplateEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.EmailTemplateReceiverEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.EmailTemplateResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.FileStorageModelMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.EmailTemplateRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailTemplateReceiverService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailTemplateService;
import vn.com.mbbank.kanban.mbmonitor.server.services.FileStorageService;


/**
 * Service Logic email template service.
 */
@RequiredArgsConstructor
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailTemplateServiceImpl extends BaseServiceImpl<EmailTemplateEntity, Long>
    implements EmailTemplateService {
  final EmailTemplateRepository emailTemplateRepository;
  final EmailTemplateReceiverService receiverService;
  @Autowired
  @Qualifier(BeanNameConstants.FILE_STORAGE_S3_SERVICE)
  FileStorageService fileStorageService;
  final SysLogKafkaProducerService sysLogKafkaProducerService;
  final EmailTemplateResponseMapper responseMapper = EmailTemplateResponseMapper.INSTANCE;
  final EmailTemplateEntityMapper templateEntityMapper = EmailTemplateEntityMapper.INSTANCE;
  final EmailTemplateReceiverEntityMapper receiverEntityMapper =
      EmailTemplateReceiverEntityMapper.INSTANCE;
  final FileStorageModelMapper fileStorageModelMapper = FileStorageModelMapper.INSTANCE;
  final EmailTemplateLogModelMapper emailTemplateLogModelMapper = EmailTemplateLogModelMapper.INSTANCE;

  @Override
  protected JpaCommonRepository<EmailTemplateEntity, Long> getRepository() {
    return emailTemplateRepository;
  }

  @Override
  public EmailTemplateModel findEmailTemplateById(Long id) throws BusinessException {
    EmailTemplateEntity emailTemplateEntity = this.emailTemplateRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.EMAIL_TEMPLATE_NOT_FOUND));
    List<FileStorageEntity> fileStorages =
        fileStorageService.findAllFileNameByDependencyNameAndDependencyId(TableName.EMAIL_TEMPLATE,
            String.valueOf(id));
    List<FileStorageModel> fileModels = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(fileStorages)) {
      fileModels = fileStorages.stream().map(fileStorageModelMapper::map)
          .toList();
    }
    List<EmailTemplateReceiverEntity> receivers =
        receiverService.findAllByEmailTemplateId(emailTemplateEntity.getId());
    return this.responseMapper.map(emailTemplateEntity, fileModels, receivers);
  }

  @Override
  public Page<EmailTemplateModel> findAll(EmailTemplatePaginationRequest request) {
    Page<EmailTemplateEntity> entities = this.emailTemplateRepository.findAll(request);
    List<Long> ids = entities.stream().map(EmailTemplateEntity::getId).toList();
    List<EmailTemplateReceiverEntity> addresses =
        this.receiverService.findAllByEmailTemplateIdIn(ids);
    Map<Long, List<EmailTemplateReceiverEntity>> receiverMap =
        addresses.stream()
            .collect(Collectors.groupingBy(EmailTemplateReceiverEntity::getEmailTemplateId));
    return entities.map(
        entity -> this.responseMapper.map(entity, List.of(), receiverMap.get(entity.getId()))
    );
  }

  @Override
  @Transactional(rollbackFor = {IOException.class, BusinessException.class})
  public EmailTemplateModel createOrUpdate(EmailTemplateRequest request, List<MultipartFile> files)
      throws BusinessException, IOException {
    boolean isUpdateMode = Objects.nonNull(request.getId());
    var formatName = StringUtils.capitalizeFirstLetter(request.getName());
    var logName = formatName;
    var templateExisted = isUpdateMode
        ? emailTemplateRepository.findByIdNotAndNameIgnoreCase(request.getId(), request.getName().trim())
        : emailTemplateRepository.findByNameIgnoreCase(request.getName().trim());
    if (templateExisted.isPresent()) {
      throw new BusinessException(ErrorCode.EMAIL_TEMPLATE_NAME_EXIST, request.getName());
    }
    EmailTemplateEntity template;
    if (isUpdateMode) {
      template = this.emailTemplateRepository.findById(request.getId())
          .orElseThrow(() -> new BusinessException(ErrorCode.EMAIL_TEMPLATE_NOT_FOUND));
      logName = template.getName();
      this.templateEntityMapper.merge(template, request);
      this.receiverService.deleteAllByEmailTemplateId(request.getId());
    } else {
      template = this.templateEntityMapper.map(request);
    }
    template.setName(formatName);
    this.emailTemplateRepository.save(template);
    this.fileStorageService.updateOldFiles(request.getFileStorages(), TableName.EMAIL_TEMPLATE,
        String.valueOf(template.getId()));
    if (CollectionUtils.isNotEmpty(files)) {
      this.fileStorageService.uploadMultipleFiles(request.getFileStorages(), files,
          TableName.EMAIL_TEMPLATE,
          String.valueOf(template.getId()));
    }
    List<EmailTemplateReceiverEntity> receivers = this.receiverEntityMapper.map(template, request);
    var receiversEntities = this.receiverService.saveAll(receivers);

    sysLogKafkaProducerService.send(isUpdateMode ? LogActionEnum.EDIT_TEMPLATE : LogActionEnum.CREATE_TEMPLATE,
        logName, emailTemplateLogModelMapper.map(template, request.getTo(), request.getCc()));
    return this.responseMapper.map(template, List.of(), receiversEntities);
  }

  @Override
  @Transactional
  public void deleteWithEmailTemplateId(Long id) throws BusinessException {
    var template = this.emailTemplateRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.EMAIL_TEMPLATE_NOT_FOUND));
    this.receiverService.deleteAllByEmailTemplateId(id);
    sysLogKafkaProducerService.send(LogActionEnum.DELETE_TEMPLATE, template.getName());
    super.delete(template);
  }

  @Override
  public StreamingResponseBody download(Long id) throws BusinessException {
    var fileStorage = fileStorageService.findById(id);
    if (Objects.isNull(fileStorage)) {
      throw new BusinessException(ErrorCode.FILE_PATH_NOT_FOUND);
    }
    return fileStorageService.readFile(fileStorage.getPath());
  }
}
