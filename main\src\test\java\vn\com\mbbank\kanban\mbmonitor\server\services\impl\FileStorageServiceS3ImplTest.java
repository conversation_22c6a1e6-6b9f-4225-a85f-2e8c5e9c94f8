package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.S3FileService;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.FileStorageModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.FileStorageRepository;

@ExtendWith(MockitoExtension.class)
public class FileStorageServiceS3ImplTest {

  @InjectMocks
  private FileStorageServiceS3Impl service;

  @Mock
  private FileStorageRepository repository;

  @Mock
  private S3FileService s3FileService;

  @BeforeEach
  void setUp() {
    ReflectionTestUtils.setField(service, "maxFileSize", 8388608L);
    ReflectionTestUtils.setField(service, "emailMaxTotalSize", 16777216L);
  }

  @Test
  void getRepository_success() {
    var result = service.getRepository();
    assertEquals(repository, result);
  }


  @Test
  void updateOldFiles_success_deleteUnwantedFiles() throws BusinessException {
    FileStorageModel fileModel = new FileStorageModel();
    fileModel.setId(1L);
    List<FileStorageModel> fileModels = List.of(fileModel);

    FileStorageEntity fileToDelete = new FileStorageEntity();
    fileToDelete.setId(2L);
    fileToDelete.setPath("s3://bucket/path/file.txt");

    when(repository.findAllByDependencyNameAndDependencyId("dependency", "123")).thenReturn(List.of(fileToDelete));
    doNothing().when(s3FileService).deleteObjects(any());

    service.updateOldFiles(fileModels, "dependency", "123");

    verify(s3FileService, times(1)).deleteObjects(any());
    verify(repository, times(1)).deleteByIdIn(List.of(2L));
  }

  @Test
  void uploadMultipleFiles_error_fileExceedsMaxSize() {
    String dependencyName = "dependencyName";
    String dependencyId = "dependencyId";
    long maxFileSize = 8 * 1024 * 1024;
    MultipartFile largeFile = mock(MultipartFile.class);
    when(largeFile.getSize()).thenReturn(maxFileSize + 1);
    when(largeFile.getOriginalFilename()).thenReturn("largeFile.txt");
    when(largeFile.isEmpty()).thenReturn(false);
    List<MultipartFile> files = List.of(largeFile);

    BusinessException exception = assertThrows(BusinessException.class,
        () -> service.uploadMultipleFiles(List.of(), files, dependencyName, dependencyId));

    assertEquals(ErrorCode.FILE_SIZE_ERROR.getCode(), exception.getCode());
  }



  @Test
  void updateOldFiles_success_s3DeleteException() throws BusinessException {
    List<FileStorageModel> fileModels = new ArrayList<>();

    FileStorageEntity fileEntity1 = new FileStorageEntity();
    ReflectionTestUtils.setField(fileEntity1, "id", 1L);
    ReflectionTestUtils.setField(fileEntity1, "path", "s3://bucket/path/file.txt");

    when(repository.findAllByDependencyNameAndDependencyId("dpdName", "dpdId"))
        .thenReturn(Collections.singletonList(fileEntity1));
    doThrow(new RuntimeException("S3 delete failed")).when(s3FileService).deleteObjects(anyList());

    assertThrows(BusinessException.class, () -> service.updateOldFiles(fileModels, "dpdName", "dpdId"));

    verify(s3FileService, times(1)).deleteObjects(anyList());
  }

  @Test
  void updateOldFiles_success_deleteAllFilesWhenNoFileModelsProvided() throws BusinessException {
    List<FileStorageModel> fileModels = new ArrayList<>();

    FileStorageEntity fileEntity1 = new FileStorageEntity();
    ReflectionTestUtils.setField(fileEntity1, "id", 1L);
    ReflectionTestUtils.setField(fileEntity1, "path", "s3://bucket/path/file1.txt");

    FileStorageEntity fileEntity2 = new FileStorageEntity();
    ReflectionTestUtils.setField(fileEntity2, "id", 2L);
    ReflectionTestUtils.setField(fileEntity2, "path", "s3://bucket/path/file2.txt");

    when(repository.findAllByDependencyNameAndDependencyId("dependency", "123")).thenReturn(
        List.of(fileEntity1, fileEntity2));
    doNothing().when(s3FileService).deleteObjects(any());

    service.updateOldFiles(fileModels, "dependency", "123");

    verify(s3FileService, times(1)).deleteObjects(any());
    verify(s3FileService, times(1)).deleteObjects(any());
    verify(repository, times(1)).deleteByIdIn(List.of(1L, 2L));
  }

  @Test
  void updateOldFiles_success_noFilesDeletedWhenAllMatch() throws BusinessException {
    FileStorageModel fileModel = new FileStorageModel();
    fileModel.setId(1L);
    List<FileStorageModel> fileModels = List.of(fileModel);

    FileStorageEntity fileEntity = new FileStorageEntity();
    ReflectionTestUtils.setField(fileEntity, "id", 1L);
    ReflectionTestUtils.setField(fileEntity, "path", "s3://bucket/path/file.txt");

    when(repository.findAllByDependencyNameAndDependencyId("dependency", "123")).thenReturn(List.of(fileEntity));

    service.updateOldFiles(fileModels, "dependency", "123");

    verify(s3FileService, never()).deleteObjects(any());
  }


  @Test
  void findAllFileNameByDependencyNameAndDependencyId_success() {
    when(repository.findAllByDependencyNameAndDependencyId(anyString(), anyString())).thenReturn(List.of());

    List<FileStorageEntity> result = service.findAllFileNameByDependencyNameAndDependencyId("dep", "123");

    assertNotNull(result);
    verify(repository, times(1)).findAllByDependencyNameAndDependencyId("dep", "123");
  }

  @Test
  void deleteAllByDependencyNameAndDependencyId_success() {
    String dependencyName = "sampleDependencyName";
    String dependencyId = "sampleDependencyId";

    service.deleteAllByDependencyNameAndDependencyId(dependencyName, dependencyId);

    verify(repository, times(1)).deleteAllByDependencyNameAndDependencyId(dependencyName, dependencyId);
  }

  @Test
  void uploadMultipleFiles_error_totalSizeExceeded() throws IOException {
    // Setup mock configuration
    Long emailMaxTotalSize = 10L * 1024 * 1024; // 10 MB
    ReflectionTestUtils.setField(service, "emailMaxTotalSize", emailMaxTotalSize);
    ReflectionTestUtils.setField(service, "maxFileSize", 5L * 1024 * 1024); // 5 MB

    // Existing files already 9 MB
    List<FileStorageModel> fileStorages = List.of(
         FileStorageModel.builder().id(1L).size(4L * 1024 * 1024).build(),
         FileStorageModel.builder().id(2L).size(5L * 1024 * 1024).build()
    );

    // Uploading a new 2 MB file → total: 11 MB > 10 MB
    MockMultipartFile fileToUpload = new MockMultipartFile(
        "file",
        "test.txt",
        "text/plain",
        new byte[2 * 1024 * 1024]
    );

    List<MultipartFile> fileUploads = List.of(fileToUpload);

    // Assert throws BusinessException
    BusinessException ex = assertThrows(BusinessException.class, () -> {
      service.uploadMultipleFiles(
          fileStorages,
          fileUploads,
          "testDep",
          "123"
      );
    });

    assertEquals(ErrorCode.FILE_SIZE_TOTAL_ERROR.getCode(), ex.getCode());
  }

  @Test
  void uploadMultipleFiles_error_invalidFileName() {
    MultipartFile file = mock(MultipartFile.class);
    when(file.getOriginalFilename()).thenReturn(null);

    List<MultipartFile> files = List.of(file);

    BusinessException exception = assertThrows(BusinessException.class,
        () -> service.uploadMultipleFiles(List.of(), files, "dep", "123"));

    assertEquals(ErrorCode.INVALID_FILE_NAME.getCode(), exception.getCode());
  }

  @Test
  void readFile_error_fileNotFound() {
    when(s3FileService.headObject("nonexistent.txt")).thenReturn(false);

    BusinessException exception = assertThrows(BusinessException.class,
        () -> service.readFile("nonexistent.txt"));

    assertEquals(ErrorCode.EXPORT_DATA_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void readFile_success() throws Exception {
    String filePath = "test/file.txt";
    byte[] fileContent = "Hello, unit test!".getBytes();

    when(s3FileService.headObject(filePath)).thenReturn(true);
    when(s3FileService.getObject(filePath)).thenReturn(fileContent);

    StreamingResponseBody result = service.readFile(filePath);

    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    result.writeTo(outputStream);
    String resultContent = outputStream.toString();

    assertEquals("Hello, unit test!", resultContent);
    verify(s3FileService, times(1)).headObject(filePath);
    verify(s3FileService, times(1)).getObject(filePath);
  }

  @Test
  void deleteWithId_success() throws BusinessException {
    Long fileId = 1L;
    FileStorageEntity fileStorage = new FileStorageEntity();
    ReflectionTestUtils.setField(fileStorage, "id", fileId);
    ReflectionTestUtils.setField(fileStorage, "path", "s3://bucket/path/file.txt");

    when(repository.findById(fileId)).thenReturn(Optional.of(fileStorage));
    doNothing().when(s3FileService).deleteObject(anyString());

    service.deleteWithId(fileId);

    verify(repository, times(1)).findById(fileId);
    verify(s3FileService, times(1)).deleteObject("s3://bucket/path/file.txt");
  }

  @Test
  void deleteWithId_error_fileNotFound() {
    Long fileId = 999L;
    when(repository.findById(fileId)).thenReturn(Optional.empty());

    BusinessException exception = assertThrows(BusinessException.class,
        () -> service.deleteWithId(fileId));

    assertEquals(ErrorCode.FILE_DELETE_ERROR.getCode(), exception.getCode());
    verify(repository, times(1)).findById(fileId);
  }

  @Test
  void deleteWithId_success_s3DeleteException() throws BusinessException {
    Long fileId = 1L;
    FileStorageEntity fileStorage = new FileStorageEntity();
    fileStorage.setId(fileId);
    // Use reflection to set path since Lombok might not be working properly in test
    ReflectionTestUtils.setField(fileStorage, "path", "s3://bucket/path/file.txt");

    when(repository.findById(fileId)).thenReturn(Optional.of(fileStorage));
    doThrow(new RuntimeException("S3 delete failed")).when(s3FileService).deleteObject(anyString());

    assertThrows(BusinessException.class, () -> service.deleteWithId(fileId));

    verify(repository, times(1)).findById(fileId);
    verify(s3FileService, times(1)).deleteObject(anyString());
  }
}
