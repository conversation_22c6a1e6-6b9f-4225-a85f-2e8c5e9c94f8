package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;

/**
 * Model request service to create or update service.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RpaConfigRequest {
  String id;
  
  @Max(value = CommonConstants.RPA_MONITOR_WEB_INTERVAL_MAX_VALUE, message = "Interval value has max {value}")
  @Min(value = CommonConstants.RPA_MONITOR_WEB_INTERVAL_MIN_VALUE, message = "Interval value has min {value}")
  @NotNull
  Integer interval;
  
  @Max(value = CommonConstants.RPA_MONITOR_WEB_NUMBER_OF_RETRY_MAX_VALUE,
      message = "Number of retry value has max {value}")
  @Min(value = CommonConstants.RPA_MONITOR_WEB_NUMBER_OF_RETRY_MIN_VALUE,
      message = "Number of retry value has min {value}")
  @NotNull
  Integer numberOfRetry;
  
  boolean active;
  
}
