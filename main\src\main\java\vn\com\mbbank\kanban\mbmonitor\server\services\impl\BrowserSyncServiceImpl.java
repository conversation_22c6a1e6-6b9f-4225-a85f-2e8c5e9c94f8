package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.util.EnumSet;
import java.util.Set;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.mbmonitor.server.services.BrowserSyncService;

@Service
@RequiredArgsConstructor
@Slf4j
public class BrowserSyncServiceImpl implements BrowserSyncService {
  
  private static final String BASE_PATH = "browsers/";
  private static final String DRIVER_BASE_PATH = "driver/";
  private static final String CHROME_SUB_FOLDER = "chromium-1178";
  private static final String FIREFOX_SUB_FOLDER = "firefox-1487";
  
  @Value("${mbmonitor.monitor.web.resource.chromium}")
  private String chromiumZipResource;
  
  @Value("${mbmonitor.monitor.web.resource.firefox}")
  private String firefoxZipResource;
  
  @Value("${mbmonitor.monitor.web.resource.driver}")
  private String driverResource;
  
  @Value("${mbmonitor.monitor.web.playwright.path}")
  private String expectPath;
  
  @Override
  public String checkAndSyncBrowsers() throws IOException {
    StringBuilder result = new StringBuilder();
    result.append(syncZipAndExtract(BASE_PATH + chromiumZipResource, chromiumZipResource, CHROME_SUB_FOLDER));
    result.append("\n");
    result.append(syncZipAndExtract(BASE_PATH + firefoxZipResource, firefoxZipResource, FIREFOX_SUB_FOLDER));
    result.append("\n");
    result.append(syncZipAndExtract(DRIVER_BASE_PATH + driverResource, driverResource, ""));
    return result.toString();
  }
  
  /**
   * Copy a zip file from resources into expectPath, then unzip into expectPath
   *
   * @param resourcePath classpath location of the ZIP file (e.g. browsers/chromium-linux.zip)
   * @param zipFileName  desired filename for saving into expectPath
   */
  private String syncZipAndExtract(String resourcePath, String zipFileName, String subFolderName) throws IOException {
    // Define target directory where the zip will be extracted
    Path zipTargetDir = Paths.get(expectPath, subFolderName);
    // Define a full path to the destination zip file
    Path zipTargetPath = zipTargetDir.resolve(zipFileName);
    
    // If the target directory already contains files (i.e., already extracted), skip the operation
    if (Files.exists(zipTargetDir) && Files.exists(zipTargetPath)) {
      // Ensure permissions on entire extracted directory (even if existed before)
      applyPermissionsRecursively(zipTargetDir);
      Set<PosixFilePermission> perms = Files.getPosixFilePermissions(zipTargetDir);
      String permsStr = PosixFilePermissions.toString(perms);
      log.info("Folder {} permissions: {}", zipTargetDir, permsStr);
      return String.format("Already extracted to %s", zipTargetDir);
    }
    
    // Ensure target directory exists
    Files.createDirectories(zipTargetDir);
    
    // Copy the zip file from classpath to the target location if it doesn't already exist
    if (!Files.exists(zipTargetPath)) {
      ClassPathResource resource = new ClassPathResource(resourcePath);
      if (!resource.exists()) {
        return String.format("Resource not found: %s", resourcePath);
      }
      
      try (InputStream in = resource.getInputStream()) {
        Files.copy(in, zipTargetPath, StandardCopyOption.REPLACE_EXISTING);
      }
    }
    
    // Extract the zip file to the target directory
    unzip(zipTargetPath, zipTargetDir);
    
    return String.format("Copied & extracted %s to %s", zipFileName, zipTargetDir);
  }
  
  /**
   * Unzips the specified ZIP file to the given destination folder
   * and sets full permissions (chmod 777) on all extracted files and directories.
   */
  private void unzip(Path zipFile, Path destDir) throws IOException {
    try (ZipInputStream zis = new ZipInputStream(Files.newInputStream(zipFile))) {
      ZipEntry zipEntry;
      while ((zipEntry = zis.getNextEntry()) != null) {
        Path newPath = resolveZipEntry(destDir, zipEntry);
        if (zipEntry.isDirectory()) {
          Files.createDirectories(newPath);
          setFullPermissions(newPath);
        } else {
          Files.createDirectories(newPath.getParent());
          Files.copy(zis, newPath, StandardCopyOption.REPLACE_EXISTING);
          setFullPermissions(newPath);
        }
      }
    }
  }
  
  /**
   * Prevents zip slip attacks by validating entry paths.
   */
  private Path resolveZipEntry(Path destDir, ZipEntry zipEntry) throws IOException {
    Path resolvedPath = destDir.resolve(zipEntry.getName()).normalize();
    if (!resolvedPath.startsWith(destDir)) {
      throw new IOException("Entry outside target dir: " + zipEntry.getName());
    }
    return resolvedPath;
  }
  
  /**
   * Sets full POSIX permissions (chmod 777) on the given path.
   * Note: Only works on Unix-like systems that support POSIX file attributes.
   */
  private void setFullPermissions(Path path) {
    try {
      Set<PosixFilePermission> perms = EnumSet.of(
          PosixFilePermission.OWNER_READ, PosixFilePermission.OWNER_WRITE, PosixFilePermission.OWNER_EXECUTE,
          PosixFilePermission.GROUP_READ, PosixFilePermission.GROUP_WRITE, PosixFilePermission.GROUP_EXECUTE,
          PosixFilePermission.OTHERS_READ, PosixFilePermission.OTHERS_WRITE, PosixFilePermission.OTHERS_EXECUTE
      );
      Files.setPosixFilePermissions(path, perms);
    } catch (UnsupportedOperationException | IOException e) {
      log.warn("Could not set permissions for path {}: {}", path, e.getMessage());
    }
  }
  
  private void applyPermissionsRecursively(Path root) {
    try {
      Files.walk(root).forEach(this::setFullPermissions);
    } catch (IOException e) {
      log.warn("Failed to apply permissions recursively on {}: {}", root, e.getMessage());
    }
  }
}
