package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import java.util.ArrayList;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.ConfigDependencyLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.MaintenanceTimeConfigLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MaintenanceTimeConfigLogModelMapper extends
    KanbanBaseMapper<MaintenanceTimeConfigLogModel, MaintenanceTimeConfigEntity> {
  MaintenanceTimeConfigLogModelMapper INSTANCE = Mappers.getMapper(MaintenanceTimeConfigLogModelMapper.class);

  /**
   * map MaintenanceTimeConfigEntity to MaintenanceTimeConfigLogModel.
   *
   * @param config                        AlertGroupConfigEntity.
   * @param serviceDependencies           services
   * @param serviceWithAllAppDependencies services with all app
   * @param applicationDependencies       applications
   * @return MaintenanceTimeConfigLogModel
   */
  default MaintenanceTimeConfigLogModel map(MaintenanceTimeConfigEntity config,
                                            List<ServiceEntity> serviceDependencies,
                                            List<ServiceEntity> serviceWithAllAppDependencies,
                                            List<ApplicationEntity> applicationDependencies) {
    var serviceNames = new ArrayList<String>();
    var applicationNames = new ArrayList<String>();
    for (ServiceEntity service : serviceDependencies) {
      serviceNames.add(service.getName());
    }
    for (ServiceEntity service : serviceWithAllAppDependencies) {
      serviceNames.add(service.getName() + " (All application)");
    }
    for (ApplicationEntity application : applicationDependencies) {
      applicationNames.add(application.getName());
    }

    var dependency = ConfigDependencyLogModel.builder().services(serviceNames).applications(applicationNames).build();

    var res = MaintenanceTimeConfigLogModel.builder()
        .name(config.getName())
        .description(config.getDescription())
        .dependency(dependency)
        .type(config.getType())
        .condition(config.getRuleGroup().toString())
        .active(config.getActive());
    if (MaintenanceTimeConfigTypeEnum.CRON_JOB.equals(config.getType())) {
      res.cronExpression(config.getCronExpression());
    } else if (MaintenanceTimeConfigTypeEnum.FROM_TIME_TO_TIME.equals(config.getType())) {
      res.startTime(DateUtils.formatDate(config.getStartTime())).endTime(DateUtils.formatDate(config.getEndTime()));
    } else {
      res.nextTime(config.getNextTime()).unit(config.getUnit());
    }
    return res.build();
  }

  /**
   * map MaintenanceTimeConfigEntity to MaintenanceTimeConfigLogModel.
   *
   * @param config                        AlertGroupConfigEntity.
   * @param serviceDependencies           services
   * @param serviceWithAllAppDependencies services with all app
   * @param applicationDependencies       applications
   * @param logRuleGroup                  rule group for syslog
   *
   * @return MaintenanceTimeConfigLogModel
   */
  default MaintenanceTimeConfigLogModel map(MaintenanceTimeConfigEntity config,
                                                  List<ServiceEntity> serviceDependencies,
                                                  List<ServiceEntity> serviceWithAllAppDependencies,
                                                  List<ApplicationEntity> applicationDependencies,
                                                  String logRuleGroup) {
    var res = map(config, serviceDependencies, serviceWithAllAppDependencies, applicationDependencies);
    if (!KanbanCommonUtil.isNullOrEmpty(logRuleGroup)) {
      res.setCondition(logRuleGroup);
    }
    return res;
  }
}
