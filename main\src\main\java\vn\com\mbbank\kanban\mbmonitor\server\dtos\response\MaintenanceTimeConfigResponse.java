package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeUnitEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

/**
 * Response DTO for representing maintenance time configurations.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MaintenanceTimeConfigResponse {
  Long id;
  String name;
  String description;
  @Builder.Default
  Boolean active = true;
  MaintenanceTimeConfigTypeEnum type;
  Integer nextTime;
  MaintenanceTimeUnitEnum unit;
  String cronExpression;
  Date startTime;
  Date endTime;
  RuleGroupType ruleGroup;
  String ruleGroupColumn;
  @Builder.Default
  List<ServiceResponse> services = new ArrayList<>();
  @Builder.Default
  List<ApplicationResponse> applications = new ArrayList<>();
  String status;
  String value;
  Date createdDate;
}
