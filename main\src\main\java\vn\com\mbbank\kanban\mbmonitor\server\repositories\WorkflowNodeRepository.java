package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowNodeEntity;

/**
 * Repository WorkflowTemplateNodeRepository.
 */
@Repository
public interface WorkflowNodeRepository
    extends JpaCommonRepository<WorkflowNodeEntity, String> {

  /**
   * delete node by workflowId.
   *
   * @param workflowId workflowId
   * @return number of result
   */
  int deleteAllByWorkflowId(String workflowId);

  /**
   * find all nodes by workflowId.
   *
   * @param workflowId workflowId
   * @return list of WorkflowNodeEntity
   */
  List<WorkflowNodeEntity> findAllByWorkflowId(String workflowId);

}
