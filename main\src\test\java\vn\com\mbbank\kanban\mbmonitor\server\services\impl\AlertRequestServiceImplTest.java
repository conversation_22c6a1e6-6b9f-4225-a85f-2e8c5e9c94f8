package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.constants.ProseMirrorMessageConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.AlertRequestDatabaseRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.AlertRequestRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.AlertRequestDatabaseResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.AlertRequestResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertRequestEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysPermissionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertRequestSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertRequestStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.PushNotificationService;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonUserService;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AlertRequestRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertRequestDatabaseService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.DatabaseThresholdConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;
import vn.com.mbbank.kanban.mbmonitor.server.services.SysPermissionService;
import vn.com.mbbank.kanban.test.WithMockMbStaff;
import java.nio.file.Files;
import java.text.MessageFormat;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AlertRequestServiceImplTest {

  private final String currentUser = "kanban";
  private final String alertRequestId = "testAlertRequestId";
  @Mock
  AlertRequestRepository alertRequestRepository;
  @Mock
  ServiceService serviceService;
  @Mock
  ApplicationService applicationService;

  @Mock
  AlertPriorityConfigService alertPriorityConfigService;
  @Mock
  AlertRequestDatabaseService alertRequestDatabaseService;
  @Mock
  DatabaseThresholdConfigService databaseThresholdConfigService;
  @Mock
  PushNotificationService pushNotificationService;
  @Mock
  CommonAclPermissionService commonAclPermissionService;
  @Mock
  SysPermissionService sysPermissionService;
  @Mock
  CommonUserService commonUserService; // Added mock for CommonUserService
  @InjectMocks
  AlertRequestServiceImpl alertRequestService;
  private MockedStatic<MessageFormat> mockedMessageFormat;

  @BeforeEach
  void setUp() {
    mockedMessageFormat = mockStatic(MessageFormat.class);
  }

  @AfterEach
  void tearDown() {
    mockedMessageFormat.close();
  }

  @Test
  void getRepository_success() {
    JpaCommonRepository<AlertRequestEntity, String> result =
        alertRequestService.getRepository();
    assertEquals(alertRequestRepository, result);
  }

  // Tests for createOrUpdate method
  @WithMockMbStaff
  @Test
  void createOrUpdate_createMode_success_databaseSource() throws BusinessException {
    try (MockedStatic<ProseMirrorMessageConstants> pMock = mockStatic(ProseMirrorMessageConstants.class)) {
      pMock.when(() -> ProseMirrorMessageConstants.submittedAlertRequest(any(), any())).thenReturn("2222");
      AlertRequestRequest request = new AlertRequestRequest();
      request.setSourceType(AlertRequestSourceTypeEnum.DATABASE);
      request.setAlertRequestDatabase(new AlertRequestDatabaseRequest());

      AlertRequestEntity mappedEntity = new AlertRequestEntity();
      mappedEntity.setId(alertRequestId);
      mappedEntity.setStatus(AlertRequestStatusEnum.WAITING_APPROVAL);

      AlertRequestDatabaseResponse dbResponse = new AlertRequestDatabaseResponse();

      when(alertRequestRepository.save(any(AlertRequestEntity.class))).thenReturn(mappedEntity);
      when(alertRequestDatabaseService.save(any(AlertRequestDatabaseRequest.class))).thenReturn(dbResponse);
      when(serviceService.findById(any())).thenReturn(new ServiceEntity());
      when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
      when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
      AlertRequestResponse result = alertRequestService.createOrUpdate(request);

      assertNotNull(result);
      verify(alertRequestDatabaseService, times(1)).deleteByAlertRequestId(eq(alertRequestId));
      verify(alertRequestDatabaseService, times(1)).save(any(AlertRequestDatabaseRequest.class));
      // Notification is sent for WAITING_APPROVAL status from NEW
      verify(pushNotificationService, times(1)).push(any(NotificationRequest.class));
    }
  }

  @Test
  @WithMockMbStaff
  void createOrUpdate_updateMode_success_databaseSource_rejectedStatus() throws BusinessException {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);
    request.setSourceType(AlertRequestSourceTypeEnum.DATABASE);
    request.setStatus(AlertRequestStatusEnum.REJECTED);
    request.setAlertRequestDatabase(new AlertRequestDatabaseRequest());

    AlertRequestEntity oldEntity = new AlertRequestEntity();
    oldEntity.setRejectedReason("oldReason");
    oldEntity.setStatus(AlertRequestStatusEnum.NEW);
    oldEntity.setCreatedBy(currentUser);

    AlertRequestEntity mappedEntity = new AlertRequestEntity();
    mappedEntity.setId(alertRequestId);
    mappedEntity.setStatus(AlertRequestStatusEnum.REJECTED);

    AlertRequestDatabaseResponse dbResponse = new AlertRequestDatabaseResponse();

    when(alertRequestRepository.save(any(AlertRequestEntity.class))).thenReturn(mappedEntity);
    when(alertRequestDatabaseService.save(any(AlertRequestDatabaseRequest.class))).thenReturn(dbResponse);
    when(alertRequestRepository.findById(anyString())).thenReturn(Optional.of(oldEntity));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
    AlertRequestResponse result = alertRequestService.createOrUpdate(request);

    assertNotNull(result);
    verify(alertRequestDatabaseService, times(1)).deleteByAlertRequestId(eq(alertRequestId));
    verify(alertRequestDatabaseService, times(1)).save(any(AlertRequestDatabaseRequest.class));
    // Notification is not sent for REJECTED status in createOrUpdate
    verify(pushNotificationService, times(0)).push(any(NotificationRequest.class));
  }

  @Test
  @WithMockMbStaff
  void createOrUpdate_updateMode_success_databaseSource_waitingApprovalStatus_fromRejected() throws BusinessException {
    try (MockedStatic<ProseMirrorMessageConstants> pMock = mockStatic(ProseMirrorMessageConstants.class)) {
      pMock.when(() -> ProseMirrorMessageConstants.submittedAlertRequest(any(), any())).thenReturn("2222");
      AlertRequestRequest request = new AlertRequestRequest();
      request.setId(alertRequestId);
      request.setSourceType(AlertRequestSourceTypeEnum.DATABASE);
      request.setStatus(AlertRequestStatusEnum.WAITING_APPROVAL);
      request.setAlertRequestDatabase(new AlertRequestDatabaseRequest());

      AlertRequestEntity oldEntity = new AlertRequestEntity();
      oldEntity.setStatus(AlertRequestStatusEnum.REJECTED);
      oldEntity.setCreatedBy(currentUser);

      AlertRequestEntity mappedEntity = new AlertRequestEntity();
      mappedEntity.setId(alertRequestId);
      mappedEntity.setStatus(AlertRequestStatusEnum.WAITING_APPROVAL);

      AlertRequestDatabaseResponse dbResponse = new AlertRequestDatabaseResponse();

      when(alertRequestRepository.save(any(AlertRequestEntity.class))).thenReturn(mappedEntity);
      when(alertRequestDatabaseService.save(any(AlertRequestDatabaseRequest.class))).thenReturn(dbResponse);
      when(alertRequestRepository.findById(anyString())).thenReturn(Optional.of(oldEntity));
      when(serviceService.findById(any())).thenReturn(new ServiceEntity());
      when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
      when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());

      AlertRequestResponse result = alertRequestService.createOrUpdate(request);

      assertNotNull(result);
      // Notification is sent for WAITING_APPROVAL status from REJECTED
      verify(pushNotificationService, times(1)).push(any(NotificationRequest.class));
    }
  }

  @Test
  @WithMockMbStaff
  void createOrUpdate_updateMode_success_databaseSource_waitingApprovalStatus_fromNew() throws BusinessException {
    try (MockedStatic<ProseMirrorMessageConstants> pMock = mockStatic(ProseMirrorMessageConstants.class)) {
      pMock.when(() -> ProseMirrorMessageConstants.submittedAlertRequest(any(), any())).thenReturn("2222");
      AlertRequestRequest request = new AlertRequestRequest();
      request.setId(alertRequestId);
      request.setSourceType(AlertRequestSourceTypeEnum.DATABASE);
      request.setStatus(AlertRequestStatusEnum.WAITING_APPROVAL);
      request.setAlertRequestDatabase(new AlertRequestDatabaseRequest());

      AlertRequestEntity oldEntity = new AlertRequestEntity();
      oldEntity.setStatus(AlertRequestStatusEnum.NEW);
      oldEntity.setCreatedBy(currentUser);
      AlertRequestEntity mappedEntity = new AlertRequestEntity();
      mappedEntity.setId(alertRequestId);
      mappedEntity.setStatus(AlertRequestStatusEnum.WAITING_APPROVAL);

      AlertRequestDatabaseResponse dbResponse = new AlertRequestDatabaseResponse();

      when(alertRequestRepository.save(any(AlertRequestEntity.class))).thenReturn(mappedEntity);
      when(alertRequestDatabaseService.save(any(AlertRequestDatabaseRequest.class))).thenReturn(dbResponse);
      when(alertRequestRepository.findById(anyString())).thenReturn(Optional.of(oldEntity));
      when(serviceService.findById(any())).thenReturn(new ServiceEntity());
      when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
      when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
      AlertRequestResponse result = alertRequestService.createOrUpdate(request);

      assertNotNull(result);
      // Notification is sent for WAITING_APPROVAL status from NEW
      verify(pushNotificationService, times(1)).push(any(NotificationRequest.class));
    }
  }

  @Test
  @WithMockMbStaff
  void createOrUpdate_updateMode_success_databaseSource_noNotification() throws BusinessException {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);
    request.setSourceType(AlertRequestSourceTypeEnum.DATABASE);
    request.setStatus(AlertRequestStatusEnum.NEW); // Status is APPROVED, no notification
    request.setAlertRequestDatabase(new AlertRequestDatabaseRequest());

    AlertRequestEntity oldEntity = new AlertRequestEntity();
    oldEntity.setStatus(AlertRequestStatusEnum.NEW);
    oldEntity.setCreatedBy(currentUser);
    AlertRequestEntity mappedEntity = new AlertRequestEntity();
    mappedEntity.setId(alertRequestId);
    mappedEntity.setStatus(AlertRequestStatusEnum.APPROVED);

    AlertRequestDatabaseResponse dbResponse = new AlertRequestDatabaseResponse();

    when(alertRequestRepository.save(any(AlertRequestEntity.class))).thenReturn(mappedEntity);
    when(alertRequestDatabaseService.save(any(AlertRequestDatabaseRequest.class))).thenReturn(dbResponse);
    when(alertRequestRepository.findById(anyString())).thenReturn(Optional.of(oldEntity));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
    AlertRequestResponse result = alertRequestService.createOrUpdate(request);

    assertNotNull(result);
    // Notification is not sent for APPROVED status in createOrUpdate
    verify(pushNotificationService, times(0)).push(any(NotificationRequest.class));
  }


  @Test
  @WithMockMbStaff
  void validateSaveRequest_createMode_success() throws BusinessException {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setServiceId("serviceId");
    request.setApplicationId("appId");
    request.setPriorityId(1L);
    request.setSourceType(AlertRequestSourceTypeEnum.DATABASE);
    request.setAlertRequestDatabase(new AlertRequestDatabaseRequest());

    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());

    AlertRequestEntity result = alertRequestService.validateSaveRequest(request);

    assertNull(result);
    verify(serviceService, times(1)).findById(eq("serviceId"));
    verify(applicationService, times(1)).findById(eq("appId"));
    verify(alertPriorityConfigService, times(1)).findById(eq(1L));
  }

  @Test
  @WithMockMbStaff
  void validateSaveRequest_updateMode_success_isOwner() throws BusinessException {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);
    request.setServiceId("serviceId");
    request.setApplicationId("appId");
    request.setPriorityId(1L);
    request.setSourceType(AlertRequestSourceTypeEnum.DATABASE);
    request.setAlertRequestDatabase(new AlertRequestDatabaseRequest());

    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setStatus(AlertRequestStatusEnum.NEW);
    existingEntity.setCreatedBy(currentUser);

    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));
    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());

    AlertRequestEntity result = alertRequestService.validateSaveRequest(request);

    assertNotNull(result);
    assertEquals(existingEntity, result);
    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
    verify(serviceService, times(1)).findById(eq("serviceId"));
    verify(applicationService, times(1)).findById(eq("appId"));
    verify(alertPriorityConfigService, times(1)).findById(eq(1L));
  }

  @Test
  @WithMockMbStaff
  void validateSaveRequest_updateMode_typeELK_success_isOwner() throws BusinessException {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);
    request.setServiceId("serviceId");
    request.setApplicationId("appId");
    request.setPriorityId(1L);
    request.setSourceType(AlertRequestSourceTypeEnum.DATABASE);
    request.setAlertRequestDatabase(null);

    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setStatus(AlertRequestStatusEnum.NEW);
    existingEntity.setCreatedBy(currentUser);

    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));
    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());

    AlertRequestEntity result = alertRequestService.validateSaveRequest(request);

    assertNotNull(result);
    assertEquals(existingEntity, result);
    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
    verify(serviceService, times(1)).findById(eq("serviceId"));
    verify(applicationService, times(1)).findById(eq("appId"));
    verify(alertPriorityConfigService, times(1)).findById(eq(1L));
  }

  @Test
  @WithMockMbStaff
  void validateSaveRequest_updateMode_success_hasApprovePermission() throws BusinessException {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);
    request.setServiceId("serviceId");
    request.setApplicationId("appId");
    request.setPriorityId(1L);
    request.setSourceType(AlertRequestSourceTypeEnum.DATABASE);
    request.setAlertRequestDatabase(new AlertRequestDatabaseRequest());

    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setStatus(AlertRequestStatusEnum.NEW);
    existingEntity.setCreatedBy(currentUser);

    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));
    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(new AlertPriorityConfigEntity());

    AlertRequestEntity result = alertRequestService.validateSaveRequest(request);

    assertNotNull(result);
    assertEquals(existingEntity, result);
    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
    verify(serviceService, times(1)).findById(eq("serviceId"));
    verify(applicationService, times(1)).findById(eq("appId"));
    verify(alertPriorityConfigService, times(1)).findById(eq(1L));
  }

  @Test
  void validateSaveRequest_error_invalidStatusApproved() {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setStatus(AlertRequestStatusEnum.APPROVED);

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.validateSaveRequest(request);
    });

    assertEquals(ErrorCode.ALERT_REQUEST_INVALID_STATUS.getMessage(), exception.getMessage());
  }

  @Test
  void validateSaveRequest_updateMode_error_notFound() {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);

    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.empty());

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.validateSaveRequest(request);
    });

    assertEquals(ErrorCode.ALERT_REQUEST_NOT_FOUND.getMessage(), exception.getMessage());
    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
  }

  @Test
  void validateSaveRequest_updateMode_error_userDontHavePermissions() throws BusinessException {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);

    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setStatus(AlertRequestStatusEnum.WAITING_APPROVAL);
    existingEntity.setCreatedBy("anotherUser");

    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));
    when(commonAclPermissionService.isAnyPermission(any())).thenReturn(false);

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.validateSaveRequest(request);
    });

    assertEquals(ErrorCode.USER_DONT_HAVE_PERMISSIONS.getMessage(), exception.getMessage());
    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
    verify(commonAclPermissionService, times(1)).isAnyPermission(any());
  }

  @Test
  @WithMockMbStaff
  void validateSaveRequest_updateMode_error_hasBeenApproved() {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);

    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setStatus(AlertRequestStatusEnum.APPROVED);
    existingEntity.setCreatedBy(currentUser);

    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.validateSaveRequest(request);
    });

    assertEquals(ErrorCode.ALERT_REQUEST_HAS_BEEN_APPROVED.getMessage(), exception.getMessage());
    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
  }

  @Test
  @WithMockMbStaff
  void validateSaveRequest_updateMode_error_unauthorizedAccess_newStatus() throws BusinessException {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);

    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setStatus(AlertRequestStatusEnum.NEW);
    existingEntity.setCreatedBy("anotherUser");

    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));
    when(commonAclPermissionService.isAnyPermission(any())).thenReturn(true);

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.validateSaveRequest(request);
    });

    assertEquals(ErrorCode.ALERT_REQUEST_UNAUTHORIZED_ACCESS.getMessage(), exception.getMessage());
    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
    verify(commonAclPermissionService, times(1)).isAnyPermission(any());
  }

  @Test
  @WithMockMbStaff
  void validateSaveRequest_updateMode_error_unauthorizedAccess_rejectedStatus() throws BusinessException {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);

    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setStatus(AlertRequestStatusEnum.REJECTED);
    existingEntity.setCreatedBy("anotherUser");

    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));
    when(commonAclPermissionService.isAnyPermission(any())).thenReturn(true);

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.validateSaveRequest(request);
    });

    assertEquals(ErrorCode.ALERT_REQUEST_UNAUTHORIZED_ACCESS.getMessage(), exception.getMessage());
    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
    verify(commonAclPermissionService, times(1)).isAnyPermission(any());
  }

  @Test
  @WithMockMbStaff
  void validateSaveRequest_updateMode_error_waitingApprovalForbidden() {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);

    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setStatus(AlertRequestStatusEnum.WAITING_APPROVAL);
    existingEntity.setCreatedBy(currentUser);

    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.validateSaveRequest(request);
    });

    assertEquals(ErrorCode.ALERT_REQUEST_WAITING_APPROVAL_FORBIDDEN.getMessage(), exception.getMessage());
    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
  }

  @Test
  @WithMockMbStaff
  void validateSaveRequest_error_invalidNameDatabaseConfig() {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setSourceType(AlertRequestSourceTypeEnum.DATABASE);
    AlertRequestDatabaseRequest dbRequest = new AlertRequestDatabaseRequest();
    dbRequest.setName("configName");
    request.setAlertRequestDatabase(dbRequest);


    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.validateSaveRequest(request);
    });

    assertEquals(ErrorCode.ALERT_REQUEST_INVALID_NAME_DATABASE_CONFIG.getMessage(), exception.getMessage());
  }

  @Test
  @WithMockMbStaff
  void validateSaveRequest_error_invalidService() {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setServiceId("serviceId");
    request.setApplicationId("appId");
    request.setPriorityId(1L);
    request.setSourceType(AlertRequestSourceTypeEnum.DATABASE);
    request.setAlertRequestDatabase(new AlertRequestDatabaseRequest());

    when(serviceService.findById(anyString())).thenReturn(null);

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.validateSaveRequest(request);
    });

    assertEquals(ErrorCode.ALERT_REQUEST_INVALID_SERVICE.getMessage(), exception.getMessage());
    verify(serviceService, times(1)).findById(eq("serviceId"));
  }

  @Test
  @WithMockMbStaff
  void validateSaveRequest_error_invalidApplication() {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setServiceId("serviceId");
    request.setApplicationId("appId");
    request.setPriorityId(1L);
    request.setSourceType(AlertRequestSourceTypeEnum.DATABASE);
    request.setAlertRequestDatabase(new AlertRequestDatabaseRequest());

    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(null);

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.validateSaveRequest(request);
    });

    assertEquals(ErrorCode.ALERT_REQUEST_INVALID_APPLICATION.getMessage(), exception.getMessage());
    verify(serviceService, times(1)).findById(eq("serviceId"));
    verify(applicationService, times(1)).findById(eq("appId"));
  }

  @Test
  void validateSaveRequest_error_invalidPriority() {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setServiceId("serviceId");
    request.setApplicationId("appId");
    request.setPriorityId(1L);
    request.setSourceType(AlertRequestSourceTypeEnum.DATABASE);
    request.setAlertRequestDatabase(new AlertRequestDatabaseRequest());

    when(serviceService.findById(anyString())).thenReturn(new ServiceEntity());
    when(applicationService.findById(anyString())).thenReturn(new ApplicationEntity());
    when(alertPriorityConfigService.findById(anyLong())).thenReturn(null);

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.validateSaveRequest(request);
    });

    assertEquals(ErrorCode.ALERT_REQUEST_INVALID_PRIORITY.getMessage(), exception.getMessage());
    verify(serviceService, times(1)).findById(eq("serviceId"));
    verify(applicationService, times(1)).findById(eq("appId"));
    verify(alertPriorityConfigService, times(1)).findById(eq(1L));
  }

  // Tests for findWithId method
  @Test
  void findWithId_success() {
    AlertRequestResponse mockResponse = new AlertRequestResponse();
    AlertRequestDatabaseResponse mockDbResponse = new AlertRequestDatabaseResponse();

    when(alertRequestRepository.findWithId(eq(alertRequestId))).thenReturn(mockResponse);
    when(alertRequestDatabaseService.findByAlertRequestId(eq(alertRequestId))).thenReturn(mockDbResponse);

    AlertRequestResponse result = alertRequestService.findWithId(alertRequestId);

    assertNotNull(result);
    assertEquals(mockDbResponse, result.getAlertRequestDatabase());
    verify(alertRequestRepository, times(1)).findWithId(eq(alertRequestId));
    verify(alertRequestDatabaseService, times(1)).findByAlertRequestId(eq(alertRequestId));
  }

  // Tests for findAll method
  @Test
  void findAll_success() {
    PaginationRequestDTO paginationRequest = new PaginationRequestDTO();
    Page<AlertRequestResponse> mockPage = new PageImpl<>(Collections.emptyList());

    when(alertRequestRepository.findAll(eq(paginationRequest))).thenReturn(mockPage);

    Page<AlertRequestResponse> result = alertRequestService.findAll(paginationRequest);

    assertNotNull(result);
    assertEquals(mockPage, result);
    verify(alertRequestRepository, times(1)).findAll(eq(paginationRequest));
  }

  // Tests for approve method
  @Test
  @WithMockMbStaff
  void approve_success_databaseSource() throws BusinessException {
    try (MockedStatic<ProseMirrorMessageConstants> pMock = mockStatic(ProseMirrorMessageConstants.class)) {
      pMock.when(() -> ProseMirrorMessageConstants.approvedAlertRequest(any(), any())).thenReturn("2222");
      AlertRequestRequest request = new AlertRequestRequest();
      request.setId(alertRequestId);
      request.setSourceType(AlertRequestSourceTypeEnum.DATABASE);
      request.setAlertRequestDatabase(new AlertRequestDatabaseRequest());

      AlertRequestEntity existingEntity = new AlertRequestEntity();
      existingEntity.setId(alertRequestId);
      existingEntity.setStatus(AlertRequestStatusEnum.WAITING_APPROVAL);
      existingEntity.setCreatedBy("ownerUser");

      AlertRequestEntity savedEntity = new AlertRequestEntity();
      savedEntity.setId(alertRequestId);
      savedEntity.setStatus(AlertRequestStatusEnum.APPROVED);

      AlertRequestResponse mappedResponse = new AlertRequestResponse();
      AlertRequestDatabaseResponse dbResponse = new AlertRequestDatabaseResponse();

      when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));
      when(alertRequestRepository.save(any(AlertRequestEntity.class))).thenReturn(savedEntity);
      when(alertRequestDatabaseService.save(any(AlertRequestDatabaseRequest.class))).thenReturn(dbResponse);

      AlertRequestResponse result = alertRequestService.approve(request);

      assertNotNull(result);
      assertEquals(AlertRequestStatusEnum.APPROVED, savedEntity.getStatus());
      verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
      verify(alertRequestRepository, times(1)).save(any(AlertRequestEntity.class));
      verify(alertRequestDatabaseService, times(1)).deleteByAlertRequestId(eq(alertRequestId));
      verify(alertRequestDatabaseService, times(1)).save(any(AlertRequestDatabaseRequest.class));
      verify(databaseThresholdConfigService, times(1)).createOrUpdate(any());
      verify(pushNotificationService, times(1)).push(any(NotificationRequest.class));
    }
  }
  @Test
  @WithMockMbStaff
  void approve_success_elkSource() throws BusinessException {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);
    request.setSourceType(AlertRequestSourceTypeEnum.ELK);
    request.setAlertRequestDatabase(new AlertRequestDatabaseRequest());

    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setStatus(AlertRequestStatusEnum.WAITING_APPROVAL);
    existingEntity.setCreatedBy("ownerUser");

    AlertRequestEntity savedEntity = new AlertRequestEntity();
    savedEntity.setId(alertRequestId);
    savedEntity.setStatus(AlertRequestStatusEnum.APPROVED);


    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));
    when(alertRequestRepository.save(any(AlertRequestEntity.class))).thenReturn(savedEntity);

    AlertRequestResponse result = alertRequestService.approve(request);

    assertNull(result);
  }

  @Test
  void approve_error_idIsNull() {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(null);


    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.approve(request);
    });

    assertEquals(ErrorCode.ALERT_REQUEST_NOT_FOUND.getMessage(), exception.getMessage());
  }

  @Test
  void approve_error_notFound() {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);

    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.empty());

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.approve(request);
    });

    assertEquals(ErrorCode.ALERT_REQUEST_NOT_FOUND.getMessage(), exception.getMessage());
    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
  }

  @Test
  void approve_error_cannotBeApproved_newStatus() {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);

    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setStatus(AlertRequestStatusEnum.NEW);

    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.approve(request);
    });

    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
  }

  @Test
  void approve_error_cannotBeApproved_approvedStatus() {
    AlertRequestRequest request = new AlertRequestRequest();
    request.setId(alertRequestId);

    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setStatus(AlertRequestStatusEnum.APPROVED);

    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.approve(request);
    });

    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
  }

  // Tests for rejectById method
  @Test
  void rejectById_success() throws BusinessException {
    try (MockedStatic<ProseMirrorMessageConstants> pMock = mockStatic(ProseMirrorMessageConstants.class)) {
      pMock.when(() -> ProseMirrorMessageConstants.rejectedAlertRequest(any(), any(), any())).thenReturn("2222");
      String rejectedReason = "testReason";
      AlertRequestEntity existingEntity = new AlertRequestEntity();
      existingEntity.setId(alertRequestId);
      existingEntity.setStatus(AlertRequestStatusEnum.WAITING_APPROVAL);
      existingEntity.setCreatedBy("ownerUser");

      when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));
      when(alertRequestRepository.save(any(AlertRequestEntity.class))).thenReturn(existingEntity);

      assertDoesNotThrow(() -> alertRequestService.rejectById(alertRequestId, rejectedReason));

      assertEquals(AlertRequestStatusEnum.REJECTED, existingEntity.getStatus());
      assertEquals(rejectedReason, existingEntity.getRejectedReason());
      verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
      verify(alertRequestRepository, times(1)).save(any(AlertRequestEntity.class));
      verify(pushNotificationService, times(1)).push(any(NotificationRequest.class));
    }
  }

  @Test
  void rejectById_error_notFound() {
    String rejectedReason = "testReason";
    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.empty());

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.rejectById(alertRequestId, rejectedReason);
    });

    assertEquals(ErrorCode.ALERT_REQUEST_NOT_FOUND.getMessage(), exception.getMessage());
    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
  }

  @Test
  void rejectById_error_cannotBeRejected() {
    String rejectedReason = "testReason";
    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setStatus(AlertRequestStatusEnum.APPROVED); // Not WAITING_APPROVAL

    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.rejectById(alertRequestId, rejectedReason);
    });

    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
  }

  // Tests for findAllByServiceId method
  @Test
  void findAllByServiceId_success() {
    String serviceId = "testServiceId";
    List<AlertRequestEntity> mockList = Collections.singletonList(new AlertRequestEntity());

    when(alertRequestRepository.findAllByServiceIdAndStatusNot(eq(serviceId), eq(AlertRequestStatusEnum.APPROVED))).thenReturn(mockList);

    List<AlertRequestEntity> result = alertRequestService.findAllByServiceId(serviceId);

    assertNotNull(result);
    assertEquals(1, result.size());
    verify(alertRequestRepository, times(1)).findAllByServiceIdAndStatusNot(eq(serviceId), eq(AlertRequestStatusEnum.APPROVED));
  }

  // Tests for findAllByApplicationId method
  @Test
  void findAllByApplicationId_success() {
    String appId = "testAppId";
    List<AlertRequestEntity> mockList = Collections.singletonList(new AlertRequestEntity());

    when(alertRequestRepository.findAllByApplicationIdAndStatusNot(eq(appId), eq(AlertRequestStatusEnum.APPROVED))).thenReturn(mockList);

    List<AlertRequestEntity> result = alertRequestService.findAllByApplicationId(appId);

    assertNotNull(result);
    assertEquals(1, result.size());
    verify(alertRequestRepository, times(1)).findAllByApplicationIdAndStatusNot(eq(appId), eq(AlertRequestStatusEnum.APPROVED));
  }

  // Tests for findAllByPriorityId method
  @Test
  void findAllByPriorityId_success() {
    Long priorityId = 1L;
    List<AlertRequestEntity> mockList = Collections.singletonList(new AlertRequestEntity());

    when(alertRequestRepository.findAllByPriorityIdAndStatusNot(eq(priorityId), eq(AlertRequestStatusEnum.APPROVED))).thenReturn(mockList);

    List<AlertRequestEntity> result = alertRequestService.findAllByPriorityId(priorityId);

    assertNotNull(result);
    assertEquals(1, result.size());
    verify(alertRequestRepository, times(1)).findAllByPriorityIdAndStatusNot(eq(priorityId), eq(AlertRequestStatusEnum.APPROVED));
  }

  // Tests for deleteWithId method
  @Test
  void deleteWithId_success_isOwner() throws BusinessException {
    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setCreatedBy(currentUser);

    when(alertRequestRepository.findById(any())).thenReturn(Optional.of(existingEntity));

    assertDoesNotThrow(() -> alertRequestService.deleteWithId(alertRequestId));

    verify(alertRequestRepository, times(1)).findById(any());
  }

  @Test
  void deleteWithId_success_hasApprovePermission() throws BusinessException {
    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setCreatedBy("anotherUser");

    when(alertRequestRepository.findById(any())).thenReturn(Optional.of(existingEntity));
    when(commonAclPermissionService.isAnyPermission(any())).thenReturn(true);

    assertDoesNotThrow(() -> alertRequestService.deleteWithId(alertRequestId));

    verify(alertRequestRepository, times(1)).findById(any());
    verify(commonAclPermissionService, times(1)).isAnyPermission(any());
  }

  @Test
  void deleteWithId_error_notFound() {
    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.empty());

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.deleteWithId(alertRequestId);
    });

    assertEquals(ErrorCode.ALERT_REQUEST_NOT_FOUND.getMessage(), exception.getMessage());
    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
  }

  @Test
  void deleteWithId_error_userDontHavePermissions() throws BusinessException {
    AlertRequestEntity existingEntity = new AlertRequestEntity();
    existingEntity.setId(alertRequestId);
    existingEntity.setCreatedBy("anotherUser");

    when(alertRequestRepository.findById(eq(alertRequestId))).thenReturn(Optional.of(existingEntity));
    when(commonAclPermissionService.isAnyPermission(any())).thenReturn(false);

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      alertRequestService.deleteWithId(alertRequestId);
    });

    assertEquals(ErrorCode.USER_DONT_HAVE_PERMISSIONS.getMessage(), exception.getMessage());
    verify(alertRequestRepository, times(1)).findById(eq(alertRequestId));
    verify(commonAclPermissionService, times(1)).isAnyPermission(any());
  }

  // Tests for sendNotification method using ReflectionTestUtils
  @Test
  void sendNotification_waitingApprovalStatus() {
    try (MockedStatic<ProseMirrorMessageConstants> pMock = mockStatic(ProseMirrorMessageConstants.class)) {
      pMock.when(() -> ProseMirrorMessageConstants.submittedAlertRequest(any(), any())).thenReturn("2222");
      String id = "notificationId";
      AlertRequestStatusEnum status = AlertRequestStatusEnum.WAITING_APPROVAL;
      String owner = "ownerUser";
      String reason = "rejectedReason";

      List<Long> approverRoleIds = Collections.singletonList(1L);

      ReflectionTestUtils.invokeMethod(alertRequestService, "sendNotification", id, status, owner, reason);

      verify(pushNotificationService, times(1)).push(any(NotificationRequest.class));
    }
  }

  @Test
  void sendNotification_approvedStatus() {
    try (MockedStatic<ProseMirrorMessageConstants> pMock = mockStatic(ProseMirrorMessageConstants.class)) {
      pMock.when(() -> ProseMirrorMessageConstants.approvedAlertRequest(any(), any())).thenReturn("2222");
      String id = "notificationId";
      AlertRequestStatusEnum status = AlertRequestStatusEnum.APPROVED;
      String owner = "ownerUser";
      String reason = "rejectedReason";

      ReflectionTestUtils.invokeMethod(alertRequestService, "sendNotification", id, status, owner, reason);

      verify(pushNotificationService, times(1)).push(any(NotificationRequest.class));
    }
    // Further verification of NotificationRequest content can be added if needed
  }

  @Test
  void sendNotification_rejectedStatus() {
    try (MockedStatic<ProseMirrorMessageConstants> pMock = mockStatic(ProseMirrorMessageConstants.class)) {
      pMock.when(() -> ProseMirrorMessageConstants.rejectedAlertRequest(any(), any(), any())).thenReturn("2222");
      String id = "notificationId";
      AlertRequestStatusEnum status = AlertRequestStatusEnum.REJECTED;
      String owner = "ownerUser";
      String reason = "rejectedReason";

      ReflectionTestUtils.invokeMethod(alertRequestService, "sendNotification", id, status, owner, reason);

      verify(pushNotificationService, times(1)).push(any(NotificationRequest.class));
    }
  }

  @Test
  void sendNotification_defaultCase() {
    String id = "notificationId";
    AlertRequestStatusEnum status = AlertRequestStatusEnum.NEW; // Default case
    String owner = "ownerUser";
    String reason = "rejectedReason";

    ReflectionTestUtils.invokeMethod(alertRequestService, "sendNotification", id, status, owner, reason);

    verify(pushNotificationService, times(0)).push(any(NotificationRequest.class));
  }

  // Tests for getApproverRoleIds private method using ReflectionTestUtils
  @Test
  @WithMockMbStaff
  void getApproverRoleIds_success() {
    List<SysPermissionEntity> mockPermissions = List.of(new SysPermissionEntity());
    when(sysPermissionService.findAllByModuleAndAction(any(),any())).thenReturn(mockPermissions);

    List<Long> result = ReflectionTestUtils.invokeMethod(alertRequestService, "getApproverRoleIds");

    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals(null, result.get(0));
    verify(sysPermissionService, times(1)).findAllByModuleAndAction(any(),any());
  }
}