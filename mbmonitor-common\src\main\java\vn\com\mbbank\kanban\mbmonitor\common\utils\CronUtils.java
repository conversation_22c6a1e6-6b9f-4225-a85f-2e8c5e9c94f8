package vn.com.mbbank.kanban.mbmonitor.common.utils;

import com.cronutils.builder.CronBuilder;
import com.cronutils.mapper.CronMapper;
import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinition;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.field.expression.FieldExpression;
import com.cronutils.model.field.expression.FieldExpressionFactory;
import com.cronutils.parser.CronParser;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.Random;
import java.util.stream.Collectors;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorWebConfigEntity;



/**
 * Utility class for handling cron expression conversions.
 */
public class CronUtils {
  /**
   * Converts a UNIX-style cron expression to Quartz format.
   *
   * @param unixCron the cron expression in UNIX format (e.g., "0 12 * * ?")
   * @return the equivalent cron expression in Quartz format
   */
  public static String convertUnixToQuartz(String unixCron) {
    CronDefinition unixDefinition = CronDefinitionBuilder.instanceDefinitionFor(CronType.UNIX);
    CronParser unixParser = new CronParser(unixDefinition);
    Cron unixCronObj = unixParser.parse(unixCron);
    Cron quartzCronObj = CronMapper.fromUnixToQuartz().map(unixCronObj);
    return quartzCronObj.asString();
  }

  /**
   * Validates a time string and returns a cron expression for daily execution using cron-utils.
   *
   * @param timeText The time string in HH:mm format (e.g., "10:03")
   * @return A cron expression (e.g., "0 3 10 * * ?") or null if the input is invalid
   */
  public static String buildDailyCron(String timeText) {
    if (timeText == null || timeText.trim().isEmpty()) {
      return null;
    }

    try {
      LocalTime time = LocalTime.parse(timeText, DateTimeFormatter.ofPattern("HH:mm"));
      int hour = time.getHour();
      int minute = time.getMinute();

      // Get Quartz cron definition
      CronDefinition cronDefinition = CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ);

      // Build cron expression using cron-utils
      return CronBuilder.cron(cronDefinition).withSecond(FieldExpressionFactory.on(0)).withMinute(
          FieldExpressionFactory.on(minute))
          .withHour(FieldExpressionFactory.on(hour)).withDoM(FieldExpressionFactory.always()).withMonth(
          FieldExpressionFactory.always()).withDoW(FieldExpressionFactory.questionMark())
          .instance().asString();
    } catch (DateTimeParseException e) {
      return null;
    }
  }
  
  /**
   * Builds a cron expression string using the provided field values for months, days, and hours.
   * This method uses the UNIX cron format: minute hour day-of-month month day-of-week.
   * The minute field is always set to "*" (i.e., every minute).
   * Supported input formats for each field:
   * - "*"        → every value
   * - "?"        → no specific value (ignored in UNIX format, but included for compatibility)
   * - "1"        → specific single value
   * - "1,2,3"    → list of specific values
   * - "1-6"      → range of values
   * - Mixed: "1,3-5,7" → combination of specific values and ranges
   *
   * @param minutes     a string representing the minutes field (e.g., "*", "1", "1-6", "1,3,5")
   * @param months      a string representing the month field (e.g., "*", "1", "1-6", "1,3,5")
   * @param dayOfMonths a string representing the day-of-month field (e.g., "*", "15", "1-10", "?")
   * @param dayOfWeeks  a string representing the day-of-week field (e.g., "*", "1", "1-5", "1,3,5", "?")
   * @param hours       a string representing the hour field (e.g., "*", "0", "0,12", "1-6")
   * @return a cron expression string in UNIX format (minute hour dayOfMonth month dayOfWeek)
   *
   */
  public static String buildCronExpression(int minutes, Integer[] months,
                                           Integer[] dayOfMonths, Integer[] dayOfWeeks, Integer[] hours) {
    var cronDefinition = CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ);
    
    boolean hasDoM = !KanbanCommonUtil.isEmpty(dayOfMonths);
    boolean hasDoW = !KanbanCommonUtil.isEmpty(dayOfWeeks);
    
    FieldExpression domExpression;
    FieldExpression dowExpression;
    
    if (hasDoM && hasDoW) {
      // Both exist → prioritize DoW, set DoM =?
      domExpression = FieldExpressionFactory.questionMark();
      dowExpression = toFieldExpression(dayOfWeeks);
    } else if (hasDoM) {
      // only day of the month
      domExpression = toFieldExpression(dayOfMonths);
      dowExpression = FieldExpressionFactory.questionMark();
    } else if (hasDoW) {
      // only day of the week
      domExpression = FieldExpressionFactory.questionMark();
      dowExpression = toFieldExpression(dayOfWeeks);
    } else {
      // Both don't exist → DoM = *, DoW =?
      domExpression = FieldExpressionFactory.always();
      dowExpression = FieldExpressionFactory.questionMark();
    }
    
    return CronBuilder.cron(cronDefinition)
        .withSecond(FieldExpressionFactory.on(0))
        .withMinute(FieldExpressionFactory.always())
        .withHour(toFieldExpression(hours))
        .withDoM(domExpression)
        .withMonth(toFieldExpression(months))
        .withDoW(dowExpression)
        .withYear(FieldExpressionFactory.always()).instance().asString();
  }
  
  /**
   * Generates a cron expression by MonitorWebConfigEntity.
   *
   * @param monitorWebConfigEntity monitorWebConfigEntity.
   * @return A valid cron expression string.
   */
  public static String makeCronTimeByMonitorWebConfigEntity(MonitorWebConfigEntity monitorWebConfigEntity) {
    Integer[] months = monitorWebConfigEntity.getMonths();
    Integer[] dayOfMonths = monitorWebConfigEntity.getDayOfMonths();
    Integer[] dayOfWeeks = monitorWebConfigEntity.getDayOfWeeks();
    Integer[] hours = monitorWebConfigEntity.getHours();
    Random random = new Random();
    int minutes = random.nextInt(20) * 3;
    return buildCronExpression(minutes, months, dayOfMonths, dayOfWeeks, hours);
  }
  
  /**
   * Converts an array of Integers into a FieldExpression for cron scheduling.
   * Conversion rules:
   * - If the array is null or empty ⇒ returns an "always" expression (equivalent to "*").
   * - If the array contains a single value ⇒ returns an "on" expression for that value (e.g., "5").
   * - If the array contains multiple values
   * ⇒ returns an "and" expression combining multiple "on" expressions (e.g., "1,2,3").
   *
   * @param input An array of integers representing cron field values.
   * @return A corresponding FieldExpression to be used in Quartz cron configuration.
   */
  private static FieldExpression toFieldExpression(Integer[] input) {
    if (KanbanCommonUtil.isEmpty(input)) {
      // No values provided → equivalent to "*"
      return FieldExpressionFactory.always();
    } else if (input.length == 1) {
      // Single value → use "on"
      return FieldExpressionFactory.on(input[0]);
    } else {
      // Multiple values → use "and" to combine multiple "on" expressions
      return FieldExpressionFactory.and(
        Arrays.stream(input)
          .map(FieldExpressionFactory::on)
          .collect(Collectors.toList())
      );
    }
  }
}
