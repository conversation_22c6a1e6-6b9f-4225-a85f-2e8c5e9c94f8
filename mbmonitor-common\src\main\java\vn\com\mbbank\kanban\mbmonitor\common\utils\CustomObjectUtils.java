package vn.com.mbbank.kanban.mbmonitor.common.utils;

import com.alibaba.fastjson2.JSONArray;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleCondition;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleElement;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

/**
 * CustomObjectUtils.
 */
public class CustomObjectUtils {

  private CustomObjectUtils() {

  }

  /**
   * Generates a map of key-value pairs representing alert conditions and their corresponding values.
   *
   * @param alert the {@link AlertEntity} object containing the alert details such as content, priority, and recipient.
   * @param customObjects a list of {@link CustomObjectEntity} objects to be evaluated with the alert content.
   *                      If the list is empty or null, no additional custom mappings are added.
   * @return a {@link Map} containing the alert's details and evaluated custom object values.
   *                      If the {@code alert} parameter is {@code null}, the method returns an empty map.
   */
  public static Map<String, Object> getAlertConditionValueMap(AlertEntity alert,
                                                              List<CustomObjectEntity> customObjects) {
    if (Objects.isNull(alert)) {
      return Collections.emptyMap();
    }
    var mapValue = new HashMap<String, Object>();
    mapValue.put("content", alert.getContent());
    mapValue.put("priority", alert.getAlertPriorityConfigId());
    mapValue.put("recipient", alert.getRecipient());
    if (!CollectionUtils.isEmpty(customObjects)) {
      customObjects.forEach(customObject -> mapValue.put(String.valueOf(customObject.getId()),
          CustomObjectUtils.evaluate(alert.getContent(), customObject)));
    }
    return mapValue;
  }

  /**
   * evaluate custom object.
   *
   * @param input        alert content
   * @param customObject custom object
   * @return evaluated value
   */
  public static String evaluate(String input, CustomObjectEntity customObject) {
    if (StringUtils.isBlank(input) || Objects.isNull(customObject)) {
      return "";
    }
    return switch (customObject.getType()) {
      case REGEX -> evaluateRegex(input, customObject.getRegex());
      case INDEX_TO_INDEX -> evaluateIndexToIndex(input, customObject.getFromIndex(), customObject.getToIndex());
      case KEYWORD_TO_KEYWORD ->
          evaluateKeywordToKeyword(input, customObject.getFromKeyword(), customObject.getToKeyword());
    };
  }

  private static String evaluateIndexToIndex(String alertContent, Integer fromIndex, Integer toIndex) {
    try {
      return alertContent.substring(fromIndex, toIndex + 1);
    } catch (IndexOutOfBoundsException exception) {
      return "";
    }
  }

  private static String evaluateRegex(String alertContent, String regex) {
    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(alertContent);
    var matched = matcher.find();
    if (!matched || matcher.groupCount() == 0) {
      return "";
    }
    return matcher.group(1);
  }

  private static String evaluateKeywordToKeyword(String alertContent, String fromKeyword, String toKeyword) {
    var fromIndex = alertContent.indexOf(fromKeyword) + fromKeyword.length();
    var toIndex = alertContent.indexOf(toKeyword);
    if (fromIndex > toIndex) {
      return "";
    }
    return evaluateIndexToIndex(alertContent, fromIndex, toIndex - 1);
  }

  /**
   * Generates a map of key-value pairs representing alert conditions and their corresponding values for trigger.
   *
   * @param alert the {@link AlertEntity} object containing the alert details such as content, priority, and recipient.
   * @param customObjects a list of {@link CustomObjectEntity} objects to be evaluated with the alert content.
   *                      If the list is empty or null, no additional custom mappings are added.
   * @return a {@link Map} containing the alert's details and evaluated custom object values.
   *                      If the {@code alert} parameter is {@code null}, the method returns an empty map.
   */
  public static Map<String, Object> getAlertConditionValueMapForTrigger(AlertEntity alert,
                                                              List<CustomObjectEntity> customObjects) {
    if (Objects.isNull(alert)) {
      return Collections.emptyMap();
    }
    var mapValue = new HashMap<String, Object>();
    mapValue.put("content", alert.getContent());
    if (alert.getAlertPriorityConfigId() != null) {
      mapValue.put("priority", alert.getAlertPriorityConfigId().toString());
    }
    mapValue.put("recipient", alert.getRecipient());
    if (!CollectionUtils.isEmpty(customObjects)) {
      customObjects.forEach(customObject -> mapValue.put(String.valueOf(customObject.getId()),
              CustomObjectUtils.evaluate(alert.getContent(), customObject)));
    }
    return mapValue;
  }

  /**
   * Replace id to name for customObject (field) and priority (value).
   *
   * @param customObjects Custom object entities.
   * @param priorityConfigEntities Priority config entities.
   * @param ruleGroup Rule group to modify.
   */
  public static void replaceFieldIdWithName(List<CustomObjectEntity> customObjects,
                                            List<AlertPriorityConfigEntity> priorityConfigEntities,
                                            RuleGroupType ruleGroup) {
    if (ruleGroup == null || CollectionUtils.isEmpty(ruleGroup.getRules())
            || (CollectionUtils.isEmpty(customObjects) && CollectionUtils.isEmpty(priorityConfigEntities))) {
      return;
    }
    Map<String, String> idToNameMap = new HashMap<>();
    Map<String, String> priorityMap = new HashMap<>();

    if (!KanbanCommonUtil.isEmpty(customObjects)) {
      idToNameMap = customObjects.stream()
              .filter(obj -> obj.getId() != null && obj.getName() != null)
              .collect(Collectors.toMap(obj -> obj.getId().toString(), CustomObjectEntity::getName));
    }

    if (!KanbanCommonUtil.isEmpty(priorityConfigEntities)) {
      priorityMap = priorityConfigEntities.stream()
              .filter(p -> p.getId() != null && p.getName() != null)
              .collect(Collectors.toMap(p -> p.getId().toString(),
                      AlertPriorityConfigEntity::getName));
    }

    for (RuleElement rule : ruleGroup.getRules()) {
      if (rule instanceof RuleCondition<?> condition) {
        String field = condition.getField();
        String fieldName = idToNameMap.get(field);
        if (fieldName != null) {
          condition.setField(fieldName);
        }

        if ("priority".equals(field)) {
          Object val = condition.getValue();
          List<Object> rawValues = new ArrayList<>();

          if (val instanceof JSONArray arr) {
            rawValues.addAll(arr);
          } else if (val instanceof Collection<?> col) {
            rawValues.addAll(col);
          } else if (val instanceof Object[] array) {
            rawValues.addAll(Arrays.asList(array));
          } else if (val instanceof String str && str.startsWith("[") && str.endsWith("]")) {
            String[] parts = str.substring(1, str.length() - 1).split(",");
            for (String part : parts) {
              rawValues.add(part.trim());
            }
          } else if (val != null) {
            rawValues.add(val);
          }

          if (!KanbanCommonUtil.isEmpty(rawValues)) {
            if (rawValues.size() == 1) {
              String v = rawValues.get(0).toString();
              String name = priorityMap.getOrDefault(v, v);
              condition.setValueFromExternal(name);
            } else {
              JSONArray replaced = new JSONArray();
              for (Object v : rawValues) {
                String name = priorityMap.getOrDefault(v.toString(), v.toString());
                replaced.add(name);
              }
              condition.setValueFromExternal(replaced);
            }
          }
        }

      } else if (rule instanceof RuleGroupType group) {
        replaceFieldIdWithName(customObjects, priorityConfigEntities, group);
      }
    }
  }

  /**
   * extract priority id to rule group.
   *
   * @param ruleGroup to extract
   * @return list id
   */
  public static Set<Long> extractPriorityIdsFromRuleGroup(RuleGroupType ruleGroup) {
    Set<Long> ids = new HashSet<>();
    collectPriorityIds(ruleGroup, ids);
    return ids;
  }

  private static void collectPriorityIds(RuleGroupType group, Set<Long> ids) {
    for (RuleElement rule : group.getRules()) {
      if (rule instanceof RuleCondition<?> condition) {
        if ("priority".equals(condition.getField())) {
          Object value = condition.getValue();
          try {
            if (value instanceof JSONArray arr) {
              for (Object v : arr) {
                parseAndAddId(v, ids);
              }
            } else if (value instanceof Collection<?> col) {
              for (Object v : col) {
                parseAndAddId(v, ids);
              }
            } else if (value instanceof Object[] array) {
              for (Object v : array) {
                parseAndAddId(v, ids);
              }
            } else if (value instanceof String str && str.startsWith("[") && str.endsWith("]")) {
              String[] parts = str.substring(1, str.length() - 1).split(",");
              for (String part : parts) {
                parseAndAddId(part, ids);
              }
            } else {
              parseAndAddId(value, ids);
            }
          } catch (Exception ignored) {
            // do nothing
          }
        }
      } else if (rule instanceof RuleGroupType subgroup) {
        collectPriorityIds(subgroup, ids);
      }
    }
  }

  private static void parseAndAddId(Object value, Set<Long> ids) {
    try {
      if (value != null) {
        ids.add(Long.parseLong(value.toString().trim()));
      }
    } catch (NumberFormatException ignored) {
      //do nothing
    }
  }
}
