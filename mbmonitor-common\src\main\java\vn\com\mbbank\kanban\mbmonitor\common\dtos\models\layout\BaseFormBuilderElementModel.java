package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.enums.FormBuilderElementTypeEnum;

/**
 * BaseFormBuilderElement.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes({
    @JsonSubTypes.Type(value = TextInputElementModel.class, name = "TEXT"),
    @JsonSubTypes.Type(value = NumberInputElementModel.class, name = "NUMBER"),
    @JsonSubTypes.Type(value = CheckboxElementModel.class, name = "CHECKBOX"),
    @JsonSubTypes.Type(value = RadioElementModel.class, name = "RADIO"),
    @JsonSubTypes.Type(value = SelectElementModel.class, name = "SELECT"),
    @JsonSubTypes.Type(value = DateTimeElementModel.class, name = "DATETIME"),
    @JsonSubTypes.Type(value = TimeDurationElementModel.class, name = "TIME_DURATION"),
    @JsonSubTypes.Type(value = TimeDurationElementModel.class, name = "TEXTAREA"),
    @JsonSubTypes.Type(value = TimeDurationElementModel.class, name = "TABLE_INPUT"),
    @JsonSubTypes.Type(value = SectionElementModel.class, name = "SECTION")
})
public class BaseFormBuilderElementModel {
  @NotBlank
  String id;
  @NotNull
  FormBuilderElementTypeEnum type;
  String label;
  Boolean showLabel;
  String helpText;
}
