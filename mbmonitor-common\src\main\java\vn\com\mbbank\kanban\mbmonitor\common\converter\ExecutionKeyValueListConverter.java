package vn.com.mbbank.kanban.mbmonitor.common.converter;

import com.alibaba.fastjson2.JSON;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecutionKeyValue;

/**
 * Convert list key value <-> string in entity.
 */
@Converter
public class ExecutionKeyValueListConverter implements AttributeConverter<List<ExecutionKeyValue>, String> {
  private final Logger logger = LoggerFactory.getLogger(ExecutionKeyValueListConverter.class);

  @Override
  public String convertToDatabaseColumn(List<ExecutionKeyValue> list) {
    try {
      return JSON.toJSONString(list);
    } catch (Exception e) {
      logger.warn("Cannot convert List<ExecutionKeyValue> to JSON string");
      throw new IllegalArgumentException("Cannot convert List<ExecutionKeyValue> to JSON string", e);
    }
  }

  @Override
  public List<ExecutionKeyValue> convertToEntityAttribute(String dbData) {
    try {
      return JSON.parseArray(dbData, ExecutionKeyValue.class);
    } catch (Exception e) {
      logger.warn("Cannot convert JSON string to List<ExecutionKeyValue>");
      throw new IllegalArgumentException("Cannot convert JSON string to List<ExecutionKeyValue>", e);
    }
  }
}
