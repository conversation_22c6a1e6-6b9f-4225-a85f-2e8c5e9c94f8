package vn.com.mbbank.kanban.mbmonitor.server.constants;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

/**
 * EmailConfigConstants.
 */
@FieldDefaults(level = AccessLevel.PUBLIC, makeFinal = true)
public class EmailConfigConstants {
  public static final String IMAP_PROTOCOL = "imap";
  public static final String SMTP_PROTOCOL = "smtp";
  public static final String DEFAULT_READ_FOLDER = "INBOX";
  public static final String KEY_IMAP_PROTOCOL = "mail.store.protocol";
  public static final String KEY_IMAP_SSL_ENABLE = "mail.imap.ssl.enable";
  public static final String TEST_CONNECTION_SMTP_TEXT =
      "This is an e-mail message sent automatically by Monitoring Tool while testing the settings for your account.";
  public static final String TEST_CONNECTION_SMTP_SUBJECT =
      "Monitoring Tool Test Message";
  public static final String KEY_TRANSPORT_PROTOCOL =
      "mail.transport.protocol";
  public static final String KEY_DEBUG =
      "mail.debug";
  public static final String KEY_SMTP_AUTH =
      "mail.smtp.auth";
  public static final String KEY_SMTP_STARTTLS = "mail.smtp.starttls.enable";
  public static final String KEY_SMTP_TLS = "mail.smtp.ssl.protocols";
  public static final String VALUE_SMTP_TLS = "TLSv1.2";
  public static final String KEY_IMAP_STARTTLS = "mail.imap.starttls.enable";
  public static final String KEY_SMTP_CONNECTION_TIMEOUT =
      "mail.smtp.connnectiontimeout";
  public static final String KEY_SMTP_TIMEOUT =
      "mail.smtp.timeout";
  public static final String KEY_SMTP_WRITE_TIMEOUT =
      "mail.smtp.writetimeout";
  public static final String SMTP_CONNECTION_TIMEOUT = "60000";
  public static final String SMTP_TIMEOUT = "60000";
  public static final String SMTP_WRITE_TIMEOUT = "60000";
  public static final String KEY_IMAP_CONNECTION_TIMEOUT = "mail.imap.connectiontimeout";
  public static final String KEY_IMAP_TIMEOUT = "mail.imap.timeout";
  public static final String VALUE_IMAP_CONNECTION_TIMEOUT = "60000";
  public static final String VALUE_IMAP_TIMEOUT = "60000";


}
