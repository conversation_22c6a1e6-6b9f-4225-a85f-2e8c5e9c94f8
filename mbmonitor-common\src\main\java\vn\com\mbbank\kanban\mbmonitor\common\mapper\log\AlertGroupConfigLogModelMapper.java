package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.AlertGroupConfigLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.ConfigDependencyLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigConditionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupOutputEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AlertGroupConfigLogModelMapper extends
    KanbanBaseMapper<AlertGroupConfigLogModel, AlertGroupConfigEntity> {
  AlertGroupConfigLogModelMapper INSTANCE = Mappers.getMapper(AlertGroupConfigLogModelMapper.class);

  /**
   * map AlertGroupConfigEntity to AlertGroupConfigLogModel.
   *
   * @param config                        AlertGroupConfigEntity.
   * @param serviceDependencies           services
   * @param serviceWithAllAppDependencies services all app
   * @param applicationDependencies       applications
   * @param conditions                    conditions
   * @param customService                 service
   * @param customApplication             application
   * @param customPriority                priority
   * @param customObjects                 custom object info
   * @return AlertGroupConfigLogModel
   */
  default AlertGroupConfigLogModel map(AlertGroupConfigEntity config,
                                       List<ServiceEntity> serviceDependencies,
                                       List<ServiceEntity> serviceWithAllAppDependencies,
                                       List<ApplicationEntity> applicationDependencies,
                                       List<AlertGroupConfigConditionEntity> conditions,
                                       ServiceEntity customService,
                                       ApplicationEntity customApplication,
                                       AlertPriorityConfigEntity customPriority,
                                       List<CustomObjectEntity> customObjects) {
    var serviceNames = new ArrayList<String>();
    var applicationNames = new ArrayList<String>();
    for (ServiceEntity service : serviceDependencies) {
      serviceNames.add(service.getName());
    }
    for (ServiceEntity service : serviceWithAllAppDependencies) {
      serviceNames.add(service.getName() + " (All application)");
    }
    for (ApplicationEntity application : applicationDependencies) {
      applicationNames.add(application.getName());
    }
    var customOutput = AlertGroupOutputEnum.CUSTOM.equals(config.getAlertOutput())
        ? AlertGroupConfigLogModel.CustomOutput.builder().service(customService.getName())
        .application(customApplication.getName()).contact(config.getCustomRecipient())
        .priority(customPriority.getName()).alertContent(config.getCustomContent()).build() : null;

    var dependency = ConfigDependencyLogModel.builder().services(serviceNames).applications(applicationNames).build();
    var isMultipleConditionType = AlertGroupConfigTypeEnum.MULTIPLE_CONDITION.equals(config.getType());
    return AlertGroupConfigLogModel.builder()
        .name(config.getName())
        .description(config.getDescription())
        .conditions(isMultipleConditionType ? conditions.stream().filter(ele -> Objects.nonNull(ele.getRuleGroup()))
            .map(ele -> ele.getRuleGroup().toString()).toList() : null)
        .customObjects(
            !isMultipleConditionType ? customObjects.stream().map(CustomObjectEntity::getName).toList() : null)
        .dependency(dependency)
        .customOutput(customOutput)
        .alertOutputType(config.getAlertOutput())
        .active(config.getActive())
        .build();
  }
}
