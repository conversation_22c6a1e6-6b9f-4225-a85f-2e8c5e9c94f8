package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout;

import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.enums.FormBuilderElementTypeEnum;

/**
 * CheckboxElement.
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CheckboxElementModel extends BaseFormBuilderInputElementModel {
  FormBuilderElementTypeEnum type = FormBuilderElementTypeEnum.CHECKBOX;
  @Builder.Default
  List<OptionModel> options = new ArrayList<>();
  @Builder.Default
  List<String> defaultValue = new ArrayList<>();
}
