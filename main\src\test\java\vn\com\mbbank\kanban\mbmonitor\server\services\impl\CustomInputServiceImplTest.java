package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.BaseFormBuilderInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.CustomInputRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomInputEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.FormBuilderElementTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.CustomInputRepository;

@ExtendWith(MockitoExtension.class)
class CustomInputServiceImplTest {

  @Mock
  private CustomInputRepository customInputRepository;

  @InjectMocks
  private CustomInputServiceImpl customInputService;

  // Test data
  private final String testId = "test-id-123";
  private final String testName = "Test Custom Input";
  private final String testDescription = "Test Description";

  @Test
  void getRepository_success() {
    // Act
    JpaCommonRepository<CustomInputEntity, String> result = customInputService.getRepository();

    // Assert
    assertEquals(customInputRepository, result);
  }

  @Test
  void findWithId_success() throws BusinessException {
    // Arrange
    CustomInputEntity customInputEntity = createTestCustomInputEntity();
    when(customInputRepository.findById(testId)).thenReturn(Optional.of(customInputEntity));

    // Act
    CustomInputEntity result = customInputService.findWithId(testId);

    // Assert
    assertNotNull(result);
    assertEquals(testId, result.getId());
    assertEquals(testName, result.getName());
    assertEquals(testDescription, result.getDescription());
    verify(customInputRepository, times(1)).findById(testId);
  }

  @Test
  void findWithId_failed_whenNotFound() {
    // Arrange
    when(customInputRepository.findById(testId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      customInputService.findWithId(testId);
    });

    assertEquals(ErrorCode.CUSTOM_INPUT_NOT_FOUND.getCode(), exception.getCode());
    verify(customInputRepository, times(1)).findById(testId);
  }

  @Test
  void createOrUpdate_success_whenCreate() throws BusinessException {
    // Arrange
    CustomInputRequest request = createTestCustomInputRequest();
    request.setId(null); // Create mode
    CustomInputEntity savedEntity = createTestCustomInputEntity();

    when(customInputRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
    when(customInputRepository.save(any(CustomInputEntity.class))).thenReturn(savedEntity);

    // Act
    CustomInputEntity result = customInputService.createOrUpdate(request);

    // Assert
    assertNotNull(result);
    assertEquals(testId, result.getId());
    assertEquals(testName, result.getName());
    verify(customInputRepository, times(1)).existsByNameIgnoreCase(testName);
    verify(customInputRepository, times(1)).save(any(CustomInputEntity.class));
    verify(customInputRepository, never()).findById(anyString());
  }

  @Test
  void createOrUpdate_success_whenUpdate() throws BusinessException {
    // Arrange
    CustomInputRequest request = createTestCustomInputRequest();
    CustomInputEntity existingEntity = createTestCustomInputEntity();
    CustomInputEntity savedEntity = createTestCustomInputEntity();

    when(customInputRepository.existsByIdNotAndNameIgnoreCase(testId, testName)).thenReturn(false);
    when(customInputRepository.findById(testId)).thenReturn(Optional.of(existingEntity));
    when(customInputRepository.save(any(CustomInputEntity.class))).thenReturn(savedEntity);

    // Act
    CustomInputEntity result = customInputService.createOrUpdate(request);

    // Assert
    assertNotNull(result);
    assertEquals(testId, result.getId());
    assertEquals(testName, result.getName());
    verify(customInputRepository, times(1)).existsByIdNotAndNameIgnoreCase(testId, testName);
    verify(customInputRepository, times(1)).findById(testId);
    verify(customInputRepository, times(1)).save(any(CustomInputEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenNameExistsInCreateMode() {
    // Arrange
    CustomInputRequest request = createTestCustomInputRequest();
    request.setId(null); // Create mode

    when(customInputRepository.existsByNameIgnoreCase(testName)).thenReturn(true);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      customInputService.createOrUpdate(request);
    });

    assertEquals(ErrorCode.CUSTOM_INPUT_NAME_IS_EXISTED.getCode(), exception.getCode());
    verify(customInputRepository, times(1)).existsByNameIgnoreCase(testName);
    verify(customInputRepository, never()).save(any(CustomInputEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenNameExistsInUpdateMode() {
    // Arrange
    CustomInputRequest request = createTestCustomInputRequest();

    when(customInputRepository.existsByIdNotAndNameIgnoreCase(testId, testName)).thenReturn(true);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      customInputService.createOrUpdate(request);
    });

    assertEquals(ErrorCode.CUSTOM_INPUT_NAME_IS_EXISTED.getCode(), exception.getCode());
    verify(customInputRepository, times(1)).existsByIdNotAndNameIgnoreCase(testId, testName);
    verify(customInputRepository, never()).save(any(CustomInputEntity.class));
  }

  @Test
  void createOrUpdate_failed_whenEntityNotFoundInUpdateMode() {
    // Arrange
    CustomInputRequest request = createTestCustomInputRequest();

    when(customInputRepository.existsByIdNotAndNameIgnoreCase(testId, testName)).thenReturn(false);
    when(customInputRepository.findById(testId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      customInputService.createOrUpdate(request);
    });

    assertEquals(ErrorCode.CUSTOM_INPUT_NOT_FOUND.getCode(), exception.getCode());
    verify(customInputRepository, times(1)).existsByIdNotAndNameIgnoreCase(testId, testName);
    verify(customInputRepository, times(1)).findById(testId);
    verify(customInputRepository, never()).save(any(CustomInputEntity.class));
  }

  @Test
  void deleteWithId_success() throws BusinessException {
    // Arrange
    CustomInputEntity customInputEntity = createTestCustomInputEntity();
    when(customInputRepository.findById(testId)).thenReturn(Optional.of(customInputEntity));

    // Act
    customInputService.deleteWithId(testId);

    // Assert
    verify(customInputRepository, times(1)).findById(testId);
    verify(customInputRepository, times(1)).delete(customInputEntity);
  }

  @Test
  void deleteWithId_failed_whenNotFound() {
    // Arrange
    when(customInputRepository.findById(testId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      customInputService.deleteWithId(testId);
    });

    assertEquals(ErrorCode.CUSTOM_INPUT_NOT_FOUND.getCode(), exception.getCode());
    verify(customInputRepository, times(1)).findById(testId);
    verify(customInputRepository, never()).delete(any(CustomInputEntity.class));
  }

  // Helper methods to create test data
  private CustomInputEntity createTestCustomInputEntity() {
    CustomInputEntity entity = new CustomInputEntity();
    entity.setId(testId);
    entity.setName(testName);
    entity.setDescription(testDescription);
    entity.setType(FormBuilderElementTypeEnum.TEXT);
    return entity;
  }

  private CustomInputRequest createTestCustomInputRequest() {
    var configuration = new BaseFormBuilderInputElementModel();
    configuration.setId(testId);
    configuration.setLabel(testName);
    return CustomInputRequest.builder()
        .id(testId)
        .name(testName)
        .description(testDescription)
        .type(FormBuilderElementTypeEnum.TEXT)
        .configuration(configuration)
        .build();
  }
}
