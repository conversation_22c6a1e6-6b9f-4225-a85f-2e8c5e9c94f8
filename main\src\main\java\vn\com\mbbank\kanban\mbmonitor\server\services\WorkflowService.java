package vn.com.mbbank.kanban.mbmonitor.server.services;

import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowEntity;

/**
 * interface logic WorkflowService.
 */
public interface WorkflowService extends BaseService<WorkflowEntity, String> {

  /**
   * create workflow.
   *
   * @param workflow workflow
   * @return WorkflowEntity
   */
  WorkflowEntity create(WorkflowModel workflow) throws BusinessException;

  /**
   * deleteWithId.
   *
   * @param workflowId workflowId
   */
  void deleteWithId(String workflowId) throws BusinessException;

  /**
   * findWithId.
   *
   * @param workflowId workflowId
   * @return WorkflowModel
   */
  WorkflowModel findWithId(String workflowId) throws BusinessException;
}