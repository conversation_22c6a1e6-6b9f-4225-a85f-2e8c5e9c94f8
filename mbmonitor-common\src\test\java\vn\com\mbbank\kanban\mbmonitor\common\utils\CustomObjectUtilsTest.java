package vn.com.mbbank.kanban.mbmonitor.common.utils;

import com.alibaba.fastjson2.JSONArray;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CustomObjectTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OperatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleCondition;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class CustomObjectUtilsTest {
  CustomObjectEntity indexToIndexCustomObject;
  CustomObjectEntity keywordToKeywordCustomObject;
  CustomObjectEntity regexCustomObject;

  @BeforeEach
  void setup() {
    indexToIndexCustomObject = new CustomObjectEntity();
    indexToIndexCustomObject.setToIndex(3);
    indexToIndexCustomObject.setFromIndex(2);
    indexToIndexCustomObject.setType(CustomObjectTypeEnum.INDEX_TO_INDEX);
    keywordToKeywordCustomObject = new CustomObjectEntity();
    keywordToKeywordCustomObject.setFromKeyword("a");
    keywordToKeywordCustomObject.setToKeyword("b");
    keywordToKeywordCustomObject.setType(CustomObjectTypeEnum.KEYWORD_TO_KEYWORD);
    regexCustomObject = new CustomObjectEntity();
    regexCustomObject.setRegex(
        "Cảnh báo bảng (?<=Cảnh báo bảng )(.*?)(?=\\s+có) có \\d+ records, vượt ngưỡng \\d+");
    regexCustomObject.setType(CustomObjectTypeEnum.REGEX);
  }

  @Test
  void evaluateIndexToIndex_success_caseInputBank() {
    var input = "";
    var res = CustomObjectUtils.evaluate(input, indexToIndexCustomObject);
    assertEquals("", res);
  }

  @Test
  void evaluateIndexToIndex_success_caseCustomObjectNull() {
    var input = "adsf";
    var res = CustomObjectUtils.evaluate(input, null);
    assertEquals("", res);
  }

  @Test
  void evaluateIndexToIndex_success_caseIndexToIndex() {
    var input = "adsf";
    var res = CustomObjectUtils.evaluate(input, indexToIndexCustomObject);
    assertEquals("sf", res);
  }

  @Test
  void evaluateIndexToIndex_success_caseFromIndexGreaterThanToIndex() {
    var input = "adsf";
    var customObject = new CustomObjectEntity();
    customObject.setToIndex(2);
    customObject.setFromIndex(3);
    customObject.setType(CustomObjectTypeEnum.INDEX_TO_INDEX);
    var res = CustomObjectUtils.evaluate(input, customObject);
    assertEquals("", res);
  }

  @Test
  void evaluateKeywordToKeyword_success_caseKeywordToKeyWord() {
    var input = "aasdfb";
    var res = CustomObjectUtils.evaluate(input, keywordToKeywordCustomObject);
    assertEquals("asdf", res);
  }

  @Test
  void evaluateKeywordToKeyword_success_caseInvalid() {
    var input = "basdfa";
    var res = CustomObjectUtils.evaluate(input, keywordToKeywordCustomObject);
    assertEquals("", res);
  }

  @Test
  void evaluateIndexToIndex_success_caseRegex() {
    var input =
        "Cảnh báo bảng FBNK.UPLOAD.PRIORITY.MB (BNK/UPLOAD.SERVICE.MB.1) có 7 records, vượt ngưỡng 5";
    var res = CustomObjectUtils.evaluate(input, regexCustomObject);
    assertEquals("FBNK.UPLOAD.PRIORITY.MB (BNK/UPLOAD.SERVICE.MB.1)", res);
  }

  @Test
  void evaluateIndexToIndex_success_caseRegexNotMatch() {
    var input = "aaaa";
    var res = CustomObjectUtils.evaluate(input, regexCustomObject);
    assertEquals("", res);
  }

  @Test
  void evaluateIndexToIndex_success_caseRegexNotFoundGroup() {
    var input =
        "Cảnh báo bảng FBNK.UPLOAD.PRIORITY.MB (BNK/UPLOAD.SERVICE.MB.1) có 7 records, vượt ngưỡng 5";
    var customObject = new CustomObjectEntity();
    customObject.setRegex("Cảnh báo bảng");
    customObject.setType(CustomObjectTypeEnum.REGEX);
    var res = CustomObjectUtils.evaluate(input, customObject);
    assertEquals("", res);
  }
  @Test
  void testReplaceCustomObjectField() {
    RuleCondition<String> condition = new RuleCondition<>();
    condition.setField("100");
    condition.setOperator(OperatorEnum.IS);
    condition.setValue("abc");

    RuleGroupType group = new RuleGroupType();
    group.setCombinator(vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum.AND);
    group.setRules(List.of(condition));

    CustomObjectEntity obj = new CustomObjectEntity();
    obj.setId(100L);
    obj.setName("Status");
    CustomObjectUtils.replaceFieldIdWithName(List.of(obj), null, group);

    assertEquals("Status", condition.getField());
  }

  @Test
  void testReplacePrioritySingleValue() {
    RuleCondition<Object> condition = new RuleCondition<>();
    condition.setField("priority");
    condition.setOperator(OperatorEnum.IS);
    condition.setValue("1");

    RuleGroupType group = new RuleGroupType();
    group.setCombinator(vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum.AND);
    group.setRules(List.of(condition));

    AlertPriorityConfigEntity priority = new AlertPriorityConfigEntity();
    priority.setId(1L);
    priority.setName("High");
    CustomObjectUtils.replaceFieldIdWithName(null, List.of(priority), group);

    assertEquals("High", condition.getValue());
  }

  @Test
  void testReplacePriorityArrayValue() {
    RuleCondition<Object> condition = new RuleCondition<>();
    condition.setField("priority");
    condition.setOperator(OperatorEnum.IS_ONE_OF);
    JSONArray arr = new JSONArray();
    arr.add("1");
    arr.add("2");
    condition.setValue(arr);

    RuleGroupType group = new RuleGroupType();
    group.setCombinator(vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum.OR);
    group.setRules(List.of(condition));

    AlertPriorityConfigEntity alertPriority1 = new AlertPriorityConfigEntity();
    alertPriority1.setId(1L);
    alertPriority1.setName("High");
    AlertPriorityConfigEntity alertPriority2 = new AlertPriorityConfigEntity();
    alertPriority2.setId(2L);
    alertPriority2.setName("Critical");
    List<AlertPriorityConfigEntity> priorities = List.of(alertPriority1, alertPriority2);

    CustomObjectUtils.replaceFieldIdWithName(null, priorities, group);

    JSONArray replaced = (JSONArray) condition.getValue();
    assertTrue(replaced.contains("High"));
    assertTrue(replaced.contains("Critical"));
  }

  @Test
  void testNestedRuleGroupPriorityReplace() {
    RuleCondition<Object> nested = new RuleCondition<>();
    nested.setField("priority");
    nested.setOperator(OperatorEnum.IS);
    nested.setValue("999");

    RuleGroupType subgroup = new RuleGroupType();
    subgroup.setCombinator(vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum.OR);
    subgroup.setRules(List.of(nested));

    RuleGroupType root = new RuleGroupType();
    root.setCombinator(vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum.AND);
    root.setRules(List.of(subgroup));

    AlertPriorityConfigEntity a1 = new AlertPriorityConfigEntity();
    a1.setId(999L);
    a1.setName("Urgent");
    List<AlertPriorityConfigEntity> priorityEntities = List.of(
            a1);
    CustomObjectUtils.replaceFieldIdWithName(null, priorityEntities, root);

    assertEquals("Urgent", nested.getValue());
  }

  @Test
  void testExtractPriorityIds_nestedGroups() {
    RuleCondition<Object> c1 = new RuleCondition<>();
    c1.setField("priority");
    c1.setOperator(OperatorEnum.IS);
    c1.setValue("123");

    RuleCondition<Object> c2 = new RuleCondition<>();
    c2.setField("priority");
    c2.setOperator(OperatorEnum.IS);
    c2.setValue("456");

    RuleGroupType subgroup = new RuleGroupType();
    subgroup.setCombinator(vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum.OR);
    subgroup.setRules(List.of(c2));

    RuleGroupType group = new RuleGroupType();
    group.setCombinator(vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum.AND);
    group.setRules(List.of(c1, subgroup));

    Set<Long> ids = CustomObjectUtils.extractPriorityIdsFromRuleGroup(group);
    assertEquals(Set.of(123L, 456L), ids);
  }

  @Test
  void testReplaceFieldIdWithName_nullGroup() {
    CustomObjectUtils.replaceFieldIdWithName(null, null, null); // should not throw
  }

  @Test
  void testReplaceFieldIdWithName_emptyRules() {
    RuleGroupType group = new RuleGroupType();
    group.setRules(Collections.emptyList());
    CustomObjectUtils.replaceFieldIdWithName(null, null, group); // should not throw
  }

  @Test
  void testReplaceFieldIdWithName_emptyCustomObjectsAndPriorities() {
    RuleCondition<String> condition = new RuleCondition<>();
    condition.setField("priority");
    condition.setValue("100");

    RuleGroupType group = new RuleGroupType();
    group.setRules(List.of(condition));

    CustomObjectUtils.replaceFieldIdWithName(Collections.emptyList(), Collections.emptyList(), group);
    assertEquals("priority", condition.getField()); // Should remain unchanged
  }
}
