package vn.com.mbbank.kanban.mbmonitor.common.utils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CustomObjectTypeEnum;

public class CustomObjectUtilsTest {
  CustomObjectEntity indexToIndexCustomObject;
  CustomObjectEntity keywordToKeywordCustomObject;
  CustomObjectEntity regexCustomObject;

  @BeforeEach
  void setup() {
    indexToIndexCustomObject = new CustomObjectEntity();
    indexToIndexCustomObject.setToIndex(3);
    indexToIndexCustomObject.setFromIndex(2);
    indexToIndexCustomObject.setType(CustomObjectTypeEnum.INDEX_TO_INDEX);
    keywordToKeywordCustomObject = new CustomObjectEntity();
    keywordToKeywordCustomObject.setFromKeyword("a");
    keywordToKeywordCustomObject.setToKeyword("b");
    keywordToKeywordCustomObject.setType(CustomObjectTypeEnum.KEYWORD_TO_KEYWORD);
    regexCustomObject = new CustomObjectEntity();
    regexCustomObject.setRegex(
        "Cảnh báo bảng (?<=Cảnh báo bảng )(.*?)(?=\\s+có) có \\d+ records, vượt ngưỡng \\d+");
    regexCustomObject.setType(CustomObjectTypeEnum.REGEX);
  }

  @Test
  void evaluateIndexToIndex_success_caseInputBank() {
    var input = "";
    var res = CustomObjectUtils.evaluate(input, indexToIndexCustomObject);
    Assertions.assertEquals("", res);
  }

  @Test
  void evaluateIndexToIndex_success_caseCustomObjectNull() {
    var input = "adsf";
    var res = CustomObjectUtils.evaluate(input, null);
    Assertions.assertEquals("", res);
  }

  @Test
  void evaluateIndexToIndex_success_caseIndexToIndex() {
    var input = "adsf";
    var res = CustomObjectUtils.evaluate(input, indexToIndexCustomObject);
    Assertions.assertEquals("sf", res);
  }

  @Test
  void evaluateIndexToIndex_success_caseFromIndexGreaterThanToIndex() {
    var input = "adsf";
    var customObject = new CustomObjectEntity();
    customObject.setToIndex(2);
    customObject.setFromIndex(3);
    customObject.setType(CustomObjectTypeEnum.INDEX_TO_INDEX);
    var res = CustomObjectUtils.evaluate(input, customObject);
    Assertions.assertEquals("", res);
  }

  @Test
  void evaluateKeywordToKeyword_success_caseKeywordToKeyWord() {
    var input = "aasdfb";
    var res = CustomObjectUtils.evaluate(input, keywordToKeywordCustomObject);
    Assertions.assertEquals("asdf", res);
  }

  @Test
  void evaluateKeywordToKeyword_success_caseInvalid() {
    var input = "basdfa";
    var res = CustomObjectUtils.evaluate(input, keywordToKeywordCustomObject);
    Assertions.assertEquals("", res);
  }

  @Test
  void evaluateIndexToIndex_success_caseRegex() {
    var input =
        "Cảnh báo bảng FBNK.UPLOAD.PRIORITY.MB (BNK/UPLOAD.SERVICE.MB.1) có 7 records, vượt ngưỡng 5";
    var res = CustomObjectUtils.evaluate(input, regexCustomObject);
    Assertions.assertEquals("FBNK.UPLOAD.PRIORITY.MB (BNK/UPLOAD.SERVICE.MB.1)", res);
  }

  @Test
  void evaluateIndexToIndex_success_caseRegexNotMatch() {
    var input = "aaaa";
    var res = CustomObjectUtils.evaluate(input, regexCustomObject);
    Assertions.assertEquals("", res);
  }

  @Test
  void evaluateIndexToIndex_success_caseRegexNotFoundGroup() {
    var input =
        "Cảnh báo bảng FBNK.UPLOAD.PRIORITY.MB (BNK/UPLOAD.SERVICE.MB.1) có 7 records, vượt ngưỡng 5";
    var customObject = new CustomObjectEntity();
    customObject.setRegex("Cảnh báo bảng");
    customObject.setType(CustomObjectTypeEnum.REGEX);
    var res = CustomObjectUtils.evaluate(input, customObject);
    Assertions.assertEquals("", res);
  }
}
