package vn.com.mbbank.kanban.mbmonitor.server.services;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailConfigPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.EmailConfigResponse;

/**
 * interface logic email config account.
 */
public interface EmailConfigService extends BaseService<EmailConfigEntity, Long> {

  /**
   * Saves or updates a email config.
   *
   * @param emailConfigRequest The email config data to be saved or updated.
   * @return The saved or updated email config.
   * @throws BusinessException If an error occurs during the operation.
   */
  EmailConfigResponse createOrUpdate(EmailConfigRequest emailConfigRequest)
      throws BusinessException;

  /**
   * Finds all email config.
   *
   * @param paginationRequest the pagination request
   * @return a paginated list of ApplicationResponseDto
   */
  Page<EmailConfigResponse> findAll(EmailConfigPaginationRequest paginationRequest);

  /**
   * Checks if an entry exists by username and protocol types.
   *
   * @param email         the email to filter by
   * @param protocolTypes the list of protocol types to filter by
   * @return true if an entry exists, false otherwise
   */
  boolean existByEmailAndProtocolTypeIn(String email, List<EmailProtocolTypeEnum> protocolTypes);

  /**
   * Checks if an entry exists by username and protocol types.
   *
   * @param email         the email to filter by
   * @param protocolTypes the list of protocol types to filter by
   * @return optional of EmailConfigEntity
   */
  Optional<EmailConfigEntity> findByEmailAndProtocolType(String email,
                                                         EmailProtocolTypeEnum protocolTypes);

  /**
   * Checks if an entry exists by username and protocol types, excluding a specific ID.
   *
   * @param id            the ID to exclude
   * @param email         the email to filter by
   * @param protocolTypes the list of protocol types to filter by
   * @return true if an entry exists, false otherwise
   */
  boolean existByIdNotAndEmailAndProtocolTypeIn(Long id, String email,
                                                List<EmailProtocolTypeEnum> protocolTypes);

  /**
   * Returns the count of EmailConfigEntity entries that match the provided username
   * and protocol types, excluding the specified ID.
   *
   * @param protocolType the list of protocol types to filter by
   * @return the list of matching EmailConfigEntity entries
   */
  List<EmailConfigEntity> findAllByProtocolTypeIn(Collection<EmailProtocolTypeEnum> protocolType);

  /**
   * update status active or inactive.
   *
   * @param id id of  EmailConfig.
   * @return The data of EmailConfigResponse object with id of CollectEmailConfig.
   */
  EmailConfigResponse updateStatus(Long id) throws BusinessException;

  /**
   * delete email config by id.
   *
   * @param id id of  EmailConfig.
   */
  void deleteEmailConfigById(Long id) throws BusinessException;

}
