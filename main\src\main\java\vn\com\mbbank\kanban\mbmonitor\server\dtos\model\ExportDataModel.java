package vn.com.mbbank.kanban.mbmonitor.server.dtos.model;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileTypeEnum;

/**
 * Model request export data message of kafka .
 */
@Data
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExportDataModel {
  Long id;
  String fileName;
  ExportFileTypeEnum extension;
}
