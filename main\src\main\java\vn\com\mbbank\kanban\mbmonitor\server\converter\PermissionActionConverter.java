package vn.com.mbbank.kanban.mbmonitor.server.converter;

import jakarta.persistence.AttributeConverter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/8/2024
 */
public class PermissionActionConverter implements AttributeConverter<PermissionActionEnum, String> {
  @Override
  public String convertToDatabaseColumn(PermissionActionEnum attribute) {
    return attribute != null ? attribute.name() : null;
  }

  @Override
  public PermissionActionEnum convertToEntityAttribute(String dbData) {
    try {
      return PermissionActionEnum.valueOf(dbData);
    } catch (IllegalArgumentException | NullPointerException e) {
      // Return a default value if the database value is invalid
      return PermissionActionEnum.UNKNOWN;  // Replace with your default value
    }
  }
}
