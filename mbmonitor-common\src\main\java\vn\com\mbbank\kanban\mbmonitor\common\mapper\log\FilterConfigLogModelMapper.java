package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.FilterConfigLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FilterAlertConfigEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FilterConfigLogModelMapper extends
    KanbanBaseMapper<FilterConfigLogModel, FilterAlertConfigEntity> {
  FilterConfigLogModelMapper INSTANCE = Mappers.getMapper(FilterConfigLogModelMapper.class);

  /**
   * map FilterAlertConfigEntity to FilterConfigLogModel.
   *
   * @param config AlertGroupConfigEntity.
   * @return FilterConfigLogModel
   */
  default FilterConfigLogModel map(FilterAlertConfigEntity config) {
    return FilterConfigLogModel.builder()
        .name(config.getName())
        .description(config.getDescription())
        .condition(config.getRuleGroup().toString())
        .active(config.getActive())
        .build();
  }
}
