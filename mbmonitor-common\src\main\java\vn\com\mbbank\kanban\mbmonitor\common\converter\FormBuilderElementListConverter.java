package vn.com.mbbank.kanban.mbmonitor.common.converter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.ArrayList;
import java.util.List;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.BaseFormBuilderElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.CheckboxElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.DateTimeElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.NumberInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.RadioElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.SectionElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.SelectElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.TableInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.TextInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.TextareaInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.TimeDurationElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.FormBuilderElementTypeEnum;

/**
 * FormBuilderElementListConverter.
 */
@Converter
public class FormBuilderElementListConverter implements AttributeConverter<List<BaseFormBuilderElementModel>, String> {

  @Override
  public String convertToDatabaseColumn(List<BaseFormBuilderElementModel> attribute) {
    if (attribute == null) {
      return null;
    }
    return JSON.toJSONString(attribute);
  }

  @Override
  public List<BaseFormBuilderElementModel> convertToEntityAttribute(String dbData) {
    if (KanbanCommonUtil.isEmpty(dbData)) {
      return new ArrayList<>();
    }

    JSONArray array = JSON.parseArray(dbData);
    List<BaseFormBuilderElementModel> result = new ArrayList<>();

    for (int i = 0; i < array.size(); i++) {
      JSONObject obj = array.getJSONObject(i);
      FormBuilderElementTypeEnum type = obj.getObject("type", FormBuilderElementTypeEnum.class);
      BaseFormBuilderElementModel element = switch (type) {
        case TEXT -> obj.toJavaObject(TextInputElementModel.class);
        case TEXTAREA -> obj.toJavaObject(TextareaInputElementModel.class);
        case NUMBER -> obj.toJavaObject(NumberInputElementModel.class);
        case CHECKBOX -> obj.toJavaObject(CheckboxElementModel.class);
        case RADIO -> obj.toJavaObject(RadioElementModel.class);
        case SELECT -> obj.toJavaObject(SelectElementModel.class);
        case DATETIME -> obj.toJavaObject(DateTimeElementModel.class);
        case TIME_DURATION -> obj.toJavaObject(TimeDurationElementModel.class);
        case TABLE_INPUT -> obj.toJavaObject(TableInputElementModel.class);
        case SECTION -> obj.toJavaObject(SectionElementModel.class);
      };

      result.add(element);
    }

    return result;
  }
}
