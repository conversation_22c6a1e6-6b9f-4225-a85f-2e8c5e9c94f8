package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertRequestEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 06/12/2025
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlertRequestResponse extends AlertRequestEntity {
  private String serviceName;
  private String applicationName;
  private String priorityName;
  private AlertRequestDatabaseResponse alertRequestDatabase;
}