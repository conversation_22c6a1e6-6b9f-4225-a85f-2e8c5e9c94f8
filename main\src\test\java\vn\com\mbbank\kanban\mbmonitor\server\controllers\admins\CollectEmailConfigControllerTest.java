package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.CollectEmailConfigModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.CollectEmailConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CollectEmailConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.CollectEmailConfigService;
import vn.com.mbbank.kanban.test.ControllerTest;

class CollectEmailConfigControllerTest extends ControllerTest {

  @Mock
  private CollectEmailConfigService collectEmailConfigService;

  @InjectMocks
  @Spy
  private CollectEmailConfigController collectEmailConfigController;

  @BeforeEach
  void setup() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void findAll_success() {
    verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.EMAIL_COLLECT, PermissionActionEnum.VIEW)),
        "findAll", CollectEmailConfigController.class);
    Page<CollectEmailConfigModel> mockPage =
        new PageImpl<>(Collections.singletonList(new CollectEmailConfigModel()));
    when(collectEmailConfigService.findAll(any(PaginationRequest.class))).thenReturn(mockPage);

    PaginationRequest paginationRequest = new PaginationRequest();
    ResponseData<Page<CollectEmailConfigModel>> response =
        collectEmailConfigController.findAll(paginationRequest);

    assertEquals(1, response.getData().getContent().size());
    verify(collectEmailConfigService, times(1)).findAll(paginationRequest);
  }

  @Test
  void createOrUpdate_success() throws Exception {
    doNothing().when(collectEmailConfigController)
        .makeSureCreateOrUpdate(any(), any(), any(), any());
    CollectEmailConfigRequest request = new CollectEmailConfigRequest();
    request.setIntervalTime(10L);
    CollectEmailConfigResponse mockResponse = new CollectEmailConfigResponse();
    when(collectEmailConfigService.createOrUpdate(any(CollectEmailConfigRequest.class))).thenReturn(
        mockResponse);

    ResponseData<CollectEmailConfigResponse> response =
        collectEmailConfigController.createOrUpdate(request);

    assertEquals(mockResponse, response.getData());
    verify(collectEmailConfigService, times(1)).createOrUpdate(request);
  }

  @Test
  void findById_success() throws Exception {
    verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.EMAIL_COLLECT, PermissionActionEnum.VIEW)),
        "findById", CollectEmailConfigController.class);
    Long id = 1L;
    CollectEmailConfigModel mockResponse = new CollectEmailConfigModel();
    when(collectEmailConfigService.findCollectEmailConfigById(id)).thenReturn(mockResponse);

    ResponseData<CollectEmailConfigResponse> response = collectEmailConfigController.findById(id);

    verify(collectEmailConfigService, times(1)).findCollectEmailConfigById(id);
  }

  @Test
  void deleteById_success() throws BusinessException {
    verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.EMAIL_COLLECT, PermissionActionEnum.DELETE)),
        "deleteById", CollectEmailConfigController.class);
    Long id = 1L;
    doNothing().when(collectEmailConfigService).deleteWithId(id);

    ResponseData<String> response = collectEmailConfigController.deleteById(id);

    assertEquals("OK", response.getData());
    verify(collectEmailConfigService, times(1)).deleteWithId(id);
  }

  @Test
  void active_success() throws Exception {
    verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.EMAIL_COLLECT, PermissionActionEnum.EDIT)),
        "active", CollectEmailConfigController.class);
    Long id = 1L;
    CollectEmailConfigResponse mockResponse = new CollectEmailConfigResponse();
    when(collectEmailConfigService.updateStatus(id)).thenReturn(mockResponse);

    ResponseData<CollectEmailConfigResponse> response = collectEmailConfigController.active(id);

    assertEquals(mockResponse, response.getData());
    verify(collectEmailConfigService, times(1)).updateStatus(id);
  }
}
