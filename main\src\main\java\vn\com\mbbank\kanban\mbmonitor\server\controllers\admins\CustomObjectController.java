package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.CustomObjectRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CustomObjectDependenciesResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CustomObjectResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.CustomObjectResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.CustomObjectService;

/**
 * Controller logic service.
 */
@RestController
@RequestMapping(ServerUrl.CUSTOM_OBJECT_URL)
@RequiredArgsConstructor
public class CustomObjectController extends BaseController {
  private final CustomObjectService customObjectService;
  private final CustomObjectResponseMapper customObjectResponseMapper =
      CustomObjectResponseMapper.INSTANCE;

  /**
   * find all custom object by paginationRequest.
   *
   * @param paginationRequest Pagination parameters for the request.
   * @param withDeleted       get deleted item option
   * @param name              name of custom object
   * @return ResponseEntity containing the response data with HTTP status OK.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.CUSTOM_OBJECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.EMAIL_COLLECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_GROUP_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.FILTER_ALERT_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MODIFY_ALERT_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.AUTO_TRIGGER_ACTION_CONFIG, action = PermissionActionEnum.VIEW),
  })
  @GetMapping
  public ResponseData<Page<CustomObjectResponse>> findAll(
      @ModelAttribute PaginationRequest paginationRequest,
      @RequestParam(value = "name", required = false, defaultValue = "")
      String name,
      @RequestParam(value = "withDeleted", required = false, defaultValue = "false") boolean withDeleted) {
    paginationRequest.setPropertiesSearch(List.of("name", "description"));
    if (!KanbanCommonUtil.isEmpty(name)) {
      paginationRequest.setPropertiesSearch(List.of("name"));
      paginationRequest.setSearch(name);
    }
    var res = withDeleted ? customObjectService.findAllWithPagingAndWithDeleted(paginationRequest) :
        customObjectService.findAllWithPaging(paginationRequest);
    return ResponseUtils.success(res.map(customObjectResponseMapper::map));
  }

  /**
   * find all dependencies by custom object id.
   *
   * @param id id of custom object
   * @return list dependencies
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.CUSTOM_OBJECT, action = PermissionActionEnum.EDIT),
      @AclPermission(module = PermissionModuleEnum.CUSTOM_OBJECT, action = PermissionActionEnum.DELETE),
  })
  @Operation(summary = "Find all dependencies by custom object id")
  @GetMapping("{id}/dependencies")
  public ResponseData<CustomObjectDependenciesResponse> findAllDependenciesById(
      @PathVariable Long id)
      throws Exception {
    return ResponseUtils.success(customObjectService.findAllDependenciesById(id));
  }


  /**
   * Find By id.
   *
   * @param id id
   * @return data
   */
  @GetMapping("{id}")
  public ResponseData<CustomObjectResponse> findById(@PathVariable Long id) {
    return ResponseUtils.success(customObjectResponseMapper.map(customObjectService.findById(id)));
  }

  /**
   * Create or update custom object.
   *
   * @param customObjectRequest CustomObjectRequest
   * @return new service CustomObjectEntity
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.CUSTOM_OBJECT, action = PermissionActionEnum.CREATE),
      @AclPermission(module = PermissionModuleEnum.CUSTOM_OBJECT, action = PermissionActionEnum.EDIT),
  })
  @PostMapping
  public ResponseData<CustomObjectResponse> createOrUpdate(
      @RequestBody @Valid CustomObjectRequest customObjectRequest
  ) throws BusinessException {
    return ResponseUtils.success(
        customObjectResponseMapper.map(customObjectService.createOrUpdate(customObjectRequest)));
  }

  /**
   * Delete by Id.
   *
   * @param id id
   * @return Object
   */

  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.CUSTOM_OBJECT, action = PermissionActionEnum.DELETE)
  })
  @Operation(summary = "Delete custom object by id")
  @DeleteMapping("/{id}")
  public ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    customObjectService.deleteCustomObjectById(id);
    return ResponseUtils.success("OK");
  }

  /**
   * Delete by batch.
   *
   * @param ids list ids
   * @return status
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.CUSTOM_OBJECT, action = PermissionActionEnum.DELETE)
  })
  @Operation(summary = "Delete custom object by list ID")
  @DeleteMapping("/batch")
  public ResponseData<String> deleteByIdIn(@RequestParam(value = "ids[]") List<Long> ids) {
    customObjectService.deleteByIdIn(ids);
    return ResponseUtils.success("OK");
  }
}
