package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ExecutionRepositoryCustom;

/**
 * ExecutionRepositoryCustomImpl.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ExecutionRepositoryCustomImpl implements ExecutionRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public Page<ExecutionResponse> findAll(PaginationRequestDTO paginationRequest) {
    var query = new PrepareQuery("""
        SELECT execution.ID,
              execution.NAME,
              execution.DESCRIPTION,
              execution.TYPE,
              execution.DATABASE_CONNECTION_ID,
              execution.SCRIPT,
              execution.EXECUTION_GROUP_ID,
              executionGroup.NAME executionGroupName
        FROM EXECUTION execution
                LEFT JOIN EXECUTION_GROUP executionGroup
                          ON execution.EXECUTION_GROUP_ID = executionGroup.ID
        WHERE 1 = 1
        """
    ).append(buildQuerySearchLike(paginationRequest.getSearch()))
        .append(buildOrderQuery(paginationRequest));
    Pageable pageable = PageRequest.of(paginationRequest.getPage(),
        paginationRequest.getSize());
    return sqlQueryUtil.queryModel()
        .queryPaging(query.getQueryBuilder().toString(), query.getParams(),
            ExecutionResponse.class, pageable);
  }

  @Override
  public List<ExecutionResponse> findAllByType(ExecutionTypeEnum type) {
    var query = new PrepareQuery("""
        SELECT execution.ID,
              execution.NAME,
              execution.DESCRIPTION,
              execution.TYPE,
              execution.DATABASE_CONNECTION_ID,
              execution.SCRIPT,
              execution.EXECUTION_GROUP_ID,
              executionGroup.NAME executionGroupName
        FROM EXECUTION execution
                LEFT JOIN EXECUTION_GROUP executionGroup
                          ON execution.EXECUTION_GROUP_ID = executionGroup.ID
        WHERE 1 = 1
        """
    );

    if (!KanbanCommonUtil.isEmpty(type)) {
      query.append(" AND execution.type = :type", "type", type.name());
    }

    return sqlQueryUtil.queryModel()
        .queryForList(query.getQueryBuilder().toString(), query.getParams(),
            ExecutionResponse.class);
  }

  @Override
  public List<String> findAllAutoTriggerDependenciesNameById(String executionId) {
    var query = new PrepareQuery("""
        SELECT autoTriggerConfig.NAME
         FROM AUTO_TRIGGER_ACTION_CONFIG_EXECUTION_MAPPER executionMapper
             LEFT JOIN AUTO_TRIGGER_ACTION_CONFIG autoTriggerConfig
                 ON autoTriggerConfig.ID = executionMapper.AUTO_TRIGGER_ACTION_CONFIG_ID
         WHERE executionMapper.EXECUTION_ID = :executionId
        """, "executionId", executionId);

    return sqlQueryUtil.queryModel()
        .queryForList(query.getQueryBuilder().toString(), query.getParams(),
            String.class);
  }

  @Override
  public List<String> findAllWorkflowTemplateDependenciesNameById(String executionId) {
    var query = new PrepareQuery("""
        SELECT workflowTemplate.NAME
        FROM WORKFLOW_NODE_DEPENDENCY workflowNodeDependency
                 LEFT JOIN WORKFLOW_TEMPLATE workflowTemplate
                 ON workflowNodeDependency.WORKFLOW_ID = workflowTemplate.WORKFLOW_ID
        WHERE workflowNodeDependency.REFERENCE_ID = :executionId
        AND (workflowNodeDependency.TYPE IN ('EXECUTION', 'API', 'SQL', 'PYTHON'))
        """, "executionId", executionId);
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQueryBuilder().toString(), query.getParams(),
            String.class);
  }

  protected PrepareQuery buildQuerySearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    return new PrepareQuery("AND (")
        .append(new PrepareQuery("LOWER(execution.NAME) LIKE :search"),
            LikeMatcher.CONTAINING)
        .append(
            new PrepareQuery(" OR LOWER(executionGroup.NAME) LIKE :search"),
            LikeMatcher.CONTAINING)
        .append(
            new PrepareQuery(" OR LOWER(execution.DESCRIPTION) LIKE :search",
                Map.of("search", search.toLowerCase())),
            LikeMatcher.CONTAINING)
        .append(")");
  }

  protected PrepareQuery buildOrderQuery(PaginationRequestDTO paginationRequest) {
    var sortBy = paginationRequest.getSortBy();
    PrepareQuery query;
    if (StringUtils.isBlank(sortBy)) {
      query = new PrepareQuery(" ORDER BY execution.CREATED_DATE ");
    } else {
      query = switch (sortBy) {
        case "name" -> new PrepareQuery(" ORDER BY execution.NAME ");
        case "description" -> new PrepareQuery(" ORDER BY execution.DESCRIPTION ");
        case "type" -> new PrepareQuery(" ORDER BY execution.TYPE ");
        case "executionGroupName" -> new PrepareQuery(" ORDER BY executionGroup.NAME ");
        default -> new PrepareQuery(" ORDER BY execution.CREATED_DATE ");
      };
    }
    return query.append(
        Objects.isNull(paginationRequest.getSortOrder()) ? null : paginationRequest.getSortOrder().name());
  }
}
