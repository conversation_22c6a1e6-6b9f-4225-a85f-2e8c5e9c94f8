package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FilterAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupConverter;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.FilterAlertConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.FilterAlertPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.FilterAlertConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.FilterAlertConfigRepository;

@ExtendWith({MockitoExtension.class})
class FilterAlertConfigServiceImplTest {
  @Mock
  FilterAlertConfigRepository filterAlertConfigRepository;
  @InjectMocks
  FilterAlertConfigServiceImpl filterAlertConfigServiceImpl;
  @Mock
  SysLogKafkaProducerService sysLogKafkaProducerService;

  @Test
  void getRepository_success() {
    JpaCommonRepository<FilterAlertConfigEntity, Long> result = filterAlertConfigServiceImpl.getRepository();
    assertEquals(filterAlertConfigRepository, result);
  }


  @Test
  void findAllWithSearch() {
    FilterAlertPaginationRequest request = new FilterAlertPaginationRequest();
    Page<FilterAlertConfigResponse> mockPage = mock(Page.class);
    when(filterAlertConfigRepository.findAllBySearch(request)).thenReturn(mockPage);

    Page<FilterAlertConfigResponse> result = filterAlertConfigServiceImpl.findAllWithSearch(request);

    assertThat(result).isEqualTo(mockPage);
    verify(filterAlertConfigRepository, times(1)).findAllBySearch(request);
  }

  @Test
  void shouldThrowExceptionWhenUpdatingNonExistentConfig() {
    // Given
    FilterAlertConfigRequest request = new FilterAlertConfigRequest();
    request.setId(999L);
    request.setName("");
    Mockito.when(filterAlertConfigRepository.findById(999L)).thenReturn(Optional.empty());

    // When & Then
    BusinessException exception = Assertions.assertThrows(
        BusinessException.class,
        () -> filterAlertConfigServiceImpl.save(request)
    );

    Assertions.assertEquals(ErrorCode.FILTER_ALERT_CONFIG_NOT_FOUND.getCode(), exception.getCode());
  }


  @Test
  void findByIdWithDetail_NotFound() {
    when(filterAlertConfigRepository.findById(anyLong())).thenReturn(Optional.empty());

    BusinessException exception =
        assertThrows(BusinessException.class, () -> filterAlertConfigServiceImpl.findByIdWithDetail(1L));
    assertThat(exception.getCode()).isEqualTo(ErrorCode.FILTER_ALERT_CONFIG_NOT_FOUND.getCode());
  }

  @Test
  void findByIdWithDetail_Success() throws BusinessException {
    FilterAlertConfigEntity entity = new FilterAlertConfigEntity();
    when(filterAlertConfigRepository.findById(anyLong())).thenReturn(Optional.of(entity));

    var result = filterAlertConfigServiceImpl.findByIdWithDetail(1L);

    assertThat(result).isNotNull();
    verify(filterAlertConfigRepository, times(1)).findById(anyLong());
  }

  @Test
  void shouldThrowExceptionWhenConfigNotFound() {
    // Given
    Long id = 1L;
    Mockito.when(filterAlertConfigRepository.findById(id)).thenReturn(Optional.empty());

    // When & Then
    BusinessException exception = Assertions.assertThrows(
        BusinessException.class,
        () -> filterAlertConfigServiceImpl.findByIdWithDetail(id)
    );

    Assertions.assertEquals(ErrorCode.FILTER_ALERT_CONFIG_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void updateActive_NotFound() {
    when(filterAlertConfigRepository.findById(anyLong())).thenReturn(Optional.empty());
    BusinessException exception =
        assertThrows(BusinessException.class, () -> filterAlertConfigServiceImpl.updateActive(1L, true));
    assertThat(exception.getCode()).isEqualTo(ErrorCode.FILTER_ALERT_CONFIG_NOT_FOUND.getCode());
  }

  @Test
  void updateActive_Success() throws BusinessException {
    FilterAlertConfigEntity entity = new FilterAlertConfigEntity();
    when(filterAlertConfigRepository.findById(anyLong())).thenReturn(Optional.of(entity));
    when(filterAlertConfigRepository.save(any(FilterAlertConfigEntity.class))).thenReturn(entity);

    FilterAlertConfigEntity result = filterAlertConfigServiceImpl.updateActive(1L, true);

    assertThat(result.getActive()).isTrue();
    verify(filterAlertConfigRepository, times(1)).save(entity);
  }


  @Test
  void updateDeactivate_NextTime_Success() throws BusinessException {
    FilterAlertConfigEntity entity = new FilterAlertConfigEntity();
    when(filterAlertConfigRepository.findById(anyLong())).thenReturn(Optional.of(entity));
    when(filterAlertConfigRepository.save(any(FilterAlertConfigEntity.class))).thenReturn(entity);
    FilterAlertConfigEntity result = filterAlertConfigServiceImpl.updateActive(1L, false);
    assertThat(result.getActive()).isFalse();
    verify(filterAlertConfigRepository, times(1)).save(entity);
  }

  @Test
  void shouldThrowExceptionWhenNameAlreadyExistsInUpdateMode() {
    // Arrange
    var request = new FilterAlertConfigRequest();
    request.setId(1L);
    request.setName("TestName");

    when(filterAlertConfigRepository.existsByIdNotAndNameIgnoreCase(1L, "TestName")).thenReturn(true);

    // Act & Assert
    var exception = assertThrows(BusinessException.class, () ->
        filterAlertConfigServiceImpl.validateFilterAlertRequest(request)
    );

    assertEquals(ErrorCode.FILTER_ALERT_CONFIG_NAME_IS_EXISTED.getCode(), exception.getCode());
  }

  @Test
  void shouldPassValidationForValidRequest() {
    // Arrange
    var request = new FilterAlertConfigRequest();
    request.setName("ValidName");

    when(filterAlertConfigRepository.existsByNameIgnoreCase("ValidName")).thenReturn(false);
    // Act & Assert
    assertDoesNotThrow(() -> filterAlertConfigServiceImpl.validateFilterAlertRequest(request));
  }


  @Test
  void shouldThrowExceptionWhenNameAlreadyExistsInCreateMode() {
    // Arrange
    var request = new FilterAlertConfigRequest();
    request.setName("TestName");
    when(filterAlertConfigRepository.existsByNameIgnoreCase("TestName")).thenReturn(true);

    // Act & Assert
    var exception = assertThrows(BusinessException.class, () ->
        filterAlertConfigServiceImpl.validateFilterAlertRequest(request)
    );
    assertEquals(ErrorCode.FILTER_ALERT_CONFIG_NAME_IS_EXISTED.getCode(), exception.getCode());
  }

  @Test
  void shouldThrowExceptionWhenRequestIsNull() {
    // Act & Assert
    assertThrows(NullPointerException.class, () ->
        filterAlertConfigServiceImpl.validateFilterAlertRequest(null)
    );
  }

  @Test
  void deleteConfigById_shouldDoNothing_whenConfigDoesNotExist() throws BusinessException {
    // Arrange
    Long id = 1L;
    when(filterAlertConfigRepository.findById(anyLong())).thenReturn(Optional.of(new FilterAlertConfigEntity()));
    // Act
    filterAlertConfigServiceImpl.deleteWithId(id);

  }

  @Test
  void deleteConfigById_failed_caseNotFoundConfig() throws BusinessException {
    // Arrange
    Long id = 1L;
    when(filterAlertConfigRepository.findById(anyLong())).thenReturn(Optional.empty());
    // Act
    assertThrows(BusinessException.class, () -> filterAlertConfigServiceImpl.deleteWithId(id));

  }

  @Test
  void findAllByCustomObjectId_success2() {
    var alertGroupConfig = new FilterAlertConfigEntity();
    alertGroupConfig.setName("a");
    var alertGroupConfig2 = new FilterAlertConfigEntity();
    alertGroupConfig2.setName("b");
    alertGroupConfig2.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"102\",\"operator\":\"IS\",\"value\":\"a\"}]}"));

    when(filterAlertConfigRepository.findAll()).thenReturn(List.of(alertGroupConfig, alertGroupConfig2));
    var res = filterAlertConfigServiceImpl.findAllByCustomObjectId(102L);
    assertEquals(1, res.size());
  }

  @Test
  void findAllByCustomObjectId_notfound() {
    var alertGroupConfig = new FilterAlertConfigEntity();
    alertGroupConfig.setName("a");
    var alertGroupConfig2 = new FilterAlertConfigEntity();
    alertGroupConfig2.setName("b");
    alertGroupConfig2.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"priority\",\"operator\":\"IS_ONE_OF\",\"value\":[\"2001\"]}]}"));
    when(filterAlertConfigRepository.findAll()).thenReturn(List.of(alertGroupConfig, alertGroupConfig2));
    var res = filterAlertConfigServiceImpl.findAllByCustomObjectId(2002L);
    assertEquals(0, res.size());
  }

  @Test
  void findAllByCustomObjectId_ruleNull() {
    var alertGroupConfig = new FilterAlertConfigEntity();
    alertGroupConfig.setName("a");
    var alertGroupConfig2 = new FilterAlertConfigEntity();
    alertGroupConfig2.setName("b");
    when(filterAlertConfigRepository.findAll()).thenReturn(List.of(alertGroupConfig, alertGroupConfig2));
    var res = filterAlertConfigServiceImpl.findAllByCustomObjectId(102L);
    assertEquals(0, res.size());
  }

  @Test
  void testFindAllByCustomObjectId_emptyRepository() {
    when(filterAlertConfigRepository.findAll()).thenReturn(Collections.emptyList());

    // Test the method
    List<String> result = filterAlertConfigServiceImpl.findAllByCustomObjectId(1L);

    // Verify results
    assertNotNull(result);
    assertTrue(result.isEmpty());
    verify(filterAlertConfigRepository, times(1)).findAll();
  }

  @Test
  void save_CreateMode() throws BusinessException {
    FilterAlertConfigRequest request = new FilterAlertConfigRequest();
    request.setId(null); // Tạo mới
    request.setName("test alert");
    request.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"102\",\"operator\":\"IS\",\"value\":\"a\"}]}"));
    FilterAlertConfigEntity entity = new FilterAlertConfigEntity();
    entity.setId(1L);
    entity.setName("Test alert");
    entity.setActive(false);
    entity.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"102\",\"operator\":\"IS\",\"value\":\"a\"}]}"));

    // Mock repository save
    when(filterAlertConfigRepository.save(any())).thenReturn(entity);

    // When
    FilterAlertConfigEntity savedEntity = filterAlertConfigServiceImpl.save(request);

    // Then
    assertNotNull(savedEntity);
    assertEquals("Test alert", savedEntity.getName());
    assertFalse(savedEntity.getActive());

    verify(filterAlertConfigRepository).save(any());
  }

  @Test
  void testSave_UpdateMode() throws BusinessException {
    FilterAlertConfigRequest request = new FilterAlertConfigRequest();
    request.setId(1L); // Cập nhật
    request.setName("updated alert");
    request.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"102\",\"operator\":\"IS\",\"value\":\"a\"}]}"));
    FilterAlertConfigEntity existingEntity = new FilterAlertConfigEntity();
    existingEntity.setId(1L);
    existingEntity.setName("Old alert");
    existingEntity.setActive(true);
    existingEntity.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"102\",\"operator\":\"IS\",\"value\":\"a\"}]}"));

    // Mock findById và save
    when(filterAlertConfigRepository.findById(1L)).thenReturn(Optional.of(existingEntity));
    when(filterAlertConfigRepository.save(any())).thenReturn(existingEntity);

    // When
    FilterAlertConfigEntity updatedEntity = filterAlertConfigServiceImpl.save(request);

    // Then
    assertNotNull(updatedEntity);
    assertEquals("Updated alert", updatedEntity.getName());
    assertTrue(updatedEntity.getActive()); // Không thay đổi trạng thái

    verify(filterAlertConfigRepository).findById(1L);
    verify(filterAlertConfigRepository).save(existingEntity);
  }

  @Test
  void testSave_FilterAlertConfigNotFound() {
    // Given
    FilterAlertConfigRequest request = new FilterAlertConfigRequest();
    request.setId(99L); // Không tồn tại
    request.setName("");
    when(filterAlertConfigRepository.findById(99L)).thenReturn(Optional.empty());

    // When - Then
    BusinessException thrown = assertThrows(
        BusinessException.class,
        () -> filterAlertConfigServiceImpl.save(request)
    );

    assertEquals(ErrorCode.FILTER_ALERT_CONFIG_NOT_FOUND.getCode(), thrown.getCode());
    verify(filterAlertConfigRepository).findById(99L);
    verify(filterAlertConfigRepository, never()).save(any());
  }
}
