package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Collections;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseCollectRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.SampleDataRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.DatabaseCollectResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ExternalDatabaseUrl;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.DatabaseCollectModelToResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.DatabaseCollectResponseToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.SqlExecutionDataResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.DatabaseCollectService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/12/2024
 */
@RestController
@RequestMapping(ExternalDatabaseUrl.DATABASE_COLLECT)
@AllArgsConstructor
public class DatabaseCollectController extends BaseController {
  @Autowired
  @Lazy
  DatabaseCollectService databaseCollectService;

  /**
   * Find All database collect.
   *
   * @param request request
   * @return list database collect
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.DATABASE_COLLECT, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  public ResponseData<Page<DatabaseCollectResponse>> findAll(HttpServletRequest request) {
    return ResponseUtils.success(DatabaseCollectResponseToEntityMapper.INSTANCE.map(
        databaseCollectService.findAllWithPaging(getPaging(request))));
  }

  /**
   * Save database collect.
   *
   * @param request request
   * @return database collect after save
   * @throws BusinessException ex
   */
  @PostMapping
  public ResponseData<DatabaseCollectResponse> save(@RequestBody DatabaseCollectRequest request)
      throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.DATABASE_COLLECT, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT,
        Collections.singleton(request.getId()));
    return ResponseUtils.success(DatabaseCollectResponseToEntityMapper.INSTANCE.map(
        databaseCollectService.save(request)));
  }

  /**
   * Get xample databse collect.
   *
   * @param sampleDataRequest sampleDataRequest
   * @return data xample
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.DATABASE_COLLECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_COLLECT, action = PermissionActionEnum.EDIT)
  })
  @PostMapping("query-sample-data")
  public ResponseData<SqlExecutionDataResponse> getSampleData(
      @RequestBody SampleDataRequest sampleDataRequest) throws BusinessException {
    var result = databaseCollectService.getSampleData(sampleDataRequest);
    SqlExecutionDataResponse response = SqlExecutionDataResponse.builder()
        .listDataMappings(result.getListDataMappings())
        .listColumns(result.getListColumns()).build();
    return ResponseUtils.success(response);
  }

  /**
   * Delete database collect by id.
   *
   * @param id id
   * @return OK
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.DATABASE_COLLECT, action = PermissionActionEnum.DELETE)
  })
  @DeleteMapping("/{id}")
  public ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    databaseCollectService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }

  /**
   * Find database collect by id.
   *
   * @param id id
   * @return data
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.DATABASE_COLLECT, action = PermissionActionEnum.VIEW)
  })
  @GetMapping("/{id}")
  public ResponseData<DatabaseCollectResponse> findById(@PathVariable Long id)
      throws BusinessException {
    return ResponseUtils.success(
        DatabaseCollectModelToResponseMapper.INSTANCE.mapTo(
            databaseCollectService.findByIdWithNameDetails(id)));
  }

  /**
   * Active connection.
   *
   * @param id       id
   * @param isActive isActive
   * @return ok/exception
   * @throws BusinessException ex
   */

  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.DATABASE_COLLECT, action = PermissionActionEnum.EDIT),
  })

  @PutMapping(value = "/{id}", params = {"active"})
  public ResponseData<String> settingStatus(@PathVariable Long id,
                                            @RequestParam(value = "active") boolean isActive)
      throws BusinessException {
    databaseCollectService.setActiveById(id, isActive);
    return ResponseUtils.success("OK");
  }
}