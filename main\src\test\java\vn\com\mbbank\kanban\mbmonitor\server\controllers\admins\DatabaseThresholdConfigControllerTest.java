package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.DatabaseThresholdConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.DatabaseThresholdConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.DatabaseThresholdConfigService;
import vn.com.mbbank.kanban.test.ControllerTest;
import java.util.Collections;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class DatabaseThresholdConfigControllerTest extends ControllerTest {

  @Mock
  private DatabaseThresholdConfigService databaseThresholdConfigService;

  @InjectMocks
  @Spy
  private DatabaseThresholdConfigController databaseThresholdConfigController;

  @BeforeEach
  void setup() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void findAll_success() {
    verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, PermissionActionEnum.VIEW)),
        "findAll", DatabaseThresholdConfigController.class);
    Page<DatabaseThresholdConfigResponse> mockPage =
        new PageImpl<>(Collections.singletonList(new DatabaseThresholdConfigResponse()));
    when(databaseThresholdConfigService.findAll(any(PaginationRequest.class))).thenReturn(mockPage);

    PaginationRequest paginationRequest = new PaginationRequest();
    ResponseData<Page<DatabaseThresholdConfigResponse>> response =
        databaseThresholdConfigController.findAll(paginationRequest);

    assertEquals(1, response.getData().getContent().size());
    verify(databaseThresholdConfigService, times(1)).findAll(paginationRequest);
  }

  @Test
  void createOrUpdate_success() throws Exception {
    doNothing().when(databaseThresholdConfigController)
        .makeSureCreateOrUpdate(any(), any(), any(), any());
    DatabaseThresholdConfigRequest request = new DatabaseThresholdConfigRequest();
    DatabaseThresholdConfigResponse mockResponse = new DatabaseThresholdConfigResponse();
    when(databaseThresholdConfigService.createOrUpdate(any(DatabaseThresholdConfigRequest.class))).thenReturn(
        mockResponse);

    ResponseData<DatabaseThresholdConfigResponse> response =
        databaseThresholdConfigController.createOrUpdate(request);

    assertEquals(mockResponse, response.getData());
    verify(databaseThresholdConfigService, times(1)).createOrUpdate(request);
  }

  @TestForDev
  void findById_success() throws Exception {
    verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, PermissionActionEnum.VIEW)),
        "findById", DatabaseThresholdConfigController.class);
    String id = "123";
    DatabaseThresholdConfigResponse mockResponse = new DatabaseThresholdConfigResponse();
    when(databaseThresholdConfigService.findWithId(id)).thenReturn(mockResponse);

    ResponseData<DatabaseThresholdConfigResponse> response = databaseThresholdConfigController.findById(id);

    verify(databaseThresholdConfigService, times(1)).findWithId(id);
  }

  @TestForDev
  void deleteById_success() throws BusinessException {
    verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, PermissionActionEnum.DELETE)),
        "deleteById", DatabaseThresholdConfigController.class);
    String id = "1L";
    doNothing().when(databaseThresholdConfigService).deleteWithId(id);

    ResponseData<String> response = databaseThresholdConfigController.deleteById(id);

    assertEquals("OK", response.getData());
    verify(databaseThresholdConfigService, times(1)).deleteWithId(id);
  }

  @TestForDev
  void active_success() throws Exception {
    verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, PermissionActionEnum.EDIT)),
        "active", DatabaseThresholdConfigController.class);
    String id = "123";
    DatabaseThresholdConfigResponse mockResponse = new DatabaseThresholdConfigResponse();
    when(databaseThresholdConfigService.updateStatus(id)).thenReturn(mockResponse);

    ResponseData<DatabaseThresholdConfigResponse> response = databaseThresholdConfigController.active(id);

    assertEquals(mockResponse, response.getData());
    verify(databaseThresholdConfigService, times(1)).updateStatus(id);
  }
}
