package vn.com.mbbank.kanban.mbmonitor.common.entities;


import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.converter.IntegerArrayToStringConverter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.BrowserEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MonitorTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 29/05/2025
 */
@KanbanAutoGenerateUlId
@Getter
@Setter
@Entity
@Table(name = TableName.MONITOR_WEB_CONFIG)
public class MonitorWebConfigEntity extends BaseEntity<String> {
  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "NAME", nullable = false)
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "WEB_URL", nullable = false)
  private String webUrl;

  @Column(name = "MONITOR_TYPE", nullable = false)
  @Enumerated(EnumType.STRING)
  private MonitorTypeEnum monitorType;

  @Column(name = "TIME_OUT", nullable = false)
  private Integer timeout;

  @Column(name = "BROWSER", nullable = false)
  @Enumerated(EnumType.STRING)
  private BrowserEnum browser;

  @Column(name = "MONTHS")
  @Convert(converter = IntegerArrayToStringConverter.class)
  private Integer[] months;

  @Column(name = "DAY_OF_MONTHS")
  @Convert(converter = IntegerArrayToStringConverter.class)
  private Integer[] dayOfMonths;

  @Column(name = "DAY_OF_WEEKS")
  @Convert(converter = IntegerArrayToStringConverter.class)
  private Integer[] dayOfWeeks;

  @Column(name = "HOURS")
  @Convert(converter = IntegerArrayToStringConverter.class)
  private Integer[] hours;
  
  @Column(name = "MINUTES")
  @Convert(converter = IntegerArrayToStringConverter.class)
  private Integer[] minutes;

  @Column(name = "AUTH_ACTION_ID")
  private String authActionId;

  @Column(name = "ACTION_ID")
  private String actionId;

  @Column(name = "SERVICE_ID")
  private String serviceId;

  @Column(name = "APPLICATION_ID")
  private String applicationId;

  @Column(name = "PRIORITY_ID")
  private Long priorityId;

  @Column(name = "CONTACT")
  private String contact;

  @Column(name = "CONTENT")
  private String content;

  @Column(name = "CONTENT_JSON")
  private String contentJson;

  @Column(name = "ACTIVE")
  private Boolean active;

  @Override
  public String getId() {
    return id;
  }
}
