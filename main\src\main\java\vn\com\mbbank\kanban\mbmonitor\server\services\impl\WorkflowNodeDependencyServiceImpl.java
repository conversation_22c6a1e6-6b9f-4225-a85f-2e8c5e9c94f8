package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowNodeDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.WorkflowNodeDependencyRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.WorkflowNodeDependencyService;

@Service
@RequiredArgsConstructor
public class WorkflowNodeDependencyServiceImpl
    extends BaseServiceImpl<WorkflowNodeDependencyEntity, String>
    implements WorkflowNodeDependencyService {
  private final WorkflowNodeDependencyRepository workflowNodeDependencyRepository;

  @Override
  protected JpaCommonRepository<WorkflowNodeDependencyEntity, String> getRepository() {
    return workflowNodeDependencyRepository;
  }

  @Override
  @Transactional
  public void deleteAllByWorkflowId(String workflowId) {
    workflowNodeDependencyRepository.deleteAllByWorkflowId(workflowId);
  }

  @Override
  public List<WorkflowNodeDependencyEntity> findAllByWorkflowId(String workflowId) {
    return workflowNodeDependencyRepository.findAllByWorkflowId(workflowId);
  }
}
