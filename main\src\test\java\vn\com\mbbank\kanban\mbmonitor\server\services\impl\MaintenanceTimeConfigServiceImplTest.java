package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.enums.SortType;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeUnitEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupConverter;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.MaintenanceTimeConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.MaintenanceTimePaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.MaintenanceTimeConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.MaintenanceTimeConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.MaintenanceTimeConfigDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;

@ExtendWith({MockitoExtension.class})
class MaintenanceTimeConfigServiceImplTest {
  @Mock
  MaintenanceTimeConfigRepository maintenanceTimeConfigRepository;

  @Mock
  ServiceService serviceService;
  @Mock
  ApplicationService applicationService;

  @Mock
  MaintenanceTimeConfigDependencyService maintenanceTimeConfigDependencyService;
  @Mock
  SysLogKafkaProducerService sysLogKafkaProducerService;
  @InjectMocks
  @Spy
  MaintenanceTimeConfigServiceImpl maintenanceTimeConfigServiceImpl;

  @Test
  void getRepository_success() {
    JpaCommonRepository<MaintenanceTimeConfigEntity, Long> result = maintenanceTimeConfigServiceImpl.getRepository();
    assertEquals(maintenanceTimeConfigRepository, result);
  }

  @Test
  void findAll_success_caseWithDeleted() {
    Mockito.when(maintenanceTimeConfigRepository.findAllByActive(true))
        .thenReturn(List.of());
    var res = maintenanceTimeConfigServiceImpl.findAllByActive(true);
    Assertions.assertEquals(0, res.size());
  }

  @Test
  void save_CreateMode() throws BusinessException {
    MaintenanceTimeConfigRequest request = new MaintenanceTimeConfigRequest();
    request.setName("Test Config");
    request.setApplicationIds(List.of("app1", "app2"));
    request.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"102\",\"operator\":\"IS\",\"value\":\"a\"}]}"));
    request.setStartTime(new Date());
    when(maintenanceTimeConfigRepository.existsByNameIgnoreCase(request.getName())).thenReturn(false);
    when(applicationService.findAllByIdInAndServiceIdInAndDeleted(anyList(), anyList(), anyBoolean())).thenReturn(
        new ArrayList<>());
    when(serviceService.findAllByIdInAndDeleted(any(), anyBoolean()))
        .thenReturn(List.of(ServiceEntity.builder().id("1L").build()));
    MaintenanceTimeConfigEntity result = maintenanceTimeConfigServiceImpl.save(request);

    assertThat(result).isNotNull();
    verify(maintenanceTimeConfigRepository, times(1)).save(any(MaintenanceTimeConfigEntity.class));
  }
  @Test
  void save_CreateMode_error_emptyService() throws BusinessException {
    MaintenanceTimeConfigRequest request = new MaintenanceTimeConfigRequest();
    request.setName("Test Config");
    request.setApplicationIds(List.of("app1", "app2"));

    request.setStartTime(new Date());
    when(maintenanceTimeConfigRepository.existsByNameIgnoreCase(request.getName())).thenReturn(false);
    when(serviceService.findAllByIdInAndDeleted(any(),anyBoolean()))
        .thenReturn(List.of());
    assertThrows(BusinessException.class,()-> maintenanceTimeConfigServiceImpl.save(request));
  }


  @Test
  void save_success() throws BusinessException {
    MaintenanceTimeConfigRequest request = new MaintenanceTimeConfigRequest();
    request.setName("Test Config");
    request.setApplicationIds(List.of("app1", "app2"));
    request.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"102\",\"operator\":\"IS\",\"value\":\"a\"}]}"));
    request.setStartTime(new Date());
    when(maintenanceTimeConfigRepository.existsByNameIgnoreCase(request.getName())).thenReturn(false);
    var app = new ApplicationEntity();
    app.setId("1");
    app.setServiceId("1");
    when(applicationService.findAllByIdInAndServiceIdInAndDeleted(anyList(), anyList(), anyBoolean())).thenReturn(
        List.of(app));
    var service = new ServiceEntity();
    service.setId("1");
    var service2 = new ServiceEntity();
    service2.setId("2");
    when(serviceService.findAllByIdInAndDeleted(anyList(), anyBoolean())).thenReturn(
        List.of(service, service2));

    MaintenanceTimeConfigEntity result = maintenanceTimeConfigServiceImpl.save(request);

    assertThat(result).isNotNull();
  }

  @Test
  void save_CreateMode_StartTimeNull() throws BusinessException {
    MaintenanceTimeConfigRequest request = new MaintenanceTimeConfigRequest();
    request.setName("Test Config");
    request.setApplicationIds(List.of("app1", "app2"));
    request.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"102\",\"operator\":\"IS\",\"value\":\"a\"}]}"));
    when(maintenanceTimeConfigRepository.existsByNameIgnoreCase(request.getName())).thenReturn(false);
    when(applicationService.findAllByIdInAndServiceIdInAndDeleted(anyList(), anyList(), anyBoolean())).thenReturn(
        new ArrayList<>());
    when(serviceService.findAllByIdInAndDeleted(any(), anyBoolean()))
        .thenReturn(List.of(ServiceEntity.builder().id("1L").build()));
    MaintenanceTimeConfigEntity result = maintenanceTimeConfigServiceImpl.save(request);

    assertThat(result).isNotNull();
    verify(maintenanceTimeConfigRepository, times(1)).save(any(MaintenanceTimeConfigEntity.class));
  }

  @Test
  void save_UpdateMode() throws BusinessException {
    MaintenanceTimeConfigRequest request = new MaintenanceTimeConfigRequest();
    request.setId(1L);
    request.setName("Updated Config");
    request.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"102\",\"operator\":\"IS\",\"value\":\"a\"}]}"));

    MaintenanceTimeConfigEntity existingEntity = new MaintenanceTimeConfigEntity();
    existingEntity.setActive(true);
    existingEntity.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"102\",\"operator\":\"IS\",\"value\":\"a\"}]}"));
    when(maintenanceTimeConfigRepository.findById(request.getId())).thenReturn(Optional.of(existingEntity));
    when(maintenanceTimeConfigRepository.existsByIdNotAndNameIgnoreCase(request.getId(), request.getName())).thenReturn(
        false);
    when(serviceService.findAllByIdInAndDeleted(any(), anyBoolean()))
        .thenReturn(List.of(ServiceEntity.builder().id("1L").build()));
    MaintenanceTimeConfigEntity result = maintenanceTimeConfigServiceImpl.save(request);

    assertThat(result).isNotNull();
    verify(maintenanceTimeConfigRepository, times(1)).save(any(MaintenanceTimeConfigEntity.class));
    verify(maintenanceTimeConfigDependencyService, times(1)).deleteAllByMaintenanceTimeConfigId(request.getId());
  }

  @Test
  void shouldUpdateExistingMaintenanceTimeConfig() throws BusinessException {
    // Given
    MaintenanceTimeConfigRequest request = new MaintenanceTimeConfigRequest();
    request.setId(1L);
    request.setName("Updated Config");
    request.setServiceIds(List.of("service3", "serviceAll"));
    request.setApplicationIds(List.of("app3"));
    request.setStartTime(new Date());
    request.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"102\",\"operator\":\"IS\",\"value\":\"a\"}]}"));
    MaintenanceTimeConfigEntity existingEntity = new MaintenanceTimeConfigEntity();
    existingEntity.setId(1L);
    existingEntity.setActive(true);
    existingEntity.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"102\",\"operator\":\"IS\",\"value\":\"a\"}]}"));
    Mockito.when(maintenanceTimeConfigRepository.findById(1L)).thenReturn(Optional.of(existingEntity));
    Mockito.when(applicationService.findAllByIdInAndServiceIdInAndDeleted(anyList(), anyList(), anyBoolean()))
        .thenReturn(List.of(
            createApplicationEntity("app3", "service3")
        ));

    when(serviceService.findAllByIdInAndDeleted(any(), anyBoolean()))
        .thenReturn(List.of(ServiceEntity.builder().id("1L").build()));
    // When
    MaintenanceTimeConfigEntity result = maintenanceTimeConfigServiceImpl.save(request);

    // Then
    Mockito.verify(maintenanceTimeConfigDependencyService).deleteAllByMaintenanceTimeConfigId(1L);
    Mockito.verify(maintenanceTimeConfigRepository).save(existingEntity);
    Mockito.verify(maintenanceTimeConfigDependencyService).saveAll(Mockito.anyList());
    assertNotNull(result);
  }

  @Test
  void shouldThrowExceptionWhenUpdatingNonExistentConfig() {
    // Given
    MaintenanceTimeConfigRequest request = new MaintenanceTimeConfigRequest();
    request.setId(999L);
    request.setName("");
    Mockito.when(maintenanceTimeConfigRepository.findById(999L)).thenReturn(Optional.empty());

    // When & Then
    BusinessException exception = Assertions.assertThrows(
        BusinessException.class,
        () -> maintenanceTimeConfigServiceImpl.save(request)
    );

    Assertions.assertEquals(ErrorCode.MAINTENANCE_TIME_CONFIG_NOT_FOUND.getCode(), exception.getCode());
  }

  private ApplicationEntity createApplicationEntity(String id, String serviceId) {
    ApplicationEntity application = new ApplicationEntity();
    application.setId(id);
    application.setServiceId(serviceId);

    return application;
  }

  @Test
  void findByIdWithDetail_NotFound() {
    when(maintenanceTimeConfigRepository.findById(anyLong())).thenReturn(Optional.empty());

    BusinessException exception =
        assertThrows(BusinessException.class, () -> maintenanceTimeConfigServiceImpl.findByIdWithDetail(1L));
    assertThat(exception.getCode()).isEqualTo(ErrorCode.MAINTENANCE_TIME_CONFIG_NOT_FOUND.getCode());
  }

  @Test
  void findByIdWithDetail_Success() throws BusinessException {
    MaintenanceTimeConfigEntity entity = new MaintenanceTimeConfigEntity();
    when(maintenanceTimeConfigRepository.findById(anyLong())).thenReturn(Optional.of(entity));
    when(maintenanceTimeConfigDependencyService.findAllByMaintenanceTimeConfigId(anyLong()))
        .thenReturn(new ArrayList<MaintenanceTimeConfigDependencyEntity>());

    var result = maintenanceTimeConfigServiceImpl.findByIdWithDetail(1L);

    assertThat(result).isNotNull();
    verify(maintenanceTimeConfigRepository, times(1)).findById(anyLong());
  }

  @Test
  void shouldThrowExceptionWhenConfigNotFound() {
    // Given
    Long id = 1L;
    Mockito.when(maintenanceTimeConfigRepository.findById(id)).thenReturn(Optional.empty());

    // When & Then
    BusinessException exception = Assertions.assertThrows(
        BusinessException.class,
        () -> maintenanceTimeConfigServiceImpl.findByIdWithDetail(id)
    );

    Assertions.assertEquals(ErrorCode.MAINTENANCE_TIME_CONFIG_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void shouldReturnEmptyDependencies() throws BusinessException {
    // Given
    Long id = 1L;
    MaintenanceTimeConfigEntity configEntity = new MaintenanceTimeConfigEntity();
    Mockito.when(maintenanceTimeConfigRepository.findById(id)).thenReturn(Optional.of(configEntity));
    Mockito.when(maintenanceTimeConfigDependencyService.findAllByMaintenanceTimeConfigId(id))
        .thenReturn(Collections.emptyList());

    // When
    maintenanceTimeConfigServiceImpl.findByIdWithDetail(id);

    // Then
    Mockito.verify(serviceService, never()).findAllByIdIn(Mockito.anyList());
    Mockito.verify(applicationService, never()).findAllByIdIn(Mockito.anyList());
  }

  @Test
  void shouldHandleValidDependencies() throws BusinessException {
    // Given
    Long id = 1L;
    MaintenanceTimeConfigEntity configEntity = new MaintenanceTimeConfigEntity();
    List<MaintenanceTimeConfigDependencyEntity> dependencies = List.of(
        createDependencyEntity(MaintenanceTimeConfigDependencyTypeEnum.SERVICE, "service1"),
        createDependencyEntity(MaintenanceTimeConfigDependencyTypeEnum.APPLICATION, "app1"),
        createDependencyEntity(MaintenanceTimeConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION, "service2")

    );
    List<ServiceEntity> services = List.of(createServiceEntity());

    List<ApplicationResponse> applications = List.of(createApplicationResponse());

    Mockito.when(maintenanceTimeConfigRepository.findById(id)).thenReturn(Optional.of(configEntity));
    Mockito.when(maintenanceTimeConfigDependencyService.findAllByMaintenanceTimeConfigId(id)).thenReturn(dependencies);
    Mockito.when(serviceService.findAllByIdIn(List.of("service1", "service2"))).thenReturn(services);
    Mockito.when(applicationService.findAllByIdIn(List.of("app1"))).thenReturn(applications);

    // When
    maintenanceTimeConfigServiceImpl.findByIdWithDetail(id);

    // Then
    Mockito.verify(serviceService).findAllByIdIn(List.of("service1", "service2"));
    Mockito.verify(applicationService).findAllByIdIn(List.of("app1"));

  }

  private MaintenanceTimeConfigDependencyEntity createDependencyEntity(MaintenanceTimeConfigDependencyTypeEnum type,
                                                                       String dependencyId) {
    MaintenanceTimeConfigDependencyEntity dependency = new MaintenanceTimeConfigDependencyEntity();
    dependency.setType(type);
    dependency.setDependencyId(dependencyId);
    return dependency;
  }

  private ServiceEntity createServiceEntity() {
    ServiceEntity service = new ServiceEntity();
    service.setId(String.valueOf(1L));
    service.setName("service1");
    return service;
  }

  private ApplicationResponse createApplicationResponse() {
    ApplicationResponse application = new ApplicationResponse();
    application.setId("app1");
    application.setName("App Name");
    return application;
  }

  @Test
  void updateActive_NotFound() {
    when(maintenanceTimeConfigRepository.findById(anyLong())).thenReturn(Optional.empty());
    BusinessException exception =
        assertThrows(BusinessException.class, () -> maintenanceTimeConfigServiceImpl.updateActive(1L, true));
    assertThat(exception.getCode()).isEqualTo(ErrorCode.MAINTENANCE_TIME_CONFIG_NOT_FOUND.getCode());
  }

  @Test
  void updateActive_Success() throws BusinessException {
    MaintenanceTimeConfigEntity entity = new MaintenanceTimeConfigEntity();
    when(maintenanceTimeConfigRepository.findById(anyLong())).thenReturn(Optional.of(entity));
    when(maintenanceTimeConfigRepository.save(any(MaintenanceTimeConfigEntity.class))).thenReturn(entity);

    MaintenanceTimeConfigEntity result = maintenanceTimeConfigServiceImpl.updateActive(1L, true);

    assertThat(result.getActive()).isTrue();
    verify(maintenanceTimeConfigRepository, times(1)).save(entity);
  }

  @Test
  void updateActive_Success_minute() throws BusinessException {
    MaintenanceTimeConfigEntity entity = new MaintenanceTimeConfigEntity();
    entity.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    entity.setNextTime(100);

    when(maintenanceTimeConfigRepository.findById(anyLong())).thenReturn(Optional.of(entity));
    when(maintenanceTimeConfigRepository.save(any(MaintenanceTimeConfigEntity.class))).thenReturn(entity);

    entity.setUnit(MaintenanceTimeUnitEnum.MINUTE);
    MaintenanceTimeConfigEntity result = maintenanceTimeConfigServiceImpl.updateActive(1L, true);
    assertThat(result.getActive()).isTrue();
    verify(maintenanceTimeConfigRepository, times(1)).save(entity);

    entity.setUnit(MaintenanceTimeUnitEnum.SECOND);
    result = maintenanceTimeConfigServiceImpl.updateActive(1L, true);
    assertThat(result.getActive()).isTrue();

    entity.setUnit(MaintenanceTimeUnitEnum.HOUR);
    result = maintenanceTimeConfigServiceImpl.updateActive(1L, true);
    assertThat(result.getActive()).isTrue();

    entity.setUnit(MaintenanceTimeUnitEnum.DAY);
    result = maintenanceTimeConfigServiceImpl.updateActive(1L, true);
    assertThat(result.getActive()).isTrue();

    entity.setUnit(MaintenanceTimeUnitEnum.WEEK);
    result = maintenanceTimeConfigServiceImpl.updateActive(1L, true);
    assertThat(result.getActive()).isTrue();

    entity.setUnit(MaintenanceTimeUnitEnum.MONTH);
    result = maintenanceTimeConfigServiceImpl.updateActive(1L, true);
    assertThat(result.getActive()).isTrue();

    entity.setUnit(MaintenanceTimeUnitEnum.YEAR);
    result = maintenanceTimeConfigServiceImpl.updateActive(1L, true);
    assertThat(result.getActive()).isTrue();

    entity.setUnit(MaintenanceTimeUnitEnum.UNKNOWN);
    result = maintenanceTimeConfigServiceImpl.updateActive(1L, true);
    assertThat(result.getActive()).isTrue();
  }

  @Test
  void updateDeactivate_NextTime_Success() throws BusinessException {
    MaintenanceTimeConfigEntity entity = new MaintenanceTimeConfigEntity();
    entity.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    when(maintenanceTimeConfigRepository.findById(anyLong())).thenReturn(Optional.of(entity));
    when(maintenanceTimeConfigRepository.save(any(MaintenanceTimeConfigEntity.class))).thenReturn(entity);
    MaintenanceTimeConfigEntity result = maintenanceTimeConfigServiceImpl.updateActive(1L, false);
    assertThat(result.getActive()).isFalse();
    verify(maintenanceTimeConfigRepository, times(1)).save(entity);
  }

  @Test
  void findAllByActive() {
    List<MaintenanceTimeConfigEntity> mockList = new ArrayList<>();
    when(maintenanceTimeConfigRepository.findAllByActive(true)).thenReturn(mockList);

    List<MaintenanceTimeConfigEntity> result = maintenanceTimeConfigServiceImpl.findAllByActive(true);

    assertThat(result).isEqualTo(mockList);
    verify(maintenanceTimeConfigRepository, times(1)).findAllByActive(true);
  }


  @Test
  void shouldThrowExceptionWhenNameAlreadyExistsInUpdateMode() {
    // Arrange
    var request = new MaintenanceTimeConfigRequest();
    request.setId(1L);
    request.setName("TestName");
    request.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);

    when(maintenanceTimeConfigRepository.existsByIdNotAndNameIgnoreCase(1L, "TestName")).thenReturn(true);

    // Act & Assert
    var exception = assertThrows(BusinessException.class, () ->
        maintenanceTimeConfigServiceImpl.validateMaintenanceTimeRequest(request)
    );

    assertEquals(ErrorCode.MAINTENANCE_TIME_CONFIG_NAME_IS_EXISTED.getCode(), exception.getCode());
  }

  @Test
  void shouldThrowExceptionWhenNextTimeIsNull() {
    // Arrange
    var request = new MaintenanceTimeConfigRequest();
    request.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    request.setUnit(MaintenanceTimeUnitEnum.HOUR);
    request.setName("");
    // Act & Assert
    var exception = assertThrows(BusinessException.class, () ->
        maintenanceTimeConfigServiceImpl.validateMaintenanceTimeRequest(request)
    );

    assertEquals(ErrorCode.MAINTENANCE_TIME_CONFIG_TYPE_TIME_INFO_CAN_NOT_EMPTY.getCode(), exception.getCode());
  }

  @Test
  void shouldThrowExceptionWhenUnitIsNull() {
    // Arrange
    var request = new MaintenanceTimeConfigRequest();
    request.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    request.setNextTime(99);
    request.setName("");
    // Act & Assert
    var exception = assertThrows(BusinessException.class, () ->
        maintenanceTimeConfigServiceImpl.validateMaintenanceTimeRequest(request)
    );

    assertEquals(ErrorCode.MAINTENANCE_TIME_CONFIG_TYPE_TIME_INFO_CAN_NOT_EMPTY.getCode(), exception.getCode());
  }


  @Test
  void shouldThrowExceptionWhenNextTimeAndUnitIsNull() {
    // Arrange
    var request = new MaintenanceTimeConfigRequest();
    request.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    request.setName("");
    // Act & Assert
    var exception = assertThrows(BusinessException.class, () ->
        maintenanceTimeConfigServiceImpl.validateMaintenanceTimeRequest(request)
    );

    assertEquals(ErrorCode.MAINTENANCE_TIME_CONFIG_TYPE_TIME_INFO_CAN_NOT_EMPTY.getCode(), exception.getCode());
  }

  @Test
  void shouldThrowExceptionWhenStartTimeAfterEndTime() {
    // Arrange
    var request = new MaintenanceTimeConfigRequest();
    request.setType(MaintenanceTimeConfigTypeEnum.FROM_TIME_TO_TIME);
    request.setStartTime(new Date(System.currentTimeMillis() + 10000)); // 10 seconds ahead
    request.setEndTime(new Date(System.currentTimeMillis())); // Now
    request.setName("");
    // Act & Assert
    var exception = assertThrows(BusinessException.class, () ->
        maintenanceTimeConfigServiceImpl.validateMaintenanceTimeRequest(request)
    );

    assertEquals(ErrorCode.MAINTENANCE_TIME_CONFIG_TYPE_TIME_INFO_CAN_NOT_EMPTY.getCode(), exception.getCode());
  }

  @Test
  void shouldThrowExceptionWhenStartTimeNull() {
    // Arrange
    var request = new MaintenanceTimeConfigRequest();
    request.setType(MaintenanceTimeConfigTypeEnum.FROM_TIME_TO_TIME);
    request.setEndTime(new Date(System.currentTimeMillis())); // Now
    request.setName("");
    // Act & Assert
    var exception = assertThrows(BusinessException.class, () ->
        maintenanceTimeConfigServiceImpl.validateMaintenanceTimeRequest(request)
    );

    assertEquals(ErrorCode.MAINTENANCE_TIME_CONFIG_TYPE_TIME_INFO_CAN_NOT_EMPTY.getCode(), exception.getCode());
  }


  @Test
  void shouldThrowExceptionWhenEndTimeNull() {
    // Arrange
    var request = new MaintenanceTimeConfigRequest();
    request.setName("");
    request.setType(MaintenanceTimeConfigTypeEnum.FROM_TIME_TO_TIME);
    request.setStartTime(new Date(System.currentTimeMillis() + 10000)); // 10 seconds ahead

    // Act & Assert
    var exception = assertThrows(BusinessException.class, () ->
        maintenanceTimeConfigServiceImpl.validateMaintenanceTimeRequest(request)
    );

    assertEquals(ErrorCode.MAINTENANCE_TIME_CONFIG_TYPE_TIME_INFO_CAN_NOT_EMPTY.getCode(), exception.getCode());
  }


  @Test
  void shouldThrowExceptionWhenStartTimeAfterEndNull() {
    // Arrange
    var request = new MaintenanceTimeConfigRequest();
    request.setType(MaintenanceTimeConfigTypeEnum.FROM_TIME_TO_TIME);
    request.setName("");
    // Act & Assert
    var exception = assertThrows(BusinessException.class, () ->
        maintenanceTimeConfigServiceImpl.validateMaintenanceTimeRequest(request)
    );

    assertEquals(ErrorCode.MAINTENANCE_TIME_CONFIG_TYPE_TIME_INFO_CAN_NOT_EMPTY.getCode(), exception.getCode());
  }

  @Test
  void shouldThrowExceptionWhenCronExpressionIsNullForCronJobType() {
    // Arrange
    var request = new MaintenanceTimeConfigRequest();
    request.setType(MaintenanceTimeConfigTypeEnum.CRON_JOB);
    request.setName("");
    // Act & Assert
    var exception = assertThrows(BusinessException.class, () ->
        maintenanceTimeConfigServiceImpl.validateMaintenanceTimeRequest(request)
    );

    assertEquals(ErrorCode.MAINTENANCE_TIME_CONFIG_TYPE_TIME_INFO_CAN_NOT_EMPTY.getCode(), exception.getCode());
  }

  @Test
  void shouldPassValidationForValidRequest() {
    // Arrange
    var request = new MaintenanceTimeConfigRequest();
    request.setName("ValidName");
    request.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    request.setNextTime(100);
    request.setUnit(MaintenanceTimeUnitEnum.HOUR);

    when(maintenanceTimeConfigRepository.existsByNameIgnoreCase("ValidName")).thenReturn(false);

    // Act & Assert
    assertDoesNotThrow(() -> maintenanceTimeConfigServiceImpl.validateMaintenanceTimeRequest(request));
  }

  @Test
  void shouldPassValidationForValidRequest_CronJob() {
    // Arrange
    var request = new MaintenanceTimeConfigRequest();
    request.setName("ValidName");
    request.setType(MaintenanceTimeConfigTypeEnum.CRON_JOB);
    request.setCronExpression("* * * * *");
    when(maintenanceTimeConfigRepository.existsByNameIgnoreCase("ValidName")).thenReturn(false);

    // Act & Assert
    assertDoesNotThrow(() -> maintenanceTimeConfigServiceImpl.validateMaintenanceTimeRequest(request));
  }

  @Test
  void shouldThrowExceptionWhenNameAlreadyExistsInCreateMode() {
    // Arrange
    var request = new MaintenanceTimeConfigRequest();
    request.setName("TestName");
    request.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);

    when(maintenanceTimeConfigRepository.existsByNameIgnoreCase("TestName")).thenReturn(true);

    // Act & Assert
    var exception = assertThrows(BusinessException.class, () ->
        maintenanceTimeConfigServiceImpl.validateMaintenanceTimeRequest(request)
    );

    assertEquals(ErrorCode.MAINTENANCE_TIME_CONFIG_NAME_IS_EXISTED.getCode(), exception.getCode());
  }

  @Test
  void shouldThrowExceptionWhenRequestIsNull() {
    // Act & Assert
    assertThrows(NullPointerException.class, () ->
        maintenanceTimeConfigServiceImpl.validateMaintenanceTimeRequest(null)
    );
  }

  @Test
  void shouldPassValidationWhenStartTimeEqualsEndTime() {
    // Arrange
    var request = new MaintenanceTimeConfigRequest();
    request.setName("");
    request.setType(MaintenanceTimeConfigTypeEnum.FROM_TIME_TO_TIME);
    var now = new Date();
    request.setStartTime(now);
    request.setEndTime(now);

    // Act & Assert
    assertDoesNotThrow(() -> maintenanceTimeConfigServiceImpl.validateMaintenanceTimeRequest(request));
  }

  @Test
  void deleteConfigById_shouldThrowBusinessException_whenConfigIsInactive() {
    // Arrange
    Long id = 1L;
    MaintenanceTimeConfigEntity inactiveConfig = new MaintenanceTimeConfigEntity();
    inactiveConfig.setActive(true);
    when(maintenanceTimeConfigRepository.findById(id)).thenReturn(Optional.of(inactiveConfig));

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      maintenanceTimeConfigServiceImpl.deleteConfigById(id);
    });

    assertEquals(ErrorCode.MAINTENANCE_TIME_CONFIG_CAN_NOT_BE_DELETED.getCode(), exception.getCode());
    verify(maintenanceTimeConfigRepository, never()).deleteById(id);
  }

  @Test
  void deleteConfigById_shouldDeleteConfig_whenConfigIsActive() throws BusinessException {
    // Arrange
    Long id = 1L;
    MaintenanceTimeConfigEntity activeConfig = new MaintenanceTimeConfigEntity();
    activeConfig.setActive(false);
    when(maintenanceTimeConfigRepository.findById(id)).thenReturn(Optional.of(activeConfig));

    // Act
    maintenanceTimeConfigServiceImpl.deleteConfigById(id);

    // Assert
    verify(maintenanceTimeConfigRepository).deleteById(id);
  }

  @Test
  void deleteConfigById_shouldDoNothing_whenConfigDoesNotExist() throws BusinessException {
    // Arrange
    Long id = 1L;
    when(maintenanceTimeConfigRepository.findById(id)).thenReturn(Optional.empty());
    // Act
    assertThrows(BusinessException.class, () -> maintenanceTimeConfigServiceImpl.deleteConfigById(id));
  }

  @Test
  void findAllByPriorityConfigId_success() {
    var alertGroupConfig = new MaintenanceTimeConfigEntity();
    alertGroupConfig.setName("a");
    alertGroupConfig.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"priority\",\"operator\":\"IS_ONE_OF\",\"value\":[\"2001\"]}]}"));
    when(maintenanceTimeConfigRepository.findAll()).thenReturn(List.of(alertGroupConfig));
    var res = maintenanceTimeConfigServiceImpl.findAllByPriorityConfigId(2001L);
    assertEquals(1, res.size());
  }

  @Test
  void findAllByPriorityConfigId_success_ruleNull() {
    var alertGroupConfig = new MaintenanceTimeConfigEntity();
    alertGroupConfig.setName("a");
    when(maintenanceTimeConfigRepository.findAll()).thenReturn(List.of(alertGroupConfig));
    var res = maintenanceTimeConfigServiceImpl.findAllByPriorityConfigId(2001L);
    assertEquals(0, res.size());
  }

  @Test
  void findAllByCustomObjectId_success2() {
    var alertGroupConfig = new MaintenanceTimeConfigEntity();
    alertGroupConfig.setName("a");
    var alertGroupConfig2 = new MaintenanceTimeConfigEntity();
    alertGroupConfig2.setName("b");
    alertGroupConfig2.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"102\",\"operator\":\"IS\",\"value\":\"a\"}]}"));

    when(maintenanceTimeConfigRepository.findAll()).thenReturn(List.of(alertGroupConfig, alertGroupConfig2));
    var res = maintenanceTimeConfigServiceImpl.findAllByCustomObjectId(102L);
    assertEquals(1, res.size());
  }

  @Test
  void findAllByCustomObjectId_success() {
    var alertGroupConfig = new MaintenanceTimeConfigEntity();
    alertGroupConfig.setName("a");
    var alertGroupConfig2 = new MaintenanceTimeConfigEntity();
    alertGroupConfig2.setName("b");
    alertGroupConfig2.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"priority\",\"operator\":\"IS_ONE_OF\",\"value\":[\"2001\"]}]}"));
    when(maintenanceTimeConfigRepository.findAll()).thenReturn(List.of(alertGroupConfig, alertGroupConfig2));
    var res = maintenanceTimeConfigServiceImpl.findAllByPriorityConfigId(2001L);
    assertEquals(1, res.size());
  }

  @Test
  void findAllByPriorityConfigId_notfound() {
    var alertGroupConfig = new MaintenanceTimeConfigEntity();
    alertGroupConfig.setName("a");
    var alertGroupConfig2 = new MaintenanceTimeConfigEntity();
    alertGroupConfig2.setName("b");
    alertGroupConfig2.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"priority\",\"operator\":\"IS_ONE_OF\",\"value\":[\"2001\"]}]}"));
    when(maintenanceTimeConfigRepository.findAll()).thenReturn(List.of(alertGroupConfig, alertGroupConfig2));
    var res = maintenanceTimeConfigServiceImpl.findAllByPriorityConfigId(2002L);
    assertEquals(0, res.size());
  }

  @Test
  void findAllByCustomObjectId_notfound() {
    var alertGroupConfig = new MaintenanceTimeConfigEntity();
    alertGroupConfig.setName("a");
    var alertGroupConfig2 = new MaintenanceTimeConfigEntity();
    alertGroupConfig2.setName("b");
    alertGroupConfig2.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"priority\",\"operator\":\"IS_ONE_OF\",\"value\":[\"2001\"]}]}"));
    when(maintenanceTimeConfigRepository.findAll()).thenReturn(List.of(alertGroupConfig, alertGroupConfig2));
    var res = maintenanceTimeConfigServiceImpl.findAllByCustomObjectId(2002L);
    assertEquals(0, res.size());
  }

  @Test
  void findAllByCustomObjectId_ruleNull() {
    var alertGroupConfig = new MaintenanceTimeConfigEntity();
    alertGroupConfig.setName("a");
    var alertGroupConfig2 = new MaintenanceTimeConfigEntity();
    alertGroupConfig2.setName("b");
    when(maintenanceTimeConfigRepository.findAll()).thenReturn(List.of(alertGroupConfig, alertGroupConfig2));
    var res = maintenanceTimeConfigServiceImpl.findAllByCustomObjectId(102L);
    assertEquals(0, res.size());
  }

  @Test
  void testFindAllByCustomObjectId_emptyRepository() {
    when(maintenanceTimeConfigRepository.findAll()).thenReturn(Collections.emptyList());

    // Test the method
    List<String> result = maintenanceTimeConfigServiceImpl.findAllByCustomObjectId(1L);

    // Verify results
    assertNotNull(result);
    assertTrue(result.isEmpty());
    verify(maintenanceTimeConfigRepository, times(1)).findAll();
  }

  @Test
  void findAllWithSearch_shouldReturnPagedResults2() {
    MaintenanceTimePaginationRequest request = new MaintenanceTimePaginationRequest();
    request.setPage(0);
    request.setSize(2);
    request.setSearch("Config");
    request.setSortBy("name");
    request.setSortOrder(SortType.ASC);

    MaintenanceTimeConfigEntity entity1 = new MaintenanceTimeConfigEntity();
    entity1.setId(1L);
    entity1.setName("Config A");

    MaintenanceTimeConfigEntity entity2 = new MaintenanceTimeConfigEntity();
    entity2.setId(2L);
    entity2.setName("Config B");

    MaintenanceTimeConfigEntity entity3 = new MaintenanceTimeConfigEntity();
    entity3.setId(3L);
    entity3.setName("Other Config");

    List<MaintenanceTimeConfigEntity> entities = List.of(entity1, entity2, entity3);

    Mockito.when(maintenanceTimeConfigRepository.findAllByOrderByCreatedDateDesc()).thenReturn(entities);

    List<MaintenanceTimeConfigResponse> result = maintenanceTimeConfigServiceImpl.findAllMaintenance();

    Assertions.assertNotNull(result);

    Mockito.verify(maintenanceTimeConfigRepository, Mockito.times(1)).findAllByOrderByCreatedDateDesc();
  }
}
