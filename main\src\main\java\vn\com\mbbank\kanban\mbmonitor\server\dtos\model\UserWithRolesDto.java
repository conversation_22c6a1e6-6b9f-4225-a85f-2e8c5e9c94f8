package vn.com.mbbank.kanban.mbmonitor.server.dtos.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.server.constants.KanbanRegexContants;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/3/2024
 */
@Getter
@Setter
public class UserWithRolesDto {
  private Long id;

  @Pattern(regexp = KanbanRegexContants.USER_NAME, message = "User name is invalid")
  @Size(max = CommonConstants.USER_NAME_MAX_LENGTH, message = "User name exceeds "
          + CommonConstants.USER_NAME_MAX_LENGTH + " characters")
  @NotNull
  private String userName;
  private List<Long> roleIds;

  @Size(max = CommonConstants.PASSWORD_MAX_LENGTH, message = "Password exceeds "
          + CommonConstants.PASSWORD_MAX_LENGTH + " characters")
  private String password;

  private Boolean isUserLocal;


}
