package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertPriorityConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.PriorityConfigUpdatePositionRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AlertPriorityConfigDependenciesResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AlertPriorityConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;

/**
 * Controller logic PriorityAlertConfig.
 */
@RestController
@RequestMapping(ServerUrl.ALERT_PRIORITY_CONFIG_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PriorityAlertConfigController extends BaseController {

  AlertPriorityConfigService alertPriorityConfigService;
  AlertPriorityConfigDependencyService alertPriorityConfigDependencyService;

  /**
   * Api find priority config by ID.
   *
   * @param id           id of priority config.
   * @return AlertPriorityConfigResponse.
   * @throws BusinessException exception
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.PRIORITY_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping(value = "/{id}")
  ResponseData<AlertPriorityConfigResponse> findById(@PathVariable Long id)
      throws BusinessException {
    return ResponseUtils.success(alertPriorityConfigService.findPriorityConfigById(id));
  }

  /**
   * Api find a list of priority config.
   *
   * @param withDeleted  option to fetch with rawValues.
   * @param search       search
   * @return List of AlertPriorityConfigResponse
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.PRIORITY_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.WEBHOOK_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.EMAIL_COLLECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_COLLECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_GROUP_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.REPORT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.FILTER_ALERT_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MODIFY_ALERT_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.AUTO_TRIGGER_ACTION_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MONITOR_WEB_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_REQUEST, action = PermissionActionEnum.APPROVE),
      @AclPermission(module = PermissionModuleEnum.ALERT_REQUEST, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  ResponseData<List<AlertPriorityConfigResponse>> findAll(
      @RequestParam(value = "withDeleted", required = false, defaultValue = "false")
      boolean withDeleted,
      @RequestParam(value = "search", required = false, defaultValue = "")
      String search) {
    return ResponseUtils.success(
        alertPriorityConfigService.findAllByWithDeletedAndSearch(withDeleted, search));
  }

  /**
   * find all dependencies by priorityId.
   *
   * @param id id of priority
   * @return list dependencies
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.PRIORITY_CONFIG, action = PermissionActionEnum.EDIT),
      @AclPermission(module = PermissionModuleEnum.PRIORITY_CONFIG, action = PermissionActionEnum.DELETE),
  })
  @Operation(summary = "Find all dependencies by priorityId")
  @GetMapping("{id}/dependencies")
  public ResponseData<AlertPriorityConfigDependenciesResponse> findAllDependenciesById(
      @PathVariable Long id)
      throws Exception {
    return ResponseUtils.success(alertPriorityConfigDependencyService.findAllDependenciesById(id));
  }

  /**
   * Api save priority config.
   *
   * @param alertPriorityConfigRequest input data.
   * @return AlertPriorityConfigResponse.
   * @throws BusinessException exception
   */
  @PostMapping
  ResponseData<AlertPriorityConfigResponse> save(
      @Valid @RequestBody AlertPriorityConfigRequest alertPriorityConfigRequest)
      throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.PRIORITY_CONFIG, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(alertPriorityConfigRequest.getId()));
    return ResponseUtils.success(alertPriorityConfigService.save(alertPriorityConfigRequest));
  }

  /**
   * Api delete priority config.
   *
   * @param id priority config id.
   * @return String
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.PRIORITY_CONFIG, action = PermissionActionEnum.DELETE),
  })
  @DeleteMapping(value = "/{id}")
  ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    alertPriorityConfigDependencyService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }

  /**
   * Api delete priority config.
   *
   * @param request alert priority config update info.
   * @return list of alert priority config
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.PRIORITY_CONFIG, action = PermissionActionEnum.EDIT)
  })
  @PutMapping("/position")
  public ResponseData<List<AlertPriorityConfigResponse>> updatePosition(
      @RequestBody @Valid PriorityConfigUpdatePositionRequest request) throws BusinessException {
    return ResponseUtils.success(alertPriorityConfigService.updatePosition(request));
  }
  
  /**
   * find alert priority configs by alert status.
   *
   * @param alertGroupStatus status of alertGroup
   * @return list of alert priority config
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MONITOR_ALERT, action = PermissionActionEnum.VIEW)
  })
  @GetMapping(params = {"alertGroupStatus"})
  public ResponseData<List<AlertPriorityConfigResponse>> findAllByAlertStatus(
      @RequestParam AlertGroupStatusEnum alertGroupStatus) {
    return ResponseUtils.success(
        alertPriorityConfigService.findAllAlertPriorityConfigByAlertStatus(alertGroupStatus));
  }
}
