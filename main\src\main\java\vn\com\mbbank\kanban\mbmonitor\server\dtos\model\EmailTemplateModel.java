package vn.com.mbbank.kanban.mbmonitor.server.dtos.model;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * EmailPartnerModel.
 */
@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailTemplateModel {
  Long id;
  String name;
  private String description;
  private String content;
  private String subject;
  List<FileStorageModel> fileStorages;
  List<String> to;
  List<String> cc;
}
