package vn.com.mbbank.kanban.mbmonitor.common.converter;

import com.alibaba.fastjson2.JSON;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowNodePositionModel;

/**
 * WorkflowTemplateNodePositionConverter.
 */
@Converter
public class WorkflowNodePositionConverter implements AttributeConverter<WorkflowNodePositionModel, String> {

  @Override
  public String convertToDatabaseColumn(WorkflowNodePositionModel attribute) {
    if (attribute == null) {
      return null;
    }
    return JSON.toJSONString(attribute);
  }

  @Override
  public WorkflowNodePositionModel convertToEntityAttribute(String dbData) {
    try {
      return JSON.parseObject(dbData, WorkflowNodePositionModel.class);
    } catch (Exception e) {
      return null;
    }
  }
}
