package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.PosixFilePermissions;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.MockedConstruction;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.util.ReflectionTestUtils;

class BrowserSyncServiceImplTest {
  
  @TempDir
  Path tempDir;
  
  private BrowserSyncServiceImpl browserSyncService;
  
  @BeforeEach
  void setUp() throws Exception {
    browserSyncService = new BrowserSyncServiceImpl();
    setField("expectPath", tempDir.toString());
    setField("chromiumZipResource", "chromium.zip");
    setField("firefoxZipResource", "firefox.zip");
    setField("driverResource", "driver.zip");
    setField("enableFirefox", true);
  }
  
  @Test
  void testCheckAndSyncBrowsers_forceTrue_shouldExtract() throws Exception {
    try (MockedConstruction<ClassPathResource> mocked = mockResourceConstructor()) {
      String result = browserSyncService.checkAndSyncBrowsers(true);
      assertTrue(result.contains("Copied & extracted chromium.zip"));
      assertTrue(result.contains("Copied & extracted firefox.zip"));
      assertTrue(result.contains("Copied & extracted driver.zip"));
      
      assertTrue(Files.exists(tempDir.resolve("chromium-1178").resolve("chromium.zip")));
      assertTrue(Files.exists(tempDir.resolve("firefox-1487").resolve("firefox.zip")));
      assertTrue(Files.exists(tempDir.resolve("driver.zip")));
    }
  }
  
  @Test
  void testSyncZipAndExtract_whenZipExistsAndPosixSupported_fileDirectoryNotExists() throws Exception {
    String zipFileName = "test-chromium.zip";
    String subFolder = "chromium-1178";
    
    Path zipTargetDir = tempDir.resolve(subFolder);
    Path zipTargetPath = zipTargetDir.resolve(zipFileName);
    Files.createDirectories(zipTargetDir);
    Files.createFile(zipTargetPath);
    
    BrowserSyncServiceImpl spyService = Mockito.spy(browserSyncService);
    setField("expectPath", tempDir.toString());
    setField("chromiumZipResource", zipFileName);
    setField("firefoxZipResource", "dummy.zip");
    setField("driverResource", "dummy-driver.zip");
    setField("enableFirefox", false);
    
    Mockito.doReturn(true).when(spyService).isPosixSupported(Mockito.any());
    
    try (MockedStatic<Files> filesMock = Mockito.mockStatic(Files.class, Mockito.CALLS_REAL_METHODS)) {
      filesMock.when(() -> Files.exists(zipTargetDir)).thenReturn(true);
      filesMock.when(() -> Files.exists(zipTargetPath)).thenReturn(true);
      filesMock.when(() -> Files.getPosixFilePermissions(Mockito.any(Path.class)))
        .thenReturn(PosixFilePermissions.fromString("rwxr-xr-x"));
      filesMock.when(() -> Files.setPosixFilePermissions(Mockito.any(Path.class), Mockito.any()))
        .then(inv -> null);
      
      String result = spyService.checkAndSyncBrowsers(false);
      assertTrue(result.contains("Copied & extracted"));
    }
  }
  
  @Test
  void testSyncZipAndExtract_whenZipExistsAndPosixSupported_fileDirectoryExists() throws Exception {
    String zipFileName = "test-chromium.zip";
    String subFolder = "chromium-1178";
    
    Path zipTargetDir = tempDir.resolve(subFolder);
    Path zipTargetPath = zipTargetDir.resolve(zipFileName);
    Files.createDirectories(zipTargetDir);
    Files.createFile(zipTargetPath);
    
    BrowserSyncServiceImpl spyService = Mockito.spy(browserSyncService);
    ReflectionTestUtils.setField(spyService, "expectPath", tempDir.toString());
    ReflectionTestUtils.setField(spyService, "chromiumZipResource", zipFileName);
    ReflectionTestUtils.setField(spyService, "firefoxZipResource", "dummy.zip");
    ReflectionTestUtils.setField(spyService, "driverResource", "dummy-driver.zip");
    ReflectionTestUtils.setField(spyService, "enableFirefox", false);
    
    Mockito.doReturn(true).when(spyService).isPosixSupported(Mockito.any());
    
    try (MockedStatic<Files> filesMock = Mockito.mockStatic(Files.class, Mockito.CALLS_REAL_METHODS)) {
      filesMock.when(() -> Files.getPosixFilePermissions(zipTargetDir))
        .thenReturn(PosixFilePermissions.fromString("rwxr-xr-x"));
      filesMock.when(() -> Files.setPosixFilePermissions(zipTargetPath, PosixFilePermissions.fromString("rwxr-xr-x")))
        .thenAnswer(inv -> null);
      
      String result = spyService.checkAndSyncBrowsers(false);
      assertTrue(result.contains("Already extracted to"));
    }
  }
  
  private MockedConstruction<ClassPathResource> mockResourceConstructor() throws IOException {
    byte[] zipData = createFakeZip();
    return Mockito.mockConstruction(ClassPathResource.class, (mock, context) -> {
      when(mock.exists()).thenReturn(true);
      when(mock.getInputStream()).thenReturn(new ByteArrayInputStream(zipData));
    });
  }
  
  private byte[] createFakeZip() throws IOException {
    ByteArrayOutputStream out = new ByteArrayOutputStream();
    try (ZipOutputStream zos = new ZipOutputStream(out)) {
      zos.putNextEntry(new ZipEntry("dummy.txt"));
      zos.write("dummy content".getBytes());
      zos.closeEntry();
    }
    return out.toByteArray();
  }
  
  private void setField(String fieldName, Object value) throws Exception {
    Field field = BrowserSyncServiceImpl.class.getDeclaredField(fieldName);
    field.setAccessible(true);
    field.set(browserSyncService, value);
  }
  
  @AfterEach
  void cleanup() throws IOException {
    FileUtils.deleteDirectory(tempDir.toFile());
  }
}
