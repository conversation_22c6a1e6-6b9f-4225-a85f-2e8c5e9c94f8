package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomInputEntity;

/**
 * Repository CustomInputEntity.
 */
@Repository
public interface CustomInputRepository
    extends JpaCommonRepository<CustomInputEntity, String> {

  /**
   * check existed by id, name and deleted status.
   *
   * @param id   id
   * @param name name
   * @return existed or not
   */
  boolean existsByIdNotAndNameIgnoreCase(String id, String name);

  /**
   * check existed by name and deleted status.
   *
   * @param name name
   * @return existed or not
   */
  boolean existsByNameIgnoreCase(String name);

}
