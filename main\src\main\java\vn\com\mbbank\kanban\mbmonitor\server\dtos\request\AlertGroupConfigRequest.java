package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupOutputEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;

/**
 * AlertGroupConfigRequest.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlertGroupConfigRequest {
  Long id;

  @Size(min = 1, message = "Group Config name can not be empty")
  @Size(
      max = CommonConstants.ALERT_GROUP_CONFIG_NAME_MAX_LENGTH,
      message = "Group Config name has max {max} character"
  )
  String name;

  @Size(
      max = CommonConstants.ALERT_GROUP_CONFIG_DESCRIPTION_MAX_LENGTH,
      message = "Group Config name has max {max} character"
  )
  String description;

  @NotNull
  AlertGroupOutputEnum alertOutput;

  @NotNull
  AlertGroupConfigTypeEnum type;

  @Builder.Default
  List<Long> customObjectIds = new ArrayList<>();

  @Builder.Default
  List<RuleGroupType> ruleGroups = new ArrayList<>();

  @NotNull
  @Size(min = 1)
  @Builder.Default
  List<String> serviceIds = new ArrayList<>();

  @NotNull
  @Builder.Default
  List<String> applicationIds = new ArrayList<>();

  private String customServiceId;

  private String customApplicationId;
  @Size(max = CommonConstants.COMMON_CONTENT_ALERT_MAX_LENGTH)
  private String customContent;
  @Size(max = CommonConstants.COMMON_CONTACT_ALERT_MAX_LENGTH)
  private String customRecipient;

  private Long customPriorityConfigId;
}
