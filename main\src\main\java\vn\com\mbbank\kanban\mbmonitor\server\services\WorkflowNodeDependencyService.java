package vn.com.mbbank.kanban.mbmonitor.server.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowNodeDependencyEntity;

/**
 * interface logic WorkflowNodeDependencyService.
 */
public interface WorkflowNodeDependencyService
    extends BaseService<WorkflowNodeDependencyEntity, String> {

  /**
   * delete node by workflowId.
   *
   * @param workflowId workflowId
   */
  void deleteAllByWorkflowId(String workflowId);

  /**
   * find all dependencies by workflowId.
   *
   * @param workflowId workflowId
   * @return list of WorkflowNodeDependencyEntity
   */
  List<WorkflowNodeDependencyEntity> findAllByWorkflowId(String workflowId);

}