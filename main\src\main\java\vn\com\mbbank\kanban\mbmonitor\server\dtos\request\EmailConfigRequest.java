package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.server.constants.KanbanRegexContants;
import vn.com.mbbank.kanban.mbmonitor.server.enums.EmailProtocolSecurityTypeEnum;

/**
 * Model request service to create or update email config.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailConfigRequest {
  Long id;
  @Size(min = 1, message = "Email Config host can not be empty")
  @Size(
      max = CommonConstants.EMAIL_CONFIG_HOST_MAX_LENGTH,
      message = "Email Config host has max {max} character"
  )
  @Pattern(
      regexp = KanbanRegexContants.EMAIL_HOST_PATTERN,
      message = "Email Config host can only contain A-Z, a-z, 0-9, . -  _"
  )
  String host;
  @Size(
      max = CommonConstants.EMAIL_CONFIG_DESCRIPTION_MAX_LENGTH,
      message = "Email Config description has max {max} character"
  )
  String description;
  @Max(value = 65535, message = "Email Config port has max {value}")
  @Min(value = 0, message = "Invalid email config port")
  int port;
  @Size(min = 1, message = "Email Config password can not be empty")
  @Size(
      max = CommonConstants.EMAIL_CONFIG_PASSWORD_MAX_LENGTH,
      message = "Email Config password has max {max} character"
  )
  String password;
  @NotNull
  EmailProtocolTypeEnum protocolType;
  @NotNull
  EmailProtocolSecurityTypeEnum securityType;
  @Size(min = 1, message = "Email Config address can not be empty")
  @Size(
      max = CommonConstants.EMAIL_CONFIG_ADDRESS_MAX_LENGTH,
      message = "Email Config address has max {max} character"
  )
  @Pattern(
      regexp = KanbanRegexContants.EMAIL_ADDRESS_PATTERN,
      message = "Email Config address can only contain A-Z, a-z, 0-9, @ . -  _"
  )
  String username;
  boolean active;
}
