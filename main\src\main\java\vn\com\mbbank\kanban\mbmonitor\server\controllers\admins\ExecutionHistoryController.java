package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionHistoryResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.ExecutionHistoryCursorModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ExecutionHistorySearchRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.ExecutionHistoryResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionHistoryService;

/**
 * ExecutionController.
 */
@RestController
@RequestMapping(ServerUrl.EXECUTION_HISTORY_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ExecutionHistoryController {

  private final ExecutionHistoryService executionHistoryService;
  private final ExecutionHistoryResponseMapper executionHistoryResponseMapper = ExecutionHistoryResponseMapper.INSTANCE;

  /**
   * find all execution history.
   *
   * @param request search request.
   * @return history response with paging.
   */
  @GetMapping
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EXECUTION_HISTORY, action = PermissionActionEnum.VIEW),
  })
  ResponseData<CursorPageResponse<ExecutionHistoryResponse, ExecutionHistoryCursorModel>> findAll(
      @ModelAttribute ExecutionHistorySearchRequest request)
      throws BusinessException {
    return ResponseUtils.success(executionHistoryService.findAll(request));
  }

  /**
   * find execution history detail.
   *
   * @param id id.
   * @return ExecutionHistoryResponse.
   */
  @GetMapping(value = "/{id}")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EXECUTION_HISTORY, action = PermissionActionEnum.VIEW),
  })
  ResponseData<ExecutionHistoryResponse> findById(@PathVariable String id) throws BusinessException {
    return ResponseUtils.success(executionHistoryResponseMapper.map(executionHistoryService.findWithId(id)));
  }
}
