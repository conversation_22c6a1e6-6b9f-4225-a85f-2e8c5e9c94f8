package vn.com.mbbank.kanban.mbmonitor.common.condition;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleElement;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

@ExtendWith({MockitoExtension.class})
public class RuleGroupTypeTest {
  private RuleGroupType ruleGroup;

  @BeforeEach
  void setup() {
    ruleGroup = new RuleGroupType();
  }

  @Test
  void check_success_AndCombinatorWithAllTrueConditions() {
    RuleElement rule1 = Mockito.mock(RuleElement.class);
    RuleElement rule2 = Mockito.mock(RuleElement.class);
    Mockito.when(rule1.check(Mockito.any(AlertEntity.class))).thenReturn(true);
    Mockito.when(rule2.check(Mockito.any(AlertEntity.class))).thenReturn(true);

    ruleGroup.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroup.setRules(List.of(rule1, rule2));

    Assertions.assertTrue(ruleGroup.check(new AlertEntity()), "All conditions should return true with AND combinator");
  }

  @Test
  void check_success_AndCombinatorWithOneFalseCondition() {
    RuleElement rule1 = Mockito.mock(RuleElement.class);
    RuleElement rule2 = Mockito.mock(RuleElement.class);
    Mockito.when(rule1.check(Mockito.any(AlertEntity.class))).thenReturn(true);
    Mockito.when(rule2.check(Mockito.any(AlertEntity.class))).thenReturn(false);

    ruleGroup.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroup.setRules(List.of(rule1, rule2));

    Assertions.assertFalse(ruleGroup.check(new AlertEntity()),
        "One false condition should return false with AND combinator");
  }

  @Test
  void check_success_OrCombinatorWithOneTrueCondition() {
    RuleElement rule1 = Mockito.mock(RuleElement.class);
    RuleElement rule2 = Mockito.mock(RuleElement.class);
    Mockito.when(rule1.check(Mockito.any(AlertEntity.class))).thenReturn(false);
    Mockito.when(rule2.check(Mockito.any(AlertEntity.class))).thenReturn(true);

    ruleGroup.setCombinator(ConditionCombinatorEnum.OR);
    ruleGroup.setRules(List.of(rule1, rule2));

    Assertions.assertTrue(ruleGroup.check(new AlertEntity()),
        "One true condition should return true with OR combinator");
  }

  @Test
  void check_success_OrCombinatorWithAllFalseConditions() {
    RuleElement rule1 = Mockito.mock(RuleElement.class);
    RuleElement rule2 = Mockito.mock(RuleElement.class);
    Mockito.when(rule1.check(Mockito.any(AlertEntity.class))).thenReturn(false);
    Mockito.when(rule2.check(Mockito.any(AlertEntity.class))).thenReturn(false);

    ruleGroup.setCombinator(ConditionCombinatorEnum.OR);
    ruleGroup.setRules(List.of(rule1, rule2));

    Assertions.assertFalse(ruleGroup.check(new AlertEntity()),
        "All false conditions should return false with OR combinator");
  }

  @Test
  void check_success_EmptyRulesList() {
    ruleGroup.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroup.setRules(List.of());

    Assertions.assertTrue(ruleGroup.check(new Object()), "Empty rules list should return true");
  }

  @Test
  void check_success_NullObjectInput() {
    RuleElement rule1 = Mockito.mock(RuleElement.class);
    Mockito.when(rule1.check(null)).thenReturn(false);

    ruleGroup.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroup.setRules(List.of(rule1));

    Assertions.assertFalse(ruleGroup.check(null), "Null object should return false when evaluated");
  }

  @Test
  void check_AndCombinatorWithAllTrueConditions2() {
    RuleElement rule1 = Mockito.mock(RuleElement.class);
    RuleElement rule2 = Mockito.mock(RuleElement.class);
    Mockito.when(rule1.check(Mockito.any(), ArgumentMatchers.any())).thenReturn(true);
    Mockito.when(rule2.check(Mockito.any(), ArgumentMatchers.any())).thenReturn(true);
    Function<Object, String> func = Mockito.mock(Function.class);
    ruleGroup.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroup.setRules(List.of(rule1, rule2));

    Assertions.assertTrue(ruleGroup.check(new Object(), func), "All conditions should return true with AND combinator");
  }

  @Test
  void check_AndCombinatorWithOneFalseCondition2() {
    RuleElement rule1 = Mockito.mock(RuleElement.class);
    RuleElement rule2 = Mockito.mock(RuleElement.class);
    Mockito.when(rule1.check(Mockito.any(), ArgumentMatchers.any())).thenReturn(true);
    Mockito.when(rule2.check(Mockito.any(), ArgumentMatchers.any())).thenReturn(false);

    ruleGroup.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroup.setRules(List.of(rule1, rule2));
    Function<Object, String> func = Mockito.mock(Function.class);
    Assertions.assertFalse(ruleGroup.check(new Object(), func),
        "One false condition should return false with AND combinator");
  }

  @Test
  void check_OrCombinatorWithOneTrueCondition2() {
    RuleElement rule1 = Mockito.mock(RuleElement.class);
    RuleElement rule2 = Mockito.mock(RuleElement.class);
    Mockito.when(rule1.check(Mockito.any(), ArgumentMatchers.any())).thenReturn(false);
    Mockito.when(rule2.check(Mockito.any(), ArgumentMatchers.any())).thenReturn(true);
    Function<Object, String> func = Mockito.mock(Function.class);
    ruleGroup.setCombinator(ConditionCombinatorEnum.OR);
    ruleGroup.setRules(List.of(rule1, rule2));

    Assertions.assertTrue(ruleGroup.check(new Object(), func),
        "One true condition should return true with OR combinator");
  }

  @Test
  void check_OrCombinatorWithAllFalseConditions2() {
    RuleElement rule1 = Mockito.mock(RuleElement.class);
    RuleElement rule2 = Mockito.mock(RuleElement.class);
    Mockito.when(rule1.check(Mockito.any(), ArgumentMatchers.any())).thenReturn(false);
    Mockito.when(rule2.check(Mockito.any(), ArgumentMatchers.any())).thenReturn(false);

    ruleGroup.setCombinator(ConditionCombinatorEnum.OR);
    ruleGroup.setRules(List.of(rule1, rule2));
    Function<Object, String> func = Mockito.mock(Function.class);
    Assertions.assertFalse(ruleGroup.check(new Object(), func),
        "All false conditions should return false with OR combinator");
  }

  @Test
  void check_EmptyRulesList2() {
    ruleGroup.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroup.setRules(List.of());
    Function<Object, String> func = Mockito.mock(Function.class);
    Assertions.assertTrue(ruleGroup.check(new Object(), func), "Empty rules list should return true");
  }

  @Test
  void check_NullObjectInput2() {
    RuleElement rule1 = Mockito.mock(RuleElement.class);
    Mockito.when(rule1.check(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(false);

    ruleGroup.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroup.setRules(List.of(rule1));
    Function<Object, String> func = Mockito.mock(Function.class);
    Assertions.assertFalse(ruleGroup.check(null, func), "Null object should return false when evaluated");
  }

  @Test
  void equal_success_caseSameObject() {
    var ruleGroupType = new RuleGroupType();
    var res = ruleGroupType.equals(ruleGroupType);
    Assertions.assertTrue(res);
  }

  @Test
  void equal_success_caseObjectNull() {
    var ruleGroupType = new RuleGroupType();
    var res = ruleGroupType.equals(null);
    Assertions.assertFalse(res);
  }

  @Test
  void equal_success_caseObjectDifferentClass() {
    var ruleGroupType = new RuleGroupType();
    var res = ruleGroupType.equals(new AlertGroupEntity());
    Assertions.assertFalse(res);
  }

  @Test
  void equal_success() {
    var ruleGroupType = new RuleGroupType();
    ruleGroup.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroup.setRules(List.of());
    var ruleGroupType1 = new RuleGroupType();
    ruleGroupType1.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroupType1.setRules(List.of());
    var res = ruleGroupType.equals(ruleGroupType1);
    Assertions.assertFalse(res);
  }

  @Test
  void check_success_caseMapNull() {
    Map<String, Object> valueMap = null;
    var ruleGroupType = new RuleGroupType();
    var res = ruleGroupType.check(valueMap);
    Assertions.assertTrue(res);
  }

  @Test
  void check_success_caseAnd() {
    Map<String, Object> valueMap = Map.of("abc", "abc");
    RuleElement rule1 = Mockito.mock(RuleElement.class);
    RuleElement rule2 = Mockito.mock(RuleElement.class);
    Mockito.when(rule1.check(Mockito.anyMap())).thenReturn(true);
    Mockito.when(rule2.check(Mockito.anyMap())).thenReturn(false);

    ruleGroup.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroup.setRules(List.of(rule1, rule2));
    var res = ruleGroup.check(valueMap);
    Assertions.assertFalse(res);
  }

  @Test
  void check_success_caseRuleEmpty() {
    Map<String, Object> valueMap = Map.of("abc", "abc");
    ruleGroup.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroup.setRules(List.of());
    var res = ruleGroup.check(valueMap);
    Assertions.assertTrue(res);
  }

  @Test
  void check_success_caseOr() {
    Map<String, Object> valueMap = Map.of("abc", "abc");
    RuleElement rule1 = Mockito.mock(RuleElement.class);
    RuleElement rule2 = Mockito.mock(RuleElement.class);
    Mockito.when(rule1.check(Mockito.anyMap())).thenReturn(true);

    ruleGroup.setCombinator(ConditionCombinatorEnum.OR);
    ruleGroup.setRules(List.of(rule1, rule2));
    var res = ruleGroup.check(valueMap);
    Assertions.assertTrue(res);
  }

  @Test
  void check_success_caseCheckMap() {
    Map<String, Object> valueMap = Map.of("abc", "abc");
    RuleElement rule1 = Mockito.mock(RuleElement.class);
    RuleElement rule2 = Mockito.mock(RuleElement.class);
    Mockito.when(rule1.check(Mockito.anyMap())).thenReturn(true);
    Mockito.when(rule2.check(Mockito.anyMap())).thenReturn(true);

    ruleGroup.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroup.setRules(List.of(rule1, rule2));

    Assertions.assertTrue(ruleGroup.check(valueMap));
  }

  @Test
  void hashCode_success() {
    var ruleGroupType = new RuleGroupType();
    var res = ruleGroupType.hashCode();
    Assertions.assertNotEquals(0, res);
  }
}
