package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExportDataService;
import java.io.IOException;

@ExtendWith({MockitoExtension.class})
public class ExportDataControllerTest {
  @InjectMocks
  ExportDataController exportDataController;
  @Mock
  ExportDataService exportDataService;

  @TestForDev
  void findAll_success() throws BusinessException {
    when(exportDataService.findAll(any(PaginationRequest.class))).thenReturn(Page.empty());
    var res = exportDataController.findAll(new PaginationRequest());
    assertEquals(0, res.getData().getContent().size());
  }
  @TestForDev
  void download_success() throws BusinessException, IOException {
    when(exportDataService.download(any())).thenReturn(null);
    exportDataController.download("1L");
    verify(exportDataService,times(1)).download(any());
  }
  @TestForDev
  void deleteById_success() throws BusinessException {
    doNothing().when(exportDataService).deleteWithId(any());
    exportDataController.deleteById("1L");
    verify(exportDataService,times(1)).deleteWithId(any());
  }
}
