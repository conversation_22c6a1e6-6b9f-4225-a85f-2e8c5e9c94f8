package vn.com.mbbank.kanban.mbmonitor.server.services;

import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.WorkflowTemplateRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.WorkflowTemplateResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowTemplateEntity;

/**
 * interface logic WorkflowTemplateService.
 */
public interface WorkflowTemplateService extends BaseService<WorkflowTemplateEntity, String> {

  /**
   * Find all.
   *
   * @param id id
   * @return page WorkflowTemplateResponse
   */
  WorkflowTemplateResponse findWithId(String id) throws BusinessException;

  /**
   * create or update.
   *
   * @param request CustomInputRequest
   * @return WorkflowTemplateEntity
   */
  WorkflowTemplateEntity createOrUpdate(WorkflowTemplateRequest request) throws BusinessException;

  /**
   * delete by id.
   *
   * @param id for delete
   */
  void deleteWithId(String id) throws BusinessException;

}