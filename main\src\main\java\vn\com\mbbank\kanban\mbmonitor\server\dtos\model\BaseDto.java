package vn.com.mbbank.kanban.mbmonitor.server.dtos.model;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Base Data Transfer Object (DTO) class containing common audit fields.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class BaseDto {
  private Date createdDate;
  private String createdBy;
  private Date modifiedDate;
  private String modifiedBy;
}
