package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.text.MessageFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.constants.NotificationConstants;
import vn.com.mbbank.kanban.mbmonitor.common.constants.ProseMirrorMessageConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.AlertRequestRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.AlertRequestResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertRequestEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysPermissionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertRequestSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertRequestStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.AlertRequestEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.AlertRequestResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.PushNotificationService;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.AlertRequestRequestToDatabaseThresholdConfigMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AlertRequestRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertRequestDatabaseService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertRequestService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.DatabaseThresholdConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;
import vn.com.mbbank.kanban.mbmonitor.server.services.SysPermissionService;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlertRequestServiceImpl extends BaseServiceImpl<AlertRequestEntity, String>
    implements AlertRequestService {
  final AlertRequestRepository alertRequestRepository;
  final ServiceService serviceService;
  final ApplicationService applicationService;
  final AlertPriorityConfigService alertPriorityConfigService;
  final AlertRequestDatabaseService alertRequestDatabaseService;
  final DatabaseThresholdConfigService databaseThresholdConfigService;
  final PushNotificationService pushNotificationService;
  final CommonAclPermissionService commonAclPermissionService;
  final SysPermissionService sysPermissionService;
  @Value("${mbmonitor.url}")
  String mbmonitorUrl;

  @Override
  public JpaCommonRepository<AlertRequestEntity, String> getRepository() {
    return alertRequestRepository;
  }

  @Override
  @Transactional(rollbackFor = BusinessException.class)
  public AlertRequestResponse createOrUpdate(AlertRequestRequest request) throws BusinessException {
    var oldAlertRequest = validateSaveRequest(request);
    var oldAlertRequestStatus =
        Optional.ofNullable(oldAlertRequest).map(AlertRequestEntity::getStatus).orElse(AlertRequestStatusEnum.NEW);
    var alertRequest = AlertRequestEntityMapper.INSTANCE.map(request);
    if (AlertRequestStatusEnum.REJECTED.equals(request.getStatus())) {
      alertRequest.setRejectedReason(oldAlertRequest.getRejectedReason());
    }
    alertRequest = alertRequestRepository.save(alertRequest);
    String requestId = alertRequest.getId();
    //logic save database threshold
    alertRequestDatabaseService.deleteByAlertRequestId(requestId);
    var dbRequest = request.getAlertRequestDatabase();
    dbRequest.setAlertRequestId(requestId);
    var dbResponse = alertRequestDatabaseService.save(dbRequest);

    AlertRequestResponse response = AlertRequestResponseMapper.INSTANCE.map(alertRequest);
    response.setAlertRequestDatabase(dbResponse);

    boolean shouldNotify = AlertRequestStatusEnum.WAITING_APPROVAL.equals(alertRequest.getStatus())
        && (AlertRequestStatusEnum.NEW.equals(oldAlertRequestStatus)
        || AlertRequestStatusEnum.REJECTED.equals(oldAlertRequestStatus));

    if (shouldNotify) {
      sendNotification(requestId, alertRequest.getStatus(), null, null);
    }

    return response;
  }

  public AlertRequestEntity validateSaveRequest(AlertRequestRequest request)
      throws BusinessException {
    var isCreate = KanbanCommonUtil.isNullOrEmpty(request.getId());
    var currentUser = getUserName();
    AlertRequestEntity oldEntity = null;
    if (AlertRequestStatusEnum.APPROVED.equals(request.getStatus())) {
      throw new BusinessException(ErrorCode.ALERT_REQUEST_INVALID_STATUS);
    }
    if (!isCreate) {
      var existingEntity = alertRequestRepository.findById(request.getId())
          .orElseThrow(() -> new BusinessException(ErrorCode.ALERT_REQUEST_NOT_FOUND));
      var status = existingEntity.getStatus();
      var isOwner = currentUser.equals(existingEntity.getCreatedBy());
      var isPermissionApprover = commonAclPermissionService.isAnyPermission(List.of(new AclPermissionModel(
          PermissionModuleEnum.ALERT_REQUEST, PermissionActionEnum.APPROVE)));
      oldEntity = existingEntity;
      if (!currentUser.equals(existingEntity.getCreatedBy())
          && !isPermissionApprover) {
        throw new BusinessException(ErrorCode.USER_DONT_HAVE_PERMISSIONS);
      }
      if (AlertRequestStatusEnum.APPROVED.equals(status)) {
        throw new BusinessException(ErrorCode.ALERT_REQUEST_HAS_BEEN_APPROVED);
      }
      // Only the owner is allowed to edit the alert request if it's in NEW or REJECTED status
      if ((AlertRequestStatusEnum.NEW.equals(status) || AlertRequestStatusEnum.REJECTED.equals(status)) && !isOwner) {
        throw new BusinessException(ErrorCode.ALERT_REQUEST_UNAUTHORIZED_ACCESS);
      }

      // User is not allowed to edit the alert request if it's currently waiting for approval
      if (AlertRequestStatusEnum.WAITING_APPROVAL.equals(status) && isOwner) {
        throw new BusinessException(ErrorCode.ALERT_REQUEST_WAITING_APPROVAL_FORBIDDEN);
      }
    }
    if (AlertRequestSourceTypeEnum.DATABASE.equals(request.getSourceType())) {
      var dbRequest = request.getAlertRequestDatabase();
      if (Objects.nonNull(dbRequest) && !KanbanCommonUtil.isNullOrEmpty(dbRequest.getName())) {
        throw new BusinessException(ErrorCode.ALERT_REQUEST_INVALID_NAME_DATABASE_CONFIG);
      }
    }

    var service = serviceService.findById(request.getServiceId());
    if (Objects.isNull(service)) {
      throw new BusinessException(ErrorCode.ALERT_REQUEST_INVALID_SERVICE);
    }
    var application = applicationService.findById(request.getApplicationId());
    if (Objects.isNull(application)) {
      throw new BusinessException(ErrorCode.ALERT_REQUEST_INVALID_APPLICATION);
    }
    var priority = alertPriorityConfigService.findById(request.getPriorityId());
    if (Objects.isNull(priority)) {
      throw new BusinessException(ErrorCode.ALERT_REQUEST_INVALID_PRIORITY);
    }
    return oldEntity;
  }

  @Override
  public AlertRequestResponse findWithId(String id) {
    var res = alertRequestRepository.findWithId(id);
    res.setAlertRequestDatabase(alertRequestDatabaseService.findByAlertRequestId(id));
    return res;
  }

  @Override
  public Page<AlertRequestResponse> findAll(PaginationRequestDTO paginationRequest) {
    return alertRequestRepository.findAll(paginationRequest);
  }


  @Override
  @Transactional(rollbackFor = BusinessException.class)
  public AlertRequestResponse approve(AlertRequestRequest request) throws BusinessException {
    var id = request.getId();
    if (KanbanCommonUtil.isNullOrEmpty(id)) {
      throw new BusinessException(ErrorCode.ALERT_REQUEST_NOT_FOUND);
    }
    var existingEntity = alertRequestRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.ALERT_REQUEST_NOT_FOUND));
    var createdBy = existingEntity.getCreatedBy();
    if (AlertRequestStatusEnum.NEW.equals(existingEntity.getStatus())
        || AlertRequestStatusEnum.APPROVED.equals(existingEntity.getStatus())) {
      throw new BusinessException(ErrorCode.ALERT_REQUEST_CAN_NOT_APPROVED, existingEntity.getStatus());
    }
    var alertRequest = AlertRequestEntityMapper.INSTANCE.map(request);
    alertRequest.setApprovedBy(getUserName());
    alertRequest.setApprovedDate(new Date());
    alertRequest.setStatus(AlertRequestStatusEnum.APPROVED);
    var response = AlertRequestResponseMapper.INSTANCE.map(alertRequestRepository.save(alertRequest));
    if (AlertRequestSourceTypeEnum.DATABASE.equals(request.getSourceType())) {
      alertRequestDatabaseService.deleteByAlertRequestId(id);
      request.getAlertRequestDatabase().setAlertRequestId(id);
      var alertRequestDatabaseResponse = alertRequestDatabaseService
          .save(request.getAlertRequestDatabase());
      response.setAlertRequestDatabase(alertRequestDatabaseResponse);
      createDatabaseThresholdConfig(request);
      sendNotification(id, alertRequest.getStatus(), createdBy, null);
      return response;
    }
    return null;
  }

  private void createDatabaseThresholdConfig(AlertRequestRequest alertRequestRequest) throws BusinessException {
    var request = AlertRequestRequestToDatabaseThresholdConfigMapper.INSTANCE
        .map(alertRequestRequest);
    var alertRequestDatabase = alertRequestRequest.getAlertRequestDatabase();
    request.setName(alertRequestDatabase.getName());
    request.setSqlCommand(alertRequestDatabase.getSqlCommand());
    request.setDatabaseConnectionId(alertRequestDatabase.getDatabaseConnectionId());
    request.setActive(true);
    databaseThresholdConfigService.createOrUpdate(request);
  }

  @Override
  @Transactional(rollbackFor = BusinessException.class)
  public void rejectById(String id, String rejectedReason) throws BusinessException {
    var alertRequest = alertRequestRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.ALERT_REQUEST_NOT_FOUND));
    var createdBy = alertRequest.getCreatedBy();
    // Add logic to change status to REJECTED
    if (!AlertRequestStatusEnum.WAITING_APPROVAL.equals(alertRequest.getStatus())) {
      throw new BusinessException(ErrorCode.ALERT_REQUEST_CAN_NOT_REJECTED, alertRequest.getStatus());
    }
    alertRequest.setStatus(AlertRequestStatusEnum.REJECTED);
    alertRequest.setRejectedReason(rejectedReason);
    alertRequestRepository.save(alertRequest);
    sendNotification(id, alertRequest.getStatus(), createdBy, rejectedReason);
  }

  @Override
  public List<AlertRequestEntity> findAllByServiceId(String id) {
    return alertRequestRepository.findAllByServiceIdAndStatusNot(id, AlertRequestStatusEnum.APPROVED);
  }

  @Override
  public List<AlertRequestEntity> findAllByApplicationId(String id) {
    return alertRequestRepository.findAllByApplicationIdAndStatusNot(id, AlertRequestStatusEnum.APPROVED);
  }

  @Override
  public List<AlertRequestEntity> findAllByPriorityId(Long id) {
    return alertRequestRepository.findAllByPriorityIdAndStatusNot(id, AlertRequestStatusEnum.APPROVED);
  }


  @Override
  public void deleteWithId(String id) throws BusinessException {

    var config = alertRequestRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.ALERT_REQUEST_NOT_FOUND));
    if (!getUserName().equals(config.getCreatedBy())
        && !commonAclPermissionService.isAnyPermission(List.of(new AclPermissionModel(
        PermissionModuleEnum.ALERT_REQUEST, PermissionActionEnum.APPROVE)))) {
      throw new BusinessException(ErrorCode.USER_DONT_HAVE_PERMISSIONS);
    }
    delete(config);
  }

  public void sendNotification(String id, AlertRequestStatusEnum status, String owner, String reason) {
    String url = MessageFormat.format("{0}alert-requests/{1}", mbmonitorUrl, id);
    String user = getUserName();
    String message;
    NotificationRequest.NotificationRequestBuilder notification = NotificationRequest.builder()
        .title(NotificationConstants.ALERT_REQUEST_TITLE)
        .type(NotificationTypeEnum.INFO)
        .sourceId(id)
        .sourceType(NotificationSourceTypeEnum.ALERT_REQUEST);
    switch (status) {
      case WAITING_APPROVAL -> {
        message = ProseMirrorMessageConstants.submittedAlertRequest(user, url);
        notification.content(message).roleIds(getApproverRoleIds());
      }
      case APPROVED -> {
        message = ProseMirrorMessageConstants.approvedAlertRequest(user, url);
        notification.content(message).userName(owner);
      }
      case REJECTED -> {
        message = ProseMirrorMessageConstants.rejectedAlertRequest(user, url, reason);
        notification.content(message).userName(owner);
      }
      default -> {
        return;
      }
    }
    pushNotificationService.push(notification.build());
  }

  private List<Long> getApproverRoleIds() {
    return sysPermissionService.findAllByModuleAndAction(
            PermissionModuleEnum.ALERT_REQUEST,
            PermissionActionEnum.APPROVE
        ).stream()
        .map(SysPermissionEntity::getRoleId)
        .toList();
  }


}
