package vn.com.mbbank.kanban.mbmonitor.server.services;

import java.util.List;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseThresholdConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.DatabaseThresholdConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.DatabaseThresholdConfigResponse;

/**
 * interface logic CollectEmailConfigService .
 */
public interface DatabaseThresholdConfigService extends BaseService<DatabaseThresholdConfigEntity, String> {
  /**
   * Saves or updates a database threshold config0.
   *
   * @param request The database threshold config data to be saved or updated.
   * @return The saved or updated database threshold config.
   * @throws BusinessException If an error occurs during the operation.
   */
  DatabaseThresholdConfigResponse createOrUpdate(DatabaseThresholdConfigRequest request)
      throws BusinessException;

  /**
   * Checks if any DatabaseThresholdConfigEntity record exists with the given name.
   *
   * @param name The name of the view to check for existence.
   * @return true if at least one DatabaseThresholdConfigEntity record with the specified name exists, false otherwise.
   */
  boolean existByName(String name);

  /**
   * Checks if any DatabaseThresholdConfigEntity record exists with the given name and ID.
   *
   * @param name The name of DatabaseThresholdConfigEntity.
   * @param id   The ID of the DatabaseThresholdConfigEntity record.
   * @return true if a DatabaseThresholdConfigEntity record with the specified name and ID exists, false otherwise.
   */
  boolean existByIdNotAndName(String id, String name);

  /**
   * find database threshold config by id.
   *
   * @param id The id to count by.
   * @return The data of DatabaseThresholdConfigResponse objects with id of DatabaseThresholdConfig.
   */
  DatabaseThresholdConfigResponse findWithId(String id) throws BusinessException;


  /**
   * find all  database threshold config by paginationRequest.
   *
   * @param paginationRequest The PaginationRequestDTO.
   * @return The page data of DatabaseThresholdConfigResponse objects with id of DatabaseThresholdConfig.
   */
  Page<DatabaseThresholdConfigResponse> findAll(PaginationRequestDTO paginationRequest);

  /**
   * update status active or inactive.
   *
   * @param id id of  DatabaseThresholdConfig.
   * @return The data of DatabaseThresholdConfigResponse object with id of DatabaseThresholdConfig.
   */
  DatabaseThresholdConfigResponse updateStatus(String id) throws BusinessException;

  /**
   * find by id of database threshold.
   *
   * @param id if of database threshold.
   * @return list DatabaseThresholdConfigResponse .
   */
  List<DatabaseThresholdConfigResponse> findAllByDatabaseConnectionId(Long id);

  /**
   * find all database connection config by id of service.
   *
   * @param id The id of service .
   * @return The list of DatabaseThresholdConfigEntity objects.
   */

  List<DatabaseThresholdConfigEntity> findAllByServiceId(String id);

  /**
   * find all database connection config by id of application.
   *
   * @param id The id of application.
   * @return The list of DatabaseThresholdConfigEntity objects.
   */

  List<DatabaseThresholdConfigEntity> findAllByApplicationId(String id);

  /**
   * find all database connection config by id of v.
   *
   * @param id The id of alert priority config.
   * @return The list of DatabaseThresholdConfigEntity objects.
   */

  List<DatabaseThresholdConfigEntity> findAllByPriorityId(Long id);

  /**
   * delete by databaseThresholdId.
   *
   * @param databaseThresholdId configId.
   */
  void deleteWithId(String databaseThresholdId) throws BusinessException;

}
