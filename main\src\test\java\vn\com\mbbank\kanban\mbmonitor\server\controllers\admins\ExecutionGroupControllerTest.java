package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionGroupRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionGroupResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionGroupDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionGroupService;
import vn.com.mbbank.kanban.test.ControllerTest;

@ExtendWith(MockitoExtension.class)
class ExecutionGroupControllerTest extends ControllerTest {

  @Mock
  private ExecutionGroupService executionGroupService;

  @Mock
  private ExecutionGroupDependencyService executionGroupDependencyService;

  @Mock
  private CommonAclPermissionService commonAclPermissionService;

  @InjectMocks
  @Spy
  private ExecutionGroupController executionGroupController;

  private ExecutionGroupResponse executionGroupResponse;
  private ExecutionGroupRequest executionGroupRequest;
  private ExecutionGroupEntity executionGroupEntity;
  private String testId;

  @BeforeEach
  void setUp() {
    // Arrange common test data
    testId = "test-id-123";

    executionGroupEntity = new ExecutionGroupEntity();
    executionGroupEntity.setId(testId);
    executionGroupEntity.setName("Test Execution Group");
    executionGroupEntity.setDescription("Test Description");

    executionGroupResponse = ExecutionGroupResponse.builder()
        .id(testId)
        .name("Test Execution Group")
        .description("Test Description")
        .executionAmount(0)
        .build();

    executionGroupRequest = ExecutionGroupRequest.builder()
        .id(testId)
        .name("Test Execution Group")
        .description("Test Description")
        .build();
  }

  // Permission Tests
  @Test
  void findById_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.EXECUTION_GROUP, PermissionActionEnum.VIEW)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "findById", ExecutionGroupController.class);
  }

  @Test
  void findAllWithPaging_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.EXECUTION_GROUP, PermissionActionEnum.VIEW)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "findAllWithPaging", ExecutionGroupController.class);
  }

  @Test
  void findAll_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.USER_MANAGEMENT, PermissionActionEnum.VIEW),
        new AclPermissionModel(PermissionModuleEnum.ROLE_MANAGEMENT, PermissionActionEnum.VIEW),
        new AclPermissionModel(PermissionModuleEnum.EXECUTION, PermissionActionEnum.EDIT),
        new AclPermissionModel(PermissionModuleEnum.EXECUTION, PermissionActionEnum.VIEW)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "findAll", ExecutionGroupController.class);
  }

  @Test
  void findAllWithExecutionAmount_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.USER_MANAGEMENT, PermissionActionEnum.VIEW),
        new AclPermissionModel(PermissionModuleEnum.ROLE_MANAGEMENT, PermissionActionEnum.VIEW)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "findAllWithExecutionAmount", ExecutionGroupController.class);
  }

  @Test
  void deleteById_shouldHaveCorrectPermissions() {
    // Arrange
    List<AclPermissionModel> expectedPermissions = List.of(
        new AclPermissionModel(PermissionModuleEnum.EXECUTION_GROUP, PermissionActionEnum.DELETE)
    );
    // Act & Assert
    verifyPermissions(expectedPermissions, "deleteById", ExecutionGroupController.class);
  }

  // Functional Tests
  @Test
  void findById_success_whenFound() throws BusinessException {
    // Arrange
    when(executionGroupService.findWithId(testId)).thenReturn(executionGroupEntity);

    // Act
    ResponseData<ExecutionGroupResponse> response = executionGroupController.findById(testId);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(testId, response.getData().getId());
    verify(executionGroupService, times(1)).findWithId(testId);
  }

  @Test
  void findAllWithPaging_success() throws BusinessException {
    // Arrange
    PaginationRequest requestDTO = new PaginationRequest();
    Page<ExecutionGroupEntity> pageEntity = new PageImpl<>(Collections.singletonList(executionGroupEntity));
    when(executionGroupService.findAllWithPaging(any(PaginationRequest.class))).thenReturn(pageEntity);

    // Act
    ResponseData<Page<ExecutionGroupResponse>> response = executionGroupController.findAllWithPaging(requestDTO);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(1, response.getData().getTotalElements());
    assertEquals(testId, response.getData().getContent().get(0).getId());
    verify(executionGroupService, times(1)).findAllWithPaging(requestDTO);
  }

  @Test
  void findAllWithPermission_success() throws BusinessException {
    // Arrange
    PaginationRequest requestDTO = new PaginationRequest();
    Page<ExecutionGroupEntity> pageEntity = new PageImpl<>(Collections.singletonList(executionGroupEntity));
    when(executionGroupService.findAllWithPermission(any(PaginationRequest.class))).thenReturn(pageEntity);

    // Act
    ResponseData<Page<ExecutionGroupResponse>> response = executionGroupController.findAllWithPermission(requestDTO);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(1, response.getData().getTotalElements());
    assertEquals(testId, response.getData().getContent().get(0).getId());
    verify(executionGroupService, times(1)).findAllWithPermission(requestDTO);
  }

  @Test
  void findAll_success() {
    // Arrange
    List<ExecutionGroupEntity> listEntity = Collections.singletonList(executionGroupEntity);
    when(executionGroupService.findAll(any(Sort.class))).thenReturn(listEntity);

    // Act
    ResponseData<List<ExecutionGroupResponse>> response = executionGroupController.findAll("abc");

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(1, response.getData().size());
    assertEquals(testId, response.getData().get(0).getId());
    verify(executionGroupService, times(1)).findAll(any(Sort.class));
  }

  @Test
  void findAllWithExecutionAmount_success() {
    // Arrange
    List<ExecutionGroupResponse> listResponse = Collections.singletonList(executionGroupResponse);
    when(executionGroupService.findAllWithExecutionAmount()).thenReturn(listResponse);

    // Act
    ResponseData<List<ExecutionGroupResponse>> response = executionGroupController.findAllWithExecutionAmount();

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(1, response.getData().size());
    assertEquals(testId, response.getData().get(0).getId());
    verify(executionGroupService, times(1)).findAllWithExecutionAmount();
  }

  @Test
  void save_success_whenCreateOrUpdate() throws BusinessException {
    // Arrange
    doNothing().when(executionGroupController).makeSureCreateOrUpdate(any(), any(), any(), any());
    when(executionGroupService.createOrUpdate(any(ExecutionGroupRequest.class))).thenReturn(executionGroupEntity);

    // Act
    ResponseData<ExecutionGroupResponse> response = executionGroupController.save(executionGroupRequest);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertNotNull(response.getData());
    assertEquals(testId, response.getData().getId());
    verify(executionGroupService, times(1)).createOrUpdate(executionGroupRequest);
    verify(executionGroupController, times(1)).makeSureCreateOrUpdate(
        PermissionModuleEnum.EXECUTION_GROUP,
        PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT,
        Collections.singleton(testId));
  }

  @Test
  void deleteById_success() throws BusinessException {
    // Arrange
    doNothing().when(executionGroupDependencyService).deleteWithId(testId);

    // Act
    ResponseData<String> response = executionGroupController.deleteById(testId);

    // Assert
    assertNotNull(response);
    assertEquals(200, response.getStatus());
    assertEquals("OK", response.getData());
    verify(executionGroupDependencyService, times(1)).deleteWithId(testId);
  }
}
