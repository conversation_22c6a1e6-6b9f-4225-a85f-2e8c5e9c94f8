package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import static vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants.COMMON_DESCRIPTION_MAX_LENGTH;
import static vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants.COMMON_NAME_MAX_LENGTH;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import vn.com.mbbank.kanban.mbmonitor.common.constants.KanbanRegexContants;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConfigCollectMapTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/12/2024
 */
@Data
public class DatabaseCollectRequest {
  private Long id;
  @NotNull
  @Size(min = 1, max = COMMON_NAME_MAX_LENGTH)
  private String name;

  @Size(max = COMMON_DESCRIPTION_MAX_LENGTH)
  private String description;

  @NotNull
  private Long connectionId;

  @NotNull
  @Size(max = 65536)
  private String sqlCommand;

  @NotNull
  @Size(max = 30)
  @Pattern(regexp = KanbanRegexContants.DATABASE_COLUMN,
      message = "createdDateField can only contain A-Z, a-z, 0-9, _")
  private String createdDateField;

  @NotNull
  @Size(max = 30)
  @Pattern(regexp = KanbanRegexContants.DATABASE_COLUMN,
      message = "alertIdField can only contain A-Z, a-z, 0-9, _")
  private String alertIdField;

  @NotNull
  private Long interval;

  @NotNull
  private ConfigCollectMapTypeEnum serviceNameType;

  private String serviceId;

  @Size(max = 30)
  @Pattern(regexp = KanbanRegexContants.DATABASE_COLUMN,
      message = "serviceMapValue can only contain A-Z, a-z, 0-9, _")
  private String serviceMapValue;

  @NotNull
  private ConfigCollectMapTypeEnum applicationType;
  private String applicationId;

  @Size(max = 30)
  @Pattern(regexp = KanbanRegexContants.DATABASE_COLUMN,
      message = "applicationMapValue can only contain A-Z, a-z, 0-9, _")
  private String applicationMapValue;

  @NotNull
  @Size(max = 30)
  @Pattern(regexp = KanbanRegexContants.DATABASE_COLUMN,
      message = "alertMapValue can only contain A-Z, a-z, 0-9, _")
  private String alertMapValue;

  @NotNull
  private ConfigCollectMapTypeEnum priorityType;
  private Long priorityId;

  @Size(max = 30)
  @Pattern(regexp = KanbanRegexContants.DATABASE_COLUMN,
      message = "priorityMapValue can only contain A-Z, a-z, 0-9, _")
  private String priorityMapValue;

  @NotNull
  private ConfigCollectMapTypeEnum contactType;

  @Size(max = 30)
  @Pattern(regexp = KanbanRegexContants.DATABASE_COLUMN,
      message = "contactMapValue can only contain A-Z, a-z, 0-9, _")
  private String contactMapValue;
  private String contactCustomValue;
  private Boolean isActive;
}