package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution;

import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionApiAuthTypeEnum;

/**
 * AuthApiModel.
 */
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AuthenticationApiRequest {
  ExecutionApiAuthTypeEnum authType;
  @Size(max = CommonConstants.EXECUTION_API_TOKEN_MAX_LENGTH)
  String token;
  @Size(max = CommonConstants.EXECUTION_API_USERNAME_MAX_LENGTH)
  String username;
  @Size(max = CommonConstants.EXECUTION_API_PASSWORD_MAX_LENGTH)
  String password;
}
