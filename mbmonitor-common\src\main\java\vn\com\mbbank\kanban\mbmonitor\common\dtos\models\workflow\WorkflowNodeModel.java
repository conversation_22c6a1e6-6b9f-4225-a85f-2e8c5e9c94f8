package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.BaseNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;

/**
 * WorkflowTemplateNodeRequest.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WorkflowNodeModel {
  @NotBlank
  String id;
  @NotNull
  WorkflowNodePositionModel position;
  @NotNull
  BaseNodeConfigurationModel configuration;
  @NotNull
  WorkflowNodeStatusEnum status;
  @NotNull
  WorkflowNodeTypeEnum type;
}
