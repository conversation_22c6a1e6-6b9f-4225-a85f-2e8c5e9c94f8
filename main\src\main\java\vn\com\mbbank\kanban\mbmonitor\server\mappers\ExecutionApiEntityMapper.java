package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.ModifyField;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ApiInfoRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ApiInfoResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.AuthenticationApiResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.BodyApiResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionApiEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionApiAuthTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionBodyTypeEnum;

/**
 * Mapper logic ExecutionApiEntity.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ExecutionApiEntityMapper extends KanbanBaseMapper<ExecutionApiEntity, ApiInfoRequest> {
  ExecutionApiEntityMapper INSTANCE = Mappers.getMapper(ExecutionApiEntityMapper.class);

  /**
   * map from ApiInfoModel to ExecutionApiEntity.
   *
   * @param entity  ExecutionApiEntity.
   * @param request ApiInfoRequest.
   * @return ExecutionApiEntity
   */
  default ExecutionApiEntity merge(@ModifyField ExecutionApiEntity entity, ApiInfoRequest request) {
    if (Objects.isNull(request)) {
      return null;
    }

    entity.setUrl(request.getUrl());
    entity.setMethod(request.getMethod());
    entity.setHttpVersion(request.getHttpVersion());
    entity.setEnableSsl(request.isEnableSsl());

    // Headers, Params, Urlencoded
    entity.setHeaders(request.getHeaders());
    entity.setParams(request.getParams());

    // Auth
    var auth = request.getAuthentication();
    var authType = Objects.nonNull(auth) ? auth.getAuthType() : ExecutionApiAuthTypeEnum.NONE;
    entity.setAuthType(authType);

    entity.setAuthToken(authType == ExecutionApiAuthTypeEnum.TOKEN ? auth.getToken() : null);
    entity.setUsername(authType == ExecutionApiAuthTypeEnum.BASIC ? auth.getUsername() : null);
    entity.setPassword(authType == ExecutionApiAuthTypeEnum.BASIC ? auth.getPassword() : null);

    // Body
    var body = request.getBody();
    var bodyType = Objects.nonNull(body) ? body.getBodyType() : ExecutionBodyTypeEnum.NONE;
    entity.setBodyType(bodyType);
    entity.setBodyRaw(bodyType == ExecutionBodyTypeEnum.RAW ? body.getBodyRaw() : null);
    entity.setFormUrlEncoded(bodyType == ExecutionBodyTypeEnum.URLENCODED ? body.getFormUrlEncoded() : null);
    entity.setContentType(bodyType == ExecutionBodyTypeEnum.RAW ? body.getContentType() : null);
    return entity;
  }

  /**
   * map from ExecutionApiEntity to ApiInfoModel.
   *
   * @param entity  EmailTemplateEntity.
   * @return ApiInfoModel
   */
  default ApiInfoResponse toModel(ExecutionApiEntity entity) {
    if (Objects.isNull(entity)) {
      return null;
    }
    ApiInfoResponse model = new ApiInfoResponse();
    model.setUrl(entity.getUrl());
    model.setMethod(entity.getMethod());
    model.setHttpVersion(entity.getHttpVersion());
    model.setEnableSsl(Boolean.TRUE.equals(entity.getEnableSsl()));

    model.setHeaders(entity.getHeaders());
    model.setParams(entity.getParams());

    BodyApiResponse body = new BodyApiResponse();
    body.setBodyType(entity.getBodyType());
    body.setBodyRaw(entity.getBodyRaw());
    body.setFormUrlEncoded(entity.getFormUrlEncoded());
    body.setContentType(entity.getContentType());
    model.setBody(body);

    AuthenticationApiResponse authentication = new AuthenticationApiResponse();
    authentication.setAuthType(entity.getAuthType());
    authentication.setToken(entity.getAuthToken());
    authentication.setUsername(entity.getUsername());
    authentication.setPassword(entity.getPassword());
    model.setAuthentication(authentication);

    return model;
  }
}
