package vn.com.mbbank.kanban.mbmonitor.server.services;

import java.util.List;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecuteScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecuteScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;

/**
 * interface logic Execution.
 */
public interface ExecutionService extends BaseService<ExecutionEntity, String> {

  /**
   * Finds by executionGroupId.
   *
   * @param executionGroupId the ID of group
   * @return list of Execution
   */
  List<ExecutionResponse> findAllByExecutionGroupId(String executionGroupId);

  /**
   * Find all execution.
   *
   * @param page page
   * @return page ExecutionResponse
   */
  Page<ExecutionResponse> findAll(PaginationRequestDTO page);

  /**
   * create or update execution.
   *
   * @param request ExecutionRequest
   * @return ExecutionEntity
   */
  ExecutionEntity createOrUpdate(ExecutionRequest request) throws BusinessException;

  /**
   * find
   * by id.
   *
   * @param id id
   * @return ExecutionEntity
   */
  ExecutionResponse findWithId(String id) throws BusinessException;

  /**
   * find by id with env info.
   *
   * @param id id
   * @return ExecutionEntity
   */
  ExecutionResponse findByIdWithVariable(String id) throws BusinessException;

  /**
   * execute request and return response.
   *
   * @param request ExecutionRunningRequest
   * @return ExecutionRunningResponse
   */
  ExecuteScriptResponse execute(ExecuteScriptRequest request) throws BusinessException;

  /**
   * Checks if any QuerySqlEntity record exists with the given databaseConnectionId.
   *
   * @param databaseConnectionId The id of the database connection.
   * @return true if at least one QuerySqlEntity record with the specified name exists, false otherwise.
   */
  boolean existsByDatabaseConnectionId(Long databaseConnectionId);

  /**
   * find all in ids.
   *
   * @param ids for find
   * @return list Execution
   */
  List<ExecutionEntity> findAllByIdIn(List<String> ids);

  /**
   * find all by type.
   *
   * @param type for find
   * @return list Execution
   */
  List<ExecutionResponse> findAllByType(ExecutionTypeEnum type);
}