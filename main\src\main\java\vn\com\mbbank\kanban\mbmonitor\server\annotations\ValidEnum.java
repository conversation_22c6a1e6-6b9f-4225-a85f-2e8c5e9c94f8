package vn.com.mbbank.kanban.mbmonitor.server.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import vn.com.mbbank.kanban.mbmonitor.server.validator.EnumValidator;

/**
 * Custom annotation to validate that the value of a field belongs to a specific Enum.
 *
 * @Constraint (validatedBy = EnumValidator.class)
 * @Target ({ ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER })
 * @Retention (RetentionPolicy.RUNTIME)
 *
 * @see EnumValidator
 *
 * <AUTHOR>
 * @created_date 09/26/2024
 */

@Constraint(validatedBy = EnumValidator.class)
@Target({ ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidEnum {
  /**
   * Specifies the Enum class whose values will be used for validation.
   *
   * @return The Enum class that should be used for validation.
   */
  Class<? extends Enum<?>> enumClass();

  /**
   * The error message that will be displayed if the value is not valid.
   *
   * @return The custom error message if the value is not part of the Enum.
   */
  String message() default "must be a valid enum value";

  /**
   * Groups for categorizing validation constraints.
   *
   * @return The array of group classes for this constraint.
   */
  Class<?>[] groups() default {};

  /**
   * Payload for clients to provide additional information about the validation.
   * This is typically used for extensibility purposes.
   *
   * @return The array of payload classes.
   */
  Class<? extends Payload>[] payload() default {};
}
