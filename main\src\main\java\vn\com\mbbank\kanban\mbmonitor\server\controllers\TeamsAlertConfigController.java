package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsSendModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TeamsConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TeamsConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.TeamsConfigResponseToModelMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.TeamsAlertConfigService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/22/2025
 */
@RestController
@RequestMapping(ServerUrl.TEAMS_ALERT_CONFIG_URL)
@RequiredArgsConstructor
public class TeamsAlertConfigController extends BaseController {
  private final TeamsAlertConfigService teamsAlertConfigService;

  /**
   * Get config alert teams.
   *
   * @return teams config
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.TEAMS_ALERT_CONFIG, action = PermissionActionEnum.CONFIG),
  })
  @GetMapping
  public ResponseData<TeamsConfigResponse> findConfig()
      throws BusinessException {
    return ResponseUtils.success(TeamsConfigResponseToModelMapper.INSTANCE.map(
        teamsAlertConfigService.findTeamsAlertConfig()));
  }

  /**
   * save or update config teams.
   *
   * @param request request
   * @return TeamsConfigResponse
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.TEAMS_ALERT_CONFIG, action = PermissionActionEnum.CONFIG),
  })
  @PostMapping
  public ResponseData<TeamsConfigResponse> save(@RequestBody TeamsConfigRequest request)
      throws BusinessException {
    return ResponseUtils.success(
        TeamsConfigResponseToModelMapper.INSTANCE.map(teamsAlertConfigService.saveConfig(request)));
  }

  /**
   * send message teams.
   *
   * @param body TeamsSendModel
   * @return TeamsSendModel
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.TEAMS_ALERT_CONFIG, action = PermissionActionEnum.SEND),
  })
  @PostMapping("send")
  public ResponseData<Object> send(@RequestBody @Valid TeamsSendModel body)
      throws BusinessException {
    return ResponseUtils.success(teamsAlertConfigService.sendMessage(body));
  }

  /**
   * send message teams.
   *
   * @return TeamsSendModel
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.TEAMS_ALERT_CONFIG, action = PermissionActionEnum.CONFIG),
  })
  @PostMapping("trigger-job")
  public ResponseData<Object> triggerJob()
      throws BusinessException {
    teamsAlertConfigService.triggerJob();
    return ResponseUtils.success("OK");
  }
}
