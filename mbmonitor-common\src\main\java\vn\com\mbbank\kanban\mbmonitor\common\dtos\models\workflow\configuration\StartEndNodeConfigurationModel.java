package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

/**
 * StartEndNodeConfigurationModel.
 */
@Data
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StartEndNodeConfigurationModel extends BaseNodeConfigurationModel {
}
