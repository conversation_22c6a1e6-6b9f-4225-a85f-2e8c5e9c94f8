package vn.com.mbbank.kanban.mbmonitor.common.utils.condition;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum;

/**
 * RuleGroupType of Condition.
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RuleGroupType extends RuleElement {
  ConditionCombinatorEnum combinator;
  List<RuleElement> rules;

  @Override
  public boolean check(Object object) {
    if (CollectionUtils.isEmpty(rules)) {
      return true;
    }
    for (RuleElement rule : rules) {
      boolean result = rule.check(object);
      if (ConditionCombinatorEnum.AND.equals(combinator) && !result) {
        return false;
      }
      if (ConditionCombinatorEnum.OR.equals(combinator) && result) {
        return true;
      }
    }
    return ConditionCombinatorEnum.AND.equals(combinator);
  }


  @Override
  public boolean check(Map<String, Object> object) {
    if (CollectionUtils.isEmpty(rules)) {
      return true;
    }
    for (RuleElement rule : rules) {
      boolean result = rule.check(object);
      if (ConditionCombinatorEnum.AND.equals(combinator) && !result) {
        return false;
      }
      if (ConditionCombinatorEnum.OR.equals(combinator) && result) {
        return true;
      }
    }
    return ConditionCombinatorEnum.AND.equals(combinator);
  }

  @Override
  public <V> boolean check(Object object, Function<Object, V> func) {
    if (CollectionUtils.isEmpty(rules)) {
      return true;
    }
    for (RuleElement rule : rules) {
      boolean result = rule.check(object, func);
      if (ConditionCombinatorEnum.AND.equals(combinator) && !result) {
        return false;
      }
      if (ConditionCombinatorEnum.OR.equals(combinator) && result) {
        return true;
      }
    }
    return ConditionCombinatorEnum.AND.equals(combinator);
  }

  @Override
  public boolean checkPriority(Long id) {
    for (RuleElement rule : rules) {
      boolean res = rule.checkPriority(id);
      if (res) {
        return true;
      }
    }
    return false;
  }

  @Override
  public boolean checkCustomObject(Long id) {
    for (RuleElement rule : rules) {
      boolean res = rule.checkCustomObject(id);
      if (res) {
        return true;
      }
    }
    return false;
  }

  @Override
  public List<Long> getCustomObjectIds(Class<?> clazz) {
    Set<String> fieldNames = Arrays.stream(clazz.getDeclaredFields())
            .map(Field::getName)
            .collect(Collectors.toSet());
    return getCustomObjectIds(fieldNames);
  }

  @Override
  public List<Long> getCustomObjectIds(Set<String> fieldNames) {
    if (CollectionUtils.isEmpty(rules)) {
      return List.of();
    }

    return rules.stream()
            .map(rule -> extractIdsFromRule(rule, fieldNames))
            .filter(Objects::nonNull)
            .flatMap(List::stream)
            .distinct()
            .collect(Collectors.toList());
  }

  @Override
  public void setValueFromExternal(Object newVal) {
    //do nothing
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RuleGroupType that = (RuleGroupType) o;
    return combinator == that.combinator && Objects.equals(rules, that.rules);
  }

  @Override
  public int hashCode() {
    return Objects.hash(combinator, rules);
  }

  @Override
  public String toString() {
    if (CollectionUtils.isEmpty(rules)) {
      return "";
    }
    if (rules.size() == 1) {
      return rules.stream().map(RuleElement::toString).collect(Collectors.joining(" " + combinator.name() + " "));
    }
    return rules.stream().map(RuleElement::toString).collect(Collectors.joining(" " + combinator.name() + " ",
        "[", "] "));
  }

  /**
   * Extract id form rule group.
   *
   * @param rule to extract
   * @param fieldNames for check
   * @return list id
   */
  public List<Long> extractIdsFromRule(RuleElement rule, Set<String> fieldNames) {
    if (rule instanceof RuleCondition<?> condition) {
      String field = condition.getField();
      if (!fieldNames.contains(field)) {
        try {
          return List.of(Long.parseLong(field));
        } catch (NumberFormatException ignored) {
          return null;
        }
      }
    } else if (rule instanceof RuleGroupType group) {
      return group.getCustomObjectIds(fieldNames);
    }
    return null;
  }
}
