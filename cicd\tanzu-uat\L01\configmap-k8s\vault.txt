vault.hashicorp.com/tls-skip-verify: "true"
traffic.sidecar.istio.io/excludeOutboundPorts: "8200"
vault.hashicorp.com/agent-inject: 'true'
vault.hashicorp.com/role: 'r-c0-53'
vault.hashicorp.com/auth-path: 'auth/kubernetes-tanzu-uat'
vault.hashicorp.com/agent-pre-populate-only: 'true'
vault.hashicorp.com/agent-requests-cpu: '50m'
vault.hashicorp.com/agent-limits-cpu: '100m'
vault-hashicorp-com-agent-requests-mem: '50Mi'
vault.hashicorp.com/agent-limits-mem: '100Mi'
vault.hashicorp.com/agent-cache-enable: 'true'
vault.hashicorp.com/agent-revoke-on-shutdown: 'true'
vault.hashicorp.com/secret-volume-path: /deployment/sc
vault.hashicorp.com/agent-inject-secret-application-sc-common.properties: 'kv/c0/dev-l01/i-02-02/common/HgrIR60aIhxOAWRJOdTr7JwO4iSkdptrzELABAllNfahoqItA3uVmfpJqXyPp-qnLrbFd-8O6aEZyNd75MV-lJingS8t-o9m1LS2cLYDcfp1uBJJ68gApNsl8VfFG6tiOAUlrFT8lFyW99OGCTL0xTXzK192v-Y_nvqoEHoVRt4=/config-sc-common-53'
vault.hashicorp.com/agent-inject-template-application-sc-common.properties: |
  {{ with secret "kv/c0/dev-l01/i-02-02/common/HgrIR60aIhxOAWRJOdTr7JwO4iSkdptrzELABAllNfahoqItA3uVmfpJqXyPp-qnLrbFd-8O6aEZyNd75MV-lJingS8t-o9m1LS2cLYDcfp1uBJJ68gApNsl8VfFG6tiOAUlrFT8lFyW99OGCTL0xTXzK192v-Y_nvqoEHoVRt4=/config-sc-common-53" -}}
  spring.datasource.hikari.password={{ .Data.data.mbmonitor_spring_datasource_hikari_password }}
  kanban.keycloak.client-secret={{ .Data.data.mbmonitor_kanban_keycloak_client_secret }}
  superiors.sql-execution.private-password={{ .Data.data.mbmonitor_superiors_sql_execution_private_password }}
  kanban.redis.password={{ .Data.data.mbmonitor_kanban_redis_password }}
  kanban.kafka.password={{ .Data.data.mbmonitor_kanban_kafka_password }}
  {{- end }}
