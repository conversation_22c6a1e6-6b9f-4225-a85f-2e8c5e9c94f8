package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.ExecutionHistoryCursorModel;

/**
 * ExecutionHistorySearchRequest.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@FieldDefaults(level = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
public class ExecutionHistorySearchRequest extends ExecutionHistoryCursorModel {
  int pageSize = 10;
}
