package vn.com.mbbank.kanban.mbmonitor.server.controllers.core;

import jakarta.validation.Valid;
import java.sql.SQLException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.SqlExecutionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.server.services.systems.SuperiorsService;

/**
 * Controller logic Superior.
 */
@RestController
@RequestMapping(ServerUrl.SUPERIOR_URL)
public class SuperiorsController extends BaseController {
  @Autowired
  private SuperiorsService service;

  /**
   * Execute direct.
   *
   * @param body Data to execute.
   * @return Result after execute
   * @throws BusinessException BusinessException
   * @throws SQLException      SQLException
   */
  @PostMapping("/sql-execution") //bypass for pentest
  public ResponseData<SqlExecutionResponse> sqlExecution(
      @RequestBody @Valid SqlExecutionRequest body) throws BusinessException, SQLException {
    makeSureUserAdmin();
    var result = service.executeQuery(body);
    return new ResponseData<SqlExecutionResponse>().success(result);
  }
}
