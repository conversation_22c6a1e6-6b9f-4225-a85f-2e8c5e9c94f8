package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration;

import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;

/**
 * EmailNodeConfigurationModel.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailNodeConfigurationModel extends BaseNodeConfigurationModel {
  WorkflowNodeTypeEnum type = WorkflowNodeTypeEnum.EMAIL;
  @Builder.Default
  List<String> emailTemplateIds = new ArrayList<>();
  @Builder.Default
  Boolean allTemplates = false;
}
