package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailConfigPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.EmailConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.services.CollectEmailConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailService;

class EmailConfigControllerTest {

  @Mock
  private EmailConfigService emailConfigService;

  @Mock
  private EmailService emailService;

  @Mock
  private CollectEmailConfigService collectEmailConfigService;
  @InjectMocks
  @Spy
  private EmailConfigController emailConfigController;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void findAll_success() {
    Page<EmailConfigResponse> mockPage = new PageImpl<>(Collections.singletonList(new EmailConfigResponse()));
    when(emailConfigService.findAll(any(EmailConfigPaginationRequest.class))).thenReturn(mockPage);

    EmailConfigPaginationRequest paginationRequest = new EmailConfigPaginationRequest();
    var response = emailConfigController.findAll(paginationRequest);

    assertEquals(1, response.getData().getContent().size());
    verify(emailConfigService, times(1)).findAll(paginationRequest);
  }

  @Test
  void findAllCollectEmailConfigById_success() {
    when(collectEmailConfigService.findAllByEmailConfigId(any())).thenReturn(List.of());

    var response = emailConfigController.findAllCollectEmailConfigById(1L);

    assertEquals(0, response.getData().size());
    verify(collectEmailConfigService, times(1)).findAllByEmailConfigId(any());
  }

  @Test
  void testCreateOrUpdate() throws Exception {
    EmailConfigRequest request = new EmailConfigRequest();
    EmailConfigResponse mockResponse = new EmailConfigResponse();
    doNothing().when(emailConfigController)
        .makeSureCreateOrUpdate(any(), any(), any(), any());
    when(emailConfigService.createOrUpdate(any(EmailConfigRequest.class))).thenReturn(mockResponse);
    var response = emailConfigController.createOrUpdate(request);

    assertEquals(mockResponse, response.getData());
    verify(emailConfigService, times(1)).createOrUpdate(request);
  }

  @Test
  void findById_success() {
    Long id = 1L;
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    when(emailConfigService.findById(id)).thenReturn(emailConfigEntity);

    var response = emailConfigController.findById(id);

    verify(emailConfigService, times(1)).findById(id);
  }


  @Test
  void deleteById_success() throws BusinessException {
    Long id = 1L;
    doNothing().when(emailConfigService).deleteEmailConfigById(id);
    var response = emailConfigController.deleteById(id);
    assertEquals("OK", response.getData());
    verify(emailConfigService, times(1)).deleteEmailConfigById(id);
  }

  @Test
  void active_success() throws Exception {
    Long id = 1L;
    EmailConfigResponse mockResponse = new EmailConfigResponse();
    when(emailConfigService.updateStatus(id)).thenReturn(mockResponse);

    var response = emailConfigController.active(id);

    assertEquals(mockResponse, response.getData());
    verify(emailConfigService, times(1)).updateStatus(id);
  }
}
