package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowNodePositionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.BaseNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;

/**
 * WorkflowTemplateNodeRequest.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WorkflowTemplateNodeRequest {
  @NotBlank
  String id;
  @NotNull
  WorkflowNodePositionModel position;
  @NotNull
  BaseNodeConfigurationModel configuration;
  @NotNull
  WorkflowNodeTypeEnum type;
}
