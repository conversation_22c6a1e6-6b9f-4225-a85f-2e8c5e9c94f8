package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.MonitorWebConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.MonitorWebConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.MonitorWebConfigService;
import vn.com.mbbank.kanban.test.ControllerTest;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class MonitorWebConfigControllerTest extends ControllerTest {

    @Mock
    private MonitorWebConfigService monitorWebConfigService;

    @InjectMocks
    @Spy
    private MonitorWebConfigController monitorWebConfigController;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findAll_success() {
        verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.MONITOR_WEB_CONFIG, PermissionActionEnum.VIEW)),
                "findAll", MonitorWebConfigController.class);
        Page<MonitorWebConfigResponse> mockPage =
                new PageImpl<>(Collections.singletonList(new MonitorWebConfigResponse()));
        when(monitorWebConfigService.findAll(any(PaginationRequest.class))).thenReturn(mockPage);
        
        PaginationRequest paginationRequest = new PaginationRequest();
        ResponseData<Page<MonitorWebConfigResponse>> response =
                monitorWebConfigController.findAll(paginationRequest);

        assertEquals(1, response.getData().getContent().size());
        verify(monitorWebConfigService, times(1)).findAll(paginationRequest);
    }

    @Test
    void createOrUpdate_success() throws Exception {
        doNothing().when(monitorWebConfigController)
                .makeSureCreateOrUpdate(any(), any(), any(), any());
        MonitorWebConfigRequest request = new MonitorWebConfigRequest();
        MonitorWebConfigResponse mockResponse = new MonitorWebConfigResponse();
        when(monitorWebConfigService.createOrUpdate(any(MonitorWebConfigRequest.class))).thenReturn(
                mockResponse);

        ResponseData<MonitorWebConfigResponse> response =
                monitorWebConfigController.createOrUpdate(request);

        assertEquals(mockResponse, response.getData());
        verify(monitorWebConfigService, times(1)).createOrUpdate(request);
    }

    @TestForDev
    void findById_success() throws Exception {
        verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.MONITOR_WEB_CONFIG, PermissionActionEnum.VIEW)),
                "findById", MonitorWebConfigController.class);
        String id = "123";
        MonitorWebConfigResponse mockResponse = new MonitorWebConfigResponse();
        when(monitorWebConfigService.findWithId(id)).thenReturn(mockResponse);

        ResponseData<MonitorWebConfigResponse> response = monitorWebConfigController.findById(id);

        verify(monitorWebConfigService, times(1)).findWithId(id);
    }

    @TestForDev
    void deleteById_success() throws BusinessException {
        verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.MONITOR_WEB_CONFIG, PermissionActionEnum.DELETE)),
                "deleteById", MonitorWebConfigController.class);
        String id = "1L";
        doNothing().when(monitorWebConfigService).deleteWithId(id);

        ResponseData<String> response = monitorWebConfigController.deleteById(id);

        assertEquals("OK", response.getData());
        verify(monitorWebConfigService, times(1)).deleteWithId(id);
    }

    @TestForDev
    void active_success() throws Exception {
        verifyPermissions(List.of(new AclPermissionModel(PermissionModuleEnum.MONITOR_WEB_CONFIG, PermissionActionEnum.EDIT)),
                "active", MonitorWebConfigController.class);
        String id = "123";
        MonitorWebConfigResponse mockResponse = new MonitorWebConfigResponse();
        when(monitorWebConfigService.updateStatus(id)).thenReturn(mockResponse);

        ResponseData<MonitorWebConfigResponse> response = monitorWebConfigController.active(id);

        assertEquals(mockResponse, response.getData());
        verify(monitorWebConfigService, times(1)).updateStatus(id);
    }
}
