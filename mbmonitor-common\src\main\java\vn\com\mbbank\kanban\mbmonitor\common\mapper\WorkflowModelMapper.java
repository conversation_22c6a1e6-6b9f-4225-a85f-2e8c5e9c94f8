package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowEdgeModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowNodeModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.BaseNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.EmailNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.ExecutionNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowNodeDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowNodeEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowTemplateEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;

/**
 * WorkflowTemplateResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WorkflowModelMapper
    extends KanbanBaseMapper<WorkflowModel, WorkflowTemplateEntity> {
  WorkflowModelMapper INSTANCE = Mappers.getMapper(WorkflowModelMapper.class);

  /**
   * map from WorkflowTemplateNodeEntity list and WorkflowTemplateNodeDependencyEntity list to WorkflowModel.
   * Dependencies are used to populate emailTemplateIds and executionIds in node configurations.
   * Edges are calculated from incomingNodeIds and outgoingNodeIds.
   *
   * @param workflow     workflow entity
   * @param nodes        list of WorkflowTemplateNodeEntity.
   * @param dependencies list of WorkflowTemplateNodeDependencyEntity for configuration.
   * @return WorkflowModel.
   */
  default WorkflowModel map(WorkflowEntity workflow, List<WorkflowNodeEntity> nodes,
                            List<WorkflowNodeDependencyEntity> dependencies) {
    if (KanbanCommonUtil.isEmpty(nodes)) {
      nodes = new ArrayList<>();
    }
    if (KanbanCommonUtil.isEmpty(dependencies)) {
      dependencies = new ArrayList<>();
    }

    // Map nodes with dependencies for configuration
    List<WorkflowNodeModel> workflowNodes = new ArrayList<>();
    for (WorkflowNodeEntity node : nodes) {
      workflowNodes.add(mapNodeEntityToModel(node, dependencies));
    }

    // Generate edges from incomingNodeIds and outgoingNodeIds of nodes
    List<WorkflowEdgeModel> workflowEdges = generateEdgesFromNodes(nodes);

    return WorkflowModel.builder()
        .startTime(workflow.getStartTime())
        .endTime(workflow.getEndTime())
        .status(workflow.getStatus())
        .id(workflow.getId())
        .nodes(workflowNodes)
        .edges(workflowEdges)
        .build();
  }

  /**
   * Map WorkflowTemplateNodeEntity to WorkflowNodeModel with dependencies for configuration.
   *
   * @param nodeEntity   WorkflowTemplateNodeEntity.
   * @param dependencies list of WorkflowTemplateNodeDependencyEntity.
   * @return WorkflowNodeModel.
   */
  default WorkflowNodeModel mapNodeEntityToModel(WorkflowNodeEntity nodeEntity,
                                                 List<WorkflowNodeDependencyEntity> dependencies) {
    if (KanbanCommonUtil.isEmpty(nodeEntity)) {
      return null;
    }

    // Get base configuration from node entity
    var configuration = nodeEntity.getConfiguration();

    // Merge dependencies into configuration based on node type
    configuration = mergeConfigurationWithDependencies(nodeEntity.getId(), configuration, dependencies);

    return WorkflowNodeModel.builder()
        .id(nodeEntity.getId())
        .position(nodeEntity.getPosition())
        .configuration(configuration)
        .status(nodeEntity.getStatus())
        .type(nodeEntity.getType())
        .build();
  }

  /**
   * Map WorkflowTemplateNodeEntity to WorkflowNodeModel (without dependencies).
   *
   * @param nodeEntity WorkflowTemplateNodeEntity.
   * @return WorkflowNodeModel.
   */
  default WorkflowNodeModel mapNodeEntityToModel(WorkflowNodeEntity nodeEntity) {
    return mapNodeEntityToModel(nodeEntity, new ArrayList<>());
  }

  /**
   * Generate edges from nodes' incomingNodeIds and outgoingNodeIds.
   *
   * @param nodes list of WorkflowTemplateNodeEntity.
   * @return list of WorkflowEdgeModel.
   */
  default List<WorkflowEdgeModel> generateEdgesFromNodes(List<WorkflowNodeEntity> nodes) {
    List<WorkflowEdgeModel> edges = new ArrayList<>();

    for (WorkflowNodeEntity node : nodes) {
      String nodeId = node.getId();

      // Create edges from outgoing connections
      List<String> outgoingNodeIds = node.getOutgoingNodeIds();
      if (!KanbanCommonUtil.isEmpty(outgoingNodeIds)) {
        for (String targetNodeId : outgoingNodeIds) {
          edges.add(WorkflowEdgeModel.builder()
              .source(nodeId)
              .target(targetNodeId)
              .build());
        }
      }
    }

    return edges;
  }

  /**
   * Merge dependencies into node configuration based on node type.
   *
   * @param nodeId        the node ID to find dependencies for.
   * @param configuration the base configuration.
   * @param dependencies  list of WorkflowTemplateNodeDependencyEntity.
   * @return updated configuration with dependencies merged.
   */
  default BaseNodeConfigurationModel mergeConfigurationWithDependencies(
      String nodeId,
      BaseNodeConfigurationModel configuration,
      List<WorkflowNodeDependencyEntity> dependencies) {

    if (KanbanCommonUtil.isEmpty(dependencies) || configuration == null) {
      return configuration;
    }

    // Filter dependencies for this specific node
    List<WorkflowNodeDependencyEntity> nodeDependencies = dependencies.stream()
        .filter(dep -> nodeId.equals(dep.getWorkflowNodeId()))
        .collect(Collectors.toList());

    if (KanbanCommonUtil.isEmpty(nodeDependencies)) {
      return configuration;
    }

    // Handle EMAIL node configuration
    if (configuration instanceof EmailNodeConfigurationModel emailConfig) {
      List<String> emailTemplateIds = nodeDependencies.stream()
          .filter(dep -> WorkflowNodeTypeEnum.EMAIL.equals(dep.getType()))
          .map(WorkflowNodeDependencyEntity::getReferenceId)
          .collect(Collectors.toList());

      emailConfig.setEmailTemplateIds(emailTemplateIds);
      return emailConfig;
    }

    // Handle EXECUTION node configuration (includes EXECUTION, API, PYTHON, SQL)
    if (configuration instanceof ExecutionNodeConfigurationModel executionConfig) {
      List<String> executionIds = nodeDependencies.stream()
          .filter(dep -> WorkflowNodeTypeEnum.EXECUTION.equals(dep.getType())
              || WorkflowNodeTypeEnum.API.equals(dep.getType())
              || WorkflowNodeTypeEnum.PYTHON.equals(dep.getType())
              || WorkflowNodeTypeEnum.SQL.equals(dep.getType()))
          .map(WorkflowNodeDependencyEntity::getReferenceId)
          .collect(Collectors.toList());

      executionConfig.setExecutionIds(executionIds);
      return executionConfig;
    }

    return configuration;
  }
}
