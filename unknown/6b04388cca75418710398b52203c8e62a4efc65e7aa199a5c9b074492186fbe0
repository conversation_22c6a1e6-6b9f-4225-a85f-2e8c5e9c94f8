package vn.com.mbbank.kanban.mbmonitor.server.dtos.model;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CollectEmailConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.enums.CollectEmailAlertContentTypeEnum;

/**
 * Model request service to create or update collect email config.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CollectEmailConfigModel {
  Long id;
  String name;
  String description;
  Long emailConfigIntervalTime;
  Long emailConfigId;
  String emailConfigEmail;
  RuleGroupType ruleGroup;
  String ruleGroupColumn;
  CollectEmailConfigTypeEnum type;
  Long absenceInterval;
  Long alertRepeatInterval;
  String serviceId;
  String serviceName;
  String applicationId;
  String applicationName;
  String recipient;
  Long priorityConfigId;
  CollectEmailAlertContentTypeEnum contentType;
  String content;
  String contentValue;
  boolean active;
}
