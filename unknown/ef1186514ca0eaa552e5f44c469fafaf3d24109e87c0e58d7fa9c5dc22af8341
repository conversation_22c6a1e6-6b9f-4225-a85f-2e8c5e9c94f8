package vn.com.mbbank.kanban.mbmonitor.server.converter;

import com.alibaba.fastjson2.JSON;
import jakarta.persistence.AttributeConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleConverterUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

/**
 * RuleGroupConverter.
 */
public class RuleGroupConverter implements AttributeConverter<RuleGroupType, String> {

  private final Logger logger = LoggerFactory.getLogger(RuleGroupConverter.class);

  /**
   * convert RuleGroupType to String.
   *
   * @param ruleGroupType ruleGroupType
   * @return string.
   */
  @Override
  public String convertToDatabaseColumn(RuleGroupType ruleGroupType) {
    return JSON.toJSONString(ruleGroupType);
  }

  /**
   * convert String to RuleGroupType.
   *
   * @param dbData the string mapping field
   * @return RuleGroupType.
   */
  @Override
  public RuleGroupType convertToEntityAttribute(String dbData) {
    try {
      return RuleConverterUtils.convertStringToRuleGroupType(dbData);
    } catch (Exception e) {
      logger.warn("Convert to RuleGroupType error fallback to null", e);
      return null;
    }
  }
}
