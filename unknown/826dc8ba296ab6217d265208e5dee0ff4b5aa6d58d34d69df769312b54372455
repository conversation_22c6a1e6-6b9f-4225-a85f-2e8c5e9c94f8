package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * KeyValueResponse.
 */
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExecutionKeyValue {
  String key;
  String value;
  String description;
  boolean enable;
  boolean autoGenerated;
}
