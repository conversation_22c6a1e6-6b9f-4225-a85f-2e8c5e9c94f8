package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.Date;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskShiftEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskTimeTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;

/**
 * Model request task.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TaskRequest {
  Long id;

  @Size(max = CommonConstants.COMMON_NAME_MAX_LENGTH, message = "Name exceeds {max} characters")
  String name;

  @Size(max = CommonConstants.COMMON_DESCRIPTION_MAX_LENGTH, message = "Description exceeds {max} characters")
  String description;

  @NotNull
  TaskTypeEnum type;

  Date startTime;

  Date endTime;

  TaskTimeTypeEnum timeType;

  TaskShiftEnum shift;

  List<String> handoverUsers;

  List<Long> taskIds;

}