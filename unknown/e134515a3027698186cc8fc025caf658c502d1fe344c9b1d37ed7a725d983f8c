package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.AlertRequestRejectRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.AlertRequestRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.AlertRequestResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertRequestService;

/**
 * Controller logic alert request.
 */
@RestController
@RequestMapping(ServerUrl.ALERT_REQUEST_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AlertRequestController extends BaseController {
  AlertRequestService alertRequestService;

  /**
   * Finds all alert request.
   *
   * @param paginationRequest the pagination request
   * @return a paginated list of AlertRequestResponse
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ALERT_REQUEST, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  public ResponseData<Page<AlertRequestResponse>> findAll(
      @ModelAttribute PaginationRequest paginationRequest) {
    return ResponseUtils.success(alertRequestService.findAll(paginationRequest));
  }

  /**
   * Create or update alert request.
   *
   * @param request request
   * @return new alert request AlertRequestResponse
   */
  @PostMapping
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ALERT_REQUEST, action = PermissionActionEnum.VIEW)
  })
  public ResponseData<AlertRequestResponse> createOrUpdate(
      @RequestBody @Valid AlertRequestRequest request
  ) throws BusinessException {
    return ResponseUtils.success(alertRequestService.createOrUpdate(request));
  }

  /**
   * Find By id.
   *
   * @param id id
   * @return data
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ALERT_REQUEST, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_REQUEST, action = PermissionActionEnum.APPROVE)
  })
  @GetMapping("/{id}")
  public ResponseData<AlertRequestResponse> findById(@PathVariable String id) throws Exception {
    return ResponseUtils.success(alertRequestService.findWithId(id));
  }

  /**
   * Approve alert request by id.
   *
   * @param request request
   * @return Approved alert request
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ALERT_REQUEST, action = PermissionActionEnum.APPROVE)
  })
  @Operation(summary = "Approve alert request")
  @PostMapping("/approve")
  public ResponseData<AlertRequestResponse> approve(
      @RequestBody @Valid AlertRequestRequest request
  ) throws BusinessException {
    return ResponseUtils.success(alertRequestService.approve(request));
  }

  /**
   * Reject alert request by id.
   *
   * @param request AlertRequestRejectRequest
   * @return Object
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ALERT_REQUEST, action = PermissionActionEnum.APPROVE)
  })
  @Operation(summary = "Reject alert request by id")
  @PostMapping("/reject")
  public ResponseData<String> rejectById(@RequestBody AlertRequestRejectRequest request) throws BusinessException {
    alertRequestService.rejectById(request.getId(), request.getRejectedReason());
    return ResponseUtils.success("OK");
  }

  /**
   * Delete by Id.
   *
   * @param id id
   * @return Object
   */
  @Operation(summary = "Delete alert request by id")
  @DeleteMapping("/{id}")
  public ResponseData<String> deleteById(@PathVariable String id) throws BusinessException {
    alertRequestService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }

}
