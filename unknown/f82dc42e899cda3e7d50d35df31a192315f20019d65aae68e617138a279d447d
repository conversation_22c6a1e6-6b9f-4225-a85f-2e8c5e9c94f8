package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution;

import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;

/**
 * KeyValueModel.
 */
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExecutionKeyValueRequest {
  @Size(max = CommonConstants.EXECUTION_API_KEY_VALUE_MAX_LENGTH)
  String key;
  @Size(max = CommonConstants.EXECUTION_API_KEY_VALUE_MAX_LENGTH)
  String value;
  @Size(max = CommonConstants.EXECUTION_API_DESCRIPTION_MAX_LENGTH)
  String description;
  boolean enable;
}
