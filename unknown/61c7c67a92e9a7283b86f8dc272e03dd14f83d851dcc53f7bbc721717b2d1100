package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import io.swagger.v3.oas.annotations.Operation;
import java.util.Collections;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.DatabaseThresholdConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.DatabaseThresholdConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.DatabaseThresholdConfigService;

/**
 * Controller logic database threshold config.
 */
@RestController
@RequestMapping(ServerUrl.DATABASE_THRESHOLD_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DatabaseThresholdConfigController extends BaseController {
  DatabaseThresholdConfigService databaseThresholdConfigService;

  /**
   * Finds all database threshold config.
   *
   * @param paginationRequest the pagination request
   * @return a paginated list of CollectEmailConfigResponse
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  public ResponseData<Page<DatabaseThresholdConfigResponse>> findAll(
      @ModelAttribute PaginationRequest paginationRequest) {
    return ResponseUtils.success(databaseThresholdConfigService.findAll(paginationRequest));
  }

  /**
   * Create or update database threshold config.
   *
   * @param request request
   * @return new database threshold config DatabaseThresholdConfigResponse
   */

  @PostMapping
  public ResponseData<DatabaseThresholdConfigResponse> createOrUpdate(
      @RequestBody DatabaseThresholdConfigRequest request
  ) throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(request.getId()));
    return ResponseUtils.success(databaseThresholdConfigService.createOrUpdate(request));
  }

  /**
   * Find By id.
   *
   * @param id id
   * @return data
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping("/{id}")
  public ResponseData<DatabaseThresholdConfigResponse> findById(@PathVariable String id) throws Exception {
    return ResponseUtils.success(databaseThresholdConfigService.findWithId(id));
  }

  /**
   * Delete by Id.
   *
   * @param id id
   * @return Object
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, action = PermissionActionEnum.DELETE)
  })
  @Operation(summary = "Delete database connection by id")
  @DeleteMapping("/{id}")
  public ResponseData<String> deleteById(@PathVariable String id) throws BusinessException {
    databaseThresholdConfigService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }

  /**
   * Active database connection config by id.
   *
   * @param id id
   * @return Object
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, action = PermissionActionEnum.EDIT),
  })
  @PutMapping("/{id}/toggle-status")
  public ResponseData<DatabaseThresholdConfigResponse> active(@PathVariable String id)
      throws BusinessException {
    return ResponseUtils.success(databaseThresholdConfigService.updateStatus(id));
  }
}
