package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;

/**
 * Model view attribute to filter in filter alert config .
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FilterAlertPaginationRequest extends PaginationRequestDTO {

}
