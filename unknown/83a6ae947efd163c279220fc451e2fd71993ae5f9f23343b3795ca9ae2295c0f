package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AttributeInfoDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportDataModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.AlertPaginationRequest;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 09/26/2024
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExportFileAlertRequest {
  @NotNull(message = "List attribute info cannot be empty.!")
  private List<AttributeInfoDto> attributes;
  private List<String> title;
  @Valid
  private AlertPaginationRequest paginationRequest;
  @Valid
  private ExportDataModel exportDataModel;
  @Max(value = CommonConstants.MAX_SIZE_FILE_RECORD_EXPORT)
  private Long numberOfResults;
}
