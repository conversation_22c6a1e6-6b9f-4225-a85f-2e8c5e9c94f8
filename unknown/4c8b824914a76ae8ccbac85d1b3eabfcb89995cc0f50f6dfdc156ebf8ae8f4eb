package vn.com.mbbank.kanban.mbmonitor.server.dtos.model;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * EmailPartnerModel.
 */
@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailPartnerModel {
  Long id;
  String name;
  List<String> addresses;
}
