package vn.com.mbbank.kanban.mbmonitor.server.dtos.model;

import static vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants.EMAIL_RECEIVER_MAX_LENGTH;
import static vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants.EMAIL_SUBJECT_MAX_LENGTH;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailPartnerRequest;

/**
 * EmailComposedModel.
 */
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailComposedModel {
  @Valid
  List<EmailPartnerRequest> partners;

  @NotBlank
  @Size(max = EMAIL_SUBJECT_MAX_LENGTH, message = "Subject should be at most "
      + EMAIL_SUBJECT_MAX_LENGTH + " characters")
  String subject;

  @Builder.Default
  Boolean isOneEmail = false;

  @NotBlank
  @Email(message = "Invalid email format for emailSender")
  String emailSender;

  List<FileStorageModel> fileStorages;

  @Size(max = EMAIL_RECEIVER_MAX_LENGTH, message = "To field cannot contain more than "
      + EMAIL_RECEIVER_MAX_LENGTH + " email addresses")
  List<String> to;

  @Size(max = EMAIL_RECEIVER_MAX_LENGTH, message = "CC field cannot contain more than "
      + EMAIL_RECEIVER_MAX_LENGTH + " email addresses")
  List<String> cc;

  @NotBlank(message = "Content cannot be empty")
  String content;
}
