package vn.com.mbbank.kanban.mbmonitor.server.controllers;


import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ExportDataResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ExportFileServiceRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ServicePaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ServiceRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ServiceDependenciesResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ServiceWithPriorityResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;

/**
 * Controller logic service.
 */
@RestController
@RequestMapping(ServerUrl.SERVICE_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ServiceController extends BaseController {
  ServiceService serviceService;
  ServiceDependencyService serviceDependencyService;

  /**
   * find all service by paginationRequest.
   *
   * @param paginationRequest Pagination parameters for the request.
   * @return ResponseEntity containing the response data with HTTP status OK.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.SERVICE_MANAGEMENT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.APPLICATION_MANAGEMENT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.WEBHOOK_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.EMAIL_COLLECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_CONNECTION, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_COLLECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_GROUP_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.REPORT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.FILTER_ALERT_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_REQUEST, action = PermissionActionEnum.APPROVE),
      @AclPermission(module = PermissionModuleEnum.MODIFY_ALERT_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.AUTO_TRIGGER_ACTION_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MONITOR_WEB_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_REQUEST, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  public ResponseData<Page<ServiceEntity>> findAll(
      @ModelAttribute ServicePaginationRequest paginationRequest) {
    return ResponseUtils.success(serviceService.findWithPaging(paginationRequest));
  }

  /**
   * find all collect email config by serviceId.
   *
   * @param id serviceId
   * @return list alert group config
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.SERVICE_MANAGEMENT, action = PermissionActionEnum.EDIT),
      @AclPermission(module = PermissionModuleEnum.SERVICE_MANAGEMENT, action = PermissionActionEnum.DELETE),
  })
  @Operation(summary = "Find all alert group configs by serviceId")
  @GetMapping("{id}/dependencies")
  public ResponseData<ServiceDependenciesResponse> findAllDependenciesById(
      @PathVariable String id) {
    return ResponseUtils.success(serviceDependencyService.findAllDependenciesById(id));
  }

  /**
   * find service by alert status.
   *
   * @param alertGroupStatus alertGroupStatus
   * @return Result list service
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MONITOR_ALERT, action = PermissionActionEnum.VIEW),
  })
  @GetMapping("/with-priority")
  public ResponseData<List<ServiceWithPriorityResponse>> findServiceWithPriorityByAlertGroupStatus(
      @RequestParam AlertGroupStatusEnum alertGroupStatus) {
    return ResponseUtils.success(
        serviceService.findServiceWithPriorityByAlertGroupStatus(alertGroupStatus));
  }

  /**
   * Find By Id.
   *
   * @param id id
   * @return data
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.SERVICE_MANAGEMENT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.APPLICATION_MANAGEMENT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.WEBHOOK_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.EMAIL_COLLECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_CONNECTION, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_COLLECT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_GROUP_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.FILTER_ALERT_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MODIFY_ALERT_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.AUTO_TRIGGER_ACTION_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.MONITOR_WEB_CONFIG, action = PermissionActionEnum.VIEW),
  })
  @GetMapping("{id}")
  public ResponseData<ServiceEntity> findById(@PathVariable String id) {
    return ResponseUtils.success(serviceService.findById(id));
  }

  /**
   * Create or update service.
   *
   * @param serviceRequest ServiceRequest
   * @return new service ServiceEntity
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.SERVICE_MANAGEMENT, action = PermissionActionEnum.CREATE),
      @AclPermission(module = PermissionModuleEnum.SERVICE_MANAGEMENT, action = PermissionActionEnum.EDIT)
  })
  @PostMapping
  public ResponseData<ServiceEntity> createOrUpdate(
      @RequestBody @Valid ServiceRequest serviceRequest
  ) throws BusinessException {
    return ResponseUtils.success(serviceService.createOrUpdate(serviceRequest));
  }

  /**
   * Delete by Id.
   *
   * @param id id
   * @return Object
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.SERVICE_MANAGEMENT, action = PermissionActionEnum.DELETE)
  })
  @Operation(summary = "Delete service by id")
  @DeleteMapping("/{id}")
  public ResponseData<String> deleteById(@PathVariable String id) throws BusinessException {
    serviceDependencyService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }

  /**
   * API to export a file as a stream based on the provided request data.
   *
   * @param request The request object containing the necessary data for file export.
   *                The request is validated using the {@link Valid} annotation.
   * @return StreamingResponseBody A response entity containing the file to be exported as a stream.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.SERVICE_MANAGEMENT, action = PermissionActionEnum.EXPORT)
  })
  @PostMapping(value = "/export")
  public ResponseData<ExportDataResponse> export(
      @RequestBody @Valid ExportFileServiceRequest request)
      throws IOException, BusinessException {
    return ResponseUtils.success(serviceService.exportFile(request));
  }
}
