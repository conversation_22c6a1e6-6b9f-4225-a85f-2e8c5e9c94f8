#!/usr/bin/env groovy
library('cicd-shared-lib-dev@tanzu') _

import java.text.SimpleDateFormat

node("${NODE_INTEGRATION_TEAM_MGT}") {
    // =================================================================================
    /**
     * TODO: Chi thay doi 2 thong tin tren tuong ung voi project
     *
     * project_name: Name of project
     * kubernetes_namespace: namespace of K8S
     */
    def project_name = "mbmonitor-server"
    def kubernetes_namespace = "uat-mbmonitor"
    
    // ===================================================================================
    def image_version = "1.0.1"
    def chart_version = "0.1.${BUILD_NUMBER}"
    def environment = "L01"
    def harbor_host = vault path: 'cicd/harbor', key: 'harbor_tanzu_dev', credentialsId: 'vault-token-dev', engineVersion: "2"
    def helm_template = vault path: 'cicd/helm', key: 'helm_template_common', credentialsId: 'vault-token-dev', engineVersion: "2"
    def helm_host_argocd = "argocd-helm-tanzu-dev-L01"
    def docker_prefix = "${harbor_host}/${kubernetes_namespace}"
    def mvnhome = "/sharedata/jenkin-home/tools/apache-maven-3.6.2"
    def jdkhome = "/sharedata/jenkin-home/tools/jdk-17"
    def secret_key = "${project_name}"
    def secret_value =  sh(script: "echo -n '${secret_key}' | sha1sum | head -c 40", returnStdout: true)
    // ===================================================================================
    
    /**
     * TODO: Chon luong deploy
     * Deploy theo luong argocd : CICD-ARGOCD
     * Deploy theo luong helm : CICD-HELM
        môi trường intenal : sub_path = "dev"
        môi trường aws: sub_path = "aws-dev"
        môi trường azure : sub_path = "azure-dev"
        môi trường tanzu : sub_path = "tanzu-dev"
     */
    def Options = "CICD-HELM"
    def sub_path = "tanzu-uat"

    // ===================================================================================
    
    ci_common(project_name, docker_prefix, image_version, harbor_host, helm_template, Options, sub_path)
    try {
        def arrProject = environment.split(' ')
        def stages = [failFast: true]
        if(arrProject.length > 0 ){
            for (int i = 0; i < arrProject.length; i++) {
                def env = arrProject[i];
                switch(env) {
                    case "L01":
                        helm_host = "${helm_host_argocd}"
                        /*
                        * TODO : Truy thong tin cum
                        **** uat <=> uat-tanzu01
                        */
                        env_argocd = "uat"
                        break
                    }
                stage("Start ${arrProject[i]} Environment"){
                    script {
                        service_account_common(project_name, env, secret_key, secret_value, sub_path)
                        helm_pack_common(project_name, env, helm_host, helm_template, docker_prefix, image_version, Options, chart_version, sub_path) 
                        cd_common(project_name, helm_host, arrProject, stages, docker_prefix, image_version, env, Options, kubernetes_namespace, env_argocd, chart_version) }
                }
            }
        }
    } catch (Exception ex) {
        currentBuild.result = "Failed"
        throw ex
    }
}

def ci_common(def project_name, def docker_prefix, def image_version, def harbor_host, def helm_template, def Options, def sub_path) {
    def mvnhome = "/sharedata/jenkin-home/tools/apache-maven-3.6.3"
    def jdkhome = "/sharedata/jenkin-home/tools/jdk-17"
    stage(' Git Clone ') {
        script {
            ci_common_java()
            sh "echo gitlabMergeRequestIid ${env.CHANGE_URL}"
            git_command("git submodule update --init")
        }
    }
    stage(' Maven build ') {
        env.JAVA_HOME = "${jdkhome}"
        env.PATH = "${mvnhome}/bin:${env.JAVA_HOME}/bin:${env.PATH}"
        sh"mvn -B clean install -DskipTests"
        sh "mvn dependency:tree"
        // script { maven_build() }
    }
    stage(' Docker Build ') {
        script { docker_build_push(project_name,docker_prefix,image_version,harbor_host,sub_path) }
    }
}

