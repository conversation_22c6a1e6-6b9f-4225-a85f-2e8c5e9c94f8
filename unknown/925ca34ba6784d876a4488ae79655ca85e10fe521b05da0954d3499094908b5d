package vn.com.mbbank.kanban.mbmonitor.common.configs;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for S3Properties bean.
 * This bean is only initialized when all required S3 properties are present
 * in the application configuration.
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "mbmonitor.s3")
public class S3Properties {
  private String accessKey;
  private String secretKey;
  private String endpoint;
  private String bucketName;
  private String region;
}
