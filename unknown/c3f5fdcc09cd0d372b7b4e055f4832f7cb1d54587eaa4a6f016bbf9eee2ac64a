package vn.com.mbbank.kanban.mbmonitor.server.constants;

/**
 * Constants related to Sentinel configuration and monitoring.
 */
public class SentinelConstants {

  private SentinelConstants() {
  }

  /**
   * Metric name for Sentinel circuit breaker monitoring.
   */
  public static final String SENTINEL_CB_MONITORING = "sentinel_circuit_breaker_monitoring";

  /**
   * Description for Sentinel circuit breaker state tracking.
   */
  public static final String SENTINEL_CB_STATE_TRACKING = "Sentinel Circuit Breaker State Tracking";

  /**
   * Type of state change observer used in Sentinel.
   */
  public static final String TYPE_STATE_CHANGE_OBSERVER = "logging";

  /**
   * Error message for Sentinel circuit breaker state monitoring errors.
   */
  public static final String MONITOR_ERROR = "CircuitBreakerState Sentinel Monitoring Error";

  /**
   * Error message for errors occurring during registration of state change observers in Sentinel.
   */
  public static final String REGISTER_ERROR =
      "registerStateChangeObserver Sentinel Monitoring Error";
}