package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;

/**
 * Model request service to create or update service.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ServiceRequest {
  String id;

  @NotBlank
  @Size(max = CommonConstants.SERVICE_NAME_MAX_LENGTH, message = "Service name exceeds 100 characters")
  @Size(min = 1, message = "Service name can not empty")
  String name;
  @Size(max = CommonConstants.SERVICE_DESCRIPTION_MAX_LENGTH, message = "Service description exceeds 300 characters")
  String description;
}
