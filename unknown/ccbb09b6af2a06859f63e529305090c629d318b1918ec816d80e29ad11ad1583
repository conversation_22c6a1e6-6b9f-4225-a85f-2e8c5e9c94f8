package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution;

import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecutionKeyValue;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionBodyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionContentTypeEnum;

/**
 * BodyApiModel.
 */
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BodyApiRequest {
  ExecutionBodyTypeEnum bodyType;
  String bodyRaw;
  @Builder.Default
  List<ExecutionKeyValue> formUrlEncoded = new ArrayList<>();
  ExecutionContentTypeEnum contentType;
}
