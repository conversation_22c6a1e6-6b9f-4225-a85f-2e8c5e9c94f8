### <PERSON>ra link
Provide link to <PERSON><PERSON> (US, Issue)

### Merge type (check one)
- [ ] Feature
- [ ] Fix bug
- [ ] Other 

### Status (check one)
- [ ] Done
- [ ] In-progress

### Merge request checklists (check all)
- [ ] The code compiles
- [ ] Naming Conventions (classes, constants, variables, methods (void vs return), etc)
- [ ] No hard-coded variables
- [ ] The code is easy to read (Readability)
- [ ] Avoid duplicate code
- [ ] Restful API naming
- [ ] Autowired depedencies is correct.
- [ ] No error Checkstyles
- [ ] Pass 100% Unit test
- [ ] Unit test 100% Coverage
