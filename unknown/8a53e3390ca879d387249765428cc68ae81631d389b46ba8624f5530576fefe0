package vn.com.mbbank.kanban.mbmonitor.server.constants;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/07/2024
 */
public class MessageConstants {

  public static final String CUSTOM_OBJECT_NAME_MAX_LENGTH_MESSAGE_ERROR =
      "Custom object name must be between 1 and " + CommonConstants.CUSTOM_OBJECT_NAME_MAX_LENGTH
          +
          " characters";

  public static final String CUSTOM_OBJECT_NAME_REGEX_MESSAGE_ERROR =
      "Custom object name can only contain A-Z, a-z, 0-9, . -  _";

  public static final String CUSTOM_OBJECT_DESCRIPTION_MAX_LENGTH_MESSAGE_ERROR =
      "Custom object description exceeds " + CommonConstants.CUSTOM_OBJECT_DESCRIPTION_MAX_LENGTH
          +
          " characters";

  public static final String CUSTOM_OBJECT_DESCRIPTION_REGEX_MESSAGE_ERROR =
      "Custom object description can only contain A-Z, a-z, 0-9, . -  _";

  public static final String CUSTOM_OBJECT_REGEX_MAX_LENGTH_MESSAGE_ERROR =
      "Custom object regex exceeds " + CommonConstants.CUSTOM_OBJECT_REGEX_MAX_LENGTH
          +
          " characters";

  public static final String CUSTOM_OBJECT_FROM_INDEX_MAX_LENGTH_MESSAGE_ERROR =
      "Custom object from index must be less than or equals "
          +
          CommonConstants.CUSTOM_OBJECT_INDEX_MAX_LENGTH;

  public static final String CUSTOM_OBJECT_TO_INDEX_MAX_LENGTH_MESSAGE_ERROR =
      "Custom object to index must be less than or equals "
          +
          CommonConstants.CUSTOM_OBJECT_INDEX_MAX_LENGTH;

  public static final String CUSTOM_OBJECT_FROM_KEYWORD_MAX_LENGTH_MESSAGE_ERROR =
      "Custom object from keyword must be less than or equals "
          +
          CommonConstants.CUSTOM_OBJECT_KEYWORD_MAX_LENGTH;

  public static final String CUSTOM_OBJECT_TO_KEYWORD_MAX_LENGTH_MESSAGE_ERROR =
      "Custom object to keyword must be less than or equals "
          +
          CommonConstants.CUSTOM_OBJECT_KEYWORD_MAX_LENGTH;
}
