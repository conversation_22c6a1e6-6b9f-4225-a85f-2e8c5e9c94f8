package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskTypeEnum;

/**
 * Model search request task.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TaskSearchRequest {
  List<TaskTypeEnum> types;
  List<TaskStatusEnum> statuses;
  List<String> creatorUsers;
  List<String> assigneeUsers;
  List<DateRangeRequest> dateRanges;
  String search;
}