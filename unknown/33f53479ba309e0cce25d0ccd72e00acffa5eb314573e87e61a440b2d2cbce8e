package vn.com.mbbank.kanban.mbmonitor.server.configs.circuitbreaker;

import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;

/**
 * This class represents the state of a circuit breaker, extending the {@link DegradeRule} class from Sentinel.
 * It includes additional fields to track the state and name of the circuit breaker.
 */
public class CircuitBreakerState extends DegradeRule {

  /**
   * The current state of the circuit breaker.
   * This value can represent different states:
   * <ul>
   *   <li>0.0 - CLOSED: The circuit breaker is closed and requests are allowed to pass.</li>
   *   <li>1.0 - HALF_OPEN: The circuit breaker is half-open and is testing
   *   if requests can be allowed to pass again.</li>
   *   <li>2.0 - OPEN: The circuit breaker is open and requests are blocked.</li>
   * </ul>
   */
  private double state;

  /**
   * The name of the circuit breaker.
   */
  private String name;

  /**
   * Default constructor.
   */
  public CircuitBreakerState() {
  }

  /**
   * Gets the current state of the circuit breaker.
   *
   * @return the current state of the circuit breaker.
   */
  public double getState() {
    return state;
  }

  /**
   * Sets the current state of the circuit breaker.
   *
   * @param state the new state of the circuit breaker.
   */
  public void setState(double state) {
    this.state = state;
  }

  /**
   * Gets the name of the circuit breaker.
   *
   * @return the name of the circuit breaker.
   */
  public String getName() {
    return name;
  }

  /**
   * Sets the name of the circuit breaker.
   *
   * @param name the new name of the circuit breaker.
   */
  public void setName(String name) {
    this.name = name;
  }
}