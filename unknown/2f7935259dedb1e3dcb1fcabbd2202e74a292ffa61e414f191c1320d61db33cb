package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.VariableRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.VariableResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.VariableResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.VariableService;

/**
 * VariableController.
 */
@RestController
@RequestMapping(ServerUrl.VARIABLE_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class VariableController extends BaseController {

  private final VariableService variableService;
  private final VariableResponseMapper variableResponseMapper =
      VariableResponseMapper.INSTANCE;

  /**
   * Api find config by ID.
   *
   * @param id id of config.
   * @return VariableResponse.
   */
  @GetMapping(value = "/{id}")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.VARIABLE, action = PermissionActionEnum.VIEW),
  })
  ResponseData<VariableResponse> findById(@PathVariable String id)
      throws BusinessException {
    return ResponseUtils.success(variableResponseMapper.map(variableService.findWithId(id)));
  }

  /**
   * Api find a list of config.
   *
   * @param request search
   * @return List of VariableResponse
   */
  @GetMapping(value = "/with-paging")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.VARIABLE, action = PermissionActionEnum.VIEW),
  })
  ResponseData<Page<VariableResponse>> findAllWithPaging(@ModelAttribute PaginationRequest request) {
    return ResponseUtils.success(variableService.findAll(request).map(variableResponseMapper::map));
  }

  /**
   * Api find a list of config.
   *
   * @return List of VariableResponse
   */
  @GetMapping
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EXECUTION, action = PermissionActionEnum.EDIT),
      @AclPermission(module = PermissionModuleEnum.EXECUTION, action = PermissionActionEnum.CREATE),
      @AclPermission(module = PermissionModuleEnum.EXECUTION, action = PermissionActionEnum.VIEW),
  })
  ResponseData<List<VariableResponse>> findAll() {
    return ResponseUtils.success(variableResponseMapper.map(variableService.findAll()));
  }

  /**
   * Api save config.
   *
   * @param request input data.
   * @return VariableResponse.
   * @throws BusinessException exception
   */
  @PostMapping
  ResponseData<VariableResponse> save(
      @Valid @RequestBody VariableRequest request) throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.VARIABLE, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(request.getId()));
    return ResponseUtils.success(variableResponseMapper.map(variableService.createOrUpdate(request)));
  }

  /**
   * Api delete config.
   *
   * @param id config id.
   * @return String
   */
  @DeleteMapping(value = "/{id}")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.VARIABLE, action = PermissionActionEnum.DELETE)
  })
  ResponseData<String> deleteById(@PathVariable String id) throws BusinessException {
    variableService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }
}
