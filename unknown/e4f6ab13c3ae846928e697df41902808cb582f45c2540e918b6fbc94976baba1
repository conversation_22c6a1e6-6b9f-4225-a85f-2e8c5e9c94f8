package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertGroupSearchRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AlertGroupResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AlertResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CloseAlertGroupsResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.AlertResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertGroupService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertService;

/**
 * Controller logic alert.
 */
@RestController
@RequestMapping(ServerUrl.ALERT_GROUP_URL)
@RequiredArgsConstructor
public class AlertGroupController extends BaseController {
  private final AlertGroupService alertGroupService;
  private final AlertService alertService;
  private final AlertResponseMapper alertResponseMapper = AlertResponseMapper.INSTANCE;

  /**
   * find all alert by paginationRequest and post method.
   *
   * @param searchRequest Pagination parameters for the request.
   * @param request       HttpServletRequest.
   * @return ResponseEntity containing the response data with HTTP status OK.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MONITOR_ALERT, action = PermissionActionEnum.VIEW),
  })
  @PostMapping("/search")
  public ResponseData<Page<AlertGroupResponse>> findAllByPostMethod(
      HttpServletRequest request, @RequestBody AlertGroupSearchRequest searchRequest) {
    return ResponseUtils.success(alertGroupService.findAll(searchRequest, getPaging(request)));
  }

  /**
   * close alert.
   *
   * @param alertGroupIds alertIds
   * @return success or not
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MONITOR_ALERT, action = PermissionActionEnum.ACKNOWLEDGE)
  })
  @PutMapping("/close")
  public ResponseData<CloseAlertGroupsResponse> close(
      @RequestParam(value = "alertGroupIds[]") List<Long> alertGroupIds) {
    return ResponseUtils.success(alertGroupService.close(alertGroupIds));
  }

  /**
   * find alert recipient by alert status.
   *
   * @param alertGroupStatus status of alertGroup
   * @return list of recipients
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MONITOR_ALERT, action = PermissionActionEnum.VIEW)
  })
  @GetMapping("/recipients")
  public ResponseData<List<String>> findAlertRecipientByAlertGroupStatus(
      @RequestParam AlertGroupStatusEnum alertGroupStatus) {
    return ResponseUtils.success(
        alertGroupService.findAlertRecipientByAlertGroupStatus(alertGroupStatus));
  }

  /**
   * find alert by alert group config.
   *
   * @param alertGroupIds            alert group config
   * @param searchRequest search request
   * @return list of alerts
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MONITOR_ALERT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ALERT_GROUP_CONFIG, action = PermissionActionEnum.EDIT),
      @AclPermission(module = PermissionModuleEnum.ALERT_GROUP_CONFIG, action = PermissionActionEnum.VIEW),
  })
  @GetMapping("/alerts")
  public ResponseData<Page<AlertResponse>> findAllByAlertGroupIdIn(
      @RequestParam(value = "alertGroupIds[]") List<Long> alertGroupIds,
      @ModelAttribute PaginationRequest searchRequest) {
    return ResponseUtils.success(
        alertService.findAllByAlertGroupIdIn(alertGroupIds, searchRequest).map(alertResponseMapper::map));
  }
}
