package vn.com.mbbank.kanban.mbmonitor.server.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Information of Author.
 * Author: hieult
 */
public class StringConverter {
  private StringConverter() {
  }

  /**
   * This method handle convert string mapping to Map has key is id of field, value is stt.
   *
   * @param mappingFields the string mapping field
   * @return Map has key is id of field, value is stt.
   */
  public static Map<Long, Integer> convertStringToMap(String mappingFields)
      throws JsonProcessingException {
    ObjectMapper objectMapper = new ObjectMapper();
    Map<Long, Integer> resultMap =
        objectMapper.readValue(mappingFields, new TypeReference<Map<Long, Integer>>() {
        });
    resultMap.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue() < 0);
    return resultMap;
  }

  /**
   * This method handle convert string list row selected to list.
   *
   * @param json the string list row selected
   * @return list row selected .
   */
  public static List<Integer> convertStringToListRowSelected(String json)
      throws JsonProcessingException {
    if (json.equals("")) {
      return new ArrayList<>();
    }
    ObjectMapper objectMapper = new ObjectMapper();
    List<Integer> numberList =
        objectMapper.readValue(json, new TypeReference<List<Integer>>() {
        });
    return numberList;
  }
}
