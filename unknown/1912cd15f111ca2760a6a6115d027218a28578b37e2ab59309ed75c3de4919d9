{"pod": "${hostName}", "localIp": "${sys:localIpValue:-}", "timestamp": {"$resolver": "timestamp", "pattern": {"format": "yyyy-MM-dd'T'HH:mm:ss.SSS", "timeZone": "UTC"}}, "thread": {"$resolver": "thread", "field": "name"}, "level": {"$resolver": "level", "field": "name"}, "loggerName": {"$resolver": "logger", "field": "name"}, "loggerFqcn": {"$resolver": "logger", "field": "fqcn"}, "fullData": {"$resolver": "message", "stringified": true}, "contextMap": {"$resolver": "mdc"}, "exception": {"exception_class": {"$resolver": "exception", "field": "className"}, "exception_message": {"$resolver": "exception", "field": "message"}, "stacktrace": {"$resolver": "exception", "field": "stackTrace", "stackTrace": {"stringified": true}}}, "source": {"class": {"$resolver": "source", "field": "className"}, "method": {"$resolver": "source", "field": "methodName"}, "file": {"$resolver": "source", "field": "fileName"}, "line": {"$resolver": "source", "field": "lineNumber"}}}