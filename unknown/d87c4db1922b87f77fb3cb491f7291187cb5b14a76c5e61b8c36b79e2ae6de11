package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Comment ModifyAlertConfigUpdatePositionRequest request.
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModifyAlertConfigUpdatePositionRequest {
  @NotNull(message = "Modify Alert Config from id can not be empty")
  private Long modifyAlertConfigFromId;
  @NotNull(message = "Modify Alert Config to td can not be empty")
  private Long modifyAlertConfigToId;
  @NotNull(message = "From position can not be empty")
  private Integer fromPosition;
  @NotNull(message = "To position can not be empty")
  private Integer toPosition;
}
