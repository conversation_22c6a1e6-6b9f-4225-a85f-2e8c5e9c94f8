package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Comment PriorityConfigUpdatePositionRequest request.
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PriorityConfigUpdatePositionRequest {
  @NotNull(message = "Priority Config From Id can not be empty")
  private Long alertPriorityConfigFromId;
  @NotNull(message = "Priority Config to Id can not be empty")
  private Long alertPriorityConfigToId;
  @NotNull(message = "From position name can not be empty")
  private Integer fromPosition;
  @NotNull(message = "To position can not be empty")
  private Integer toPosition;
}
