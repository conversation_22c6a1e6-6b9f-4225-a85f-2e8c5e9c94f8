package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import java.util.Collections;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationEventRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationEventResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.services.NotificationEventService;


/**
 * Controller for notification events.
 */
@RestController
@RequestMapping(ServerUrl.NOTIFICATION_EVENT_URL)
@RequiredArgsConstructor
public class NotificationEventController extends BaseController {

  private final NotificationEventService notificationEventService;

  /**
   * Find all notification events with pagination.
   *
   * @param request Pagination request
   * @return Page of notification events
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.NOTIFICATION_EVENT, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  public ResponseData<Page<NotificationEventResponse>> findAll(
      @ModelAttribute PaginationRequest request) {
    return ResponseUtils.success(notificationEventService.findAll(request));
  }

  /**
   * Find notification event by ID.
   *
   * @param id Notification event ID
   * @return Notification event
   * @throws BusinessException if notification event not found
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.NOTIFICATION_EVENT, action = PermissionActionEnum.VIEW)
  })
  @GetMapping("/{id}")
  public ResponseData<NotificationEventResponse> findById(@PathVariable String id) throws BusinessException {
    return ResponseUtils.success(notificationEventService.findById(id));
  }

  /**
   * Create or update notification event.
   *
   * @param request Notification event request
   * @return Created or updated notification event
   * @throws BusinessException if validation fails
   */
  @PostMapping
  public ResponseData<NotificationEventResponse> createOrUpdate(
      @RequestBody NotificationEventRequest request) throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.NOTIFICATION_EVENT, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(request.getId()));
    return ResponseUtils.success(notificationEventService.createOrUpdate(request));
  }

  /**
   * Delete notification event by ID.
   *
   * @param id Notification event ID
   * @return Success message
   * @throws BusinessException if notification event not found
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.NOTIFICATION_EVENT, action = PermissionActionEnum.DELETE)
  })
  @DeleteMapping("/{id}")
  public ResponseData<String> deleteById(@PathVariable String id) throws BusinessException {
    notificationEventService.deleteById(id);
    return ResponseUtils.success("OK");
  }

  /**
   * Toggle active status of notification event.
   *
   * @param id Notification event ID
   * @return Success message
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.NOTIFICATION_EVENT, action = PermissionActionEnum.EDIT)
  })
  @PutMapping("/{id}/toggle-status")
  public ResponseData<String> updateActive(@PathVariable String id) throws BusinessException {
    notificationEventService.toggleStatus(id);
    return ResponseUtils.success("OK");
  }
}