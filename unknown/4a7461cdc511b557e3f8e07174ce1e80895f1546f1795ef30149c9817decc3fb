package vn.com.mbbank.kanban.mbmonitor.server.dtos.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.com.mbbank.kanban.mbmonitor.common.enums.RawValueConfigTypeEnum;

/**
 * AlertPriorityConfigWithRawValue.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlertPriorityConfigWithRawValueModel {
  Long id;
  String name;
  String color;
  Integer order;
  String rawValue;
  RawValueConfigTypeEnum rawValueType;
}
