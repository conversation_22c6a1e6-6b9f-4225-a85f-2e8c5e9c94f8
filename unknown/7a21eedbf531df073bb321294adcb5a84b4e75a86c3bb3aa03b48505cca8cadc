package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.CollectEmailConfigModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.CollectEmailConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CollectEmailConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.CollectEmailConfigResponseCollectEmailConfigModelMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.CollectEmailConfigService;

/**
 * Controller logic collect email config.
 */
@RestController
@RequestMapping(ServerUrl.COLLECT_EMAIL_CONFIG_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CollectEmailConfigController extends BaseController {
  CollectEmailConfigService collectEmailConfigService;

  /**
   * Finds all collect email config.
   *
   * @param paginationRequest the pagination request
   * @return a paginated list of CollectEmailConfigResponse
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_COLLECT, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  public ResponseData<Page<CollectEmailConfigModel>> findAll(
      @ModelAttribute PaginationRequest paginationRequest) {
    return ResponseUtils.success(collectEmailConfigService.findAll(paginationRequest));
  }

  /**
   * Create or update email config.
   *
   * @param request request
   * @return new email config EmailConfigResponse
   */

  @PostMapping
  public ResponseData<CollectEmailConfigResponse> createOrUpdate(
      @RequestBody @Valid CollectEmailConfigRequest request
  ) throws BusinessException {
    List<Long> ids = new ArrayList<>();
    if (!KanbanCommonUtil.isEmpty(request.getId()) && request.getId() > 0L) {
      ids.add(request.getId());
    }
    makeSureCreateOrUpdate(PermissionModuleEnum.EMAIL_COLLECT, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, ids);
    return ResponseUtils.success(collectEmailConfigService.createOrUpdate(request));
  }

  /**
   * Find By id.
   *
   * @param id id
   * @return data
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_COLLECT, action = PermissionActionEnum.VIEW)
  })
  @GetMapping("{id}")
  public ResponseData<CollectEmailConfigResponse> findById(@PathVariable Long id) throws Exception {
    var collectEmailConfigResponse =
        CollectEmailConfigResponseCollectEmailConfigModelMapper.INSTANCE.map(
            collectEmailConfigService.findCollectEmailConfigById(id));
    return ResponseUtils.success(collectEmailConfigResponse);
  }

  /**
   * Delete by Id.
   *
   * @param id id
   * @return Object
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_COLLECT, action = PermissionActionEnum.DELETE)
  })
  @Operation(summary = "Delete email config by id")
  @DeleteMapping("/{id}")
  public ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    collectEmailConfigService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }

  /**
   * Active collect email config by id.
   *
   * @param id id
   * @return Object
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_COLLECT, action = PermissionActionEnum.EDIT),
  })
  @PutMapping("/{id}/toggle-status")
  public ResponseData<CollectEmailConfigResponse> active(@PathVariable Long id)
      throws BusinessException {
    return ResponseUtils.success(collectEmailConfigService.updateStatus(id));
  }
}
