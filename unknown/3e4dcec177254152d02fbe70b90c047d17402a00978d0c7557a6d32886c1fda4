package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TaskEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskUserTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AssignTaskRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TaskCursor;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TaskRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TaskSearchParamRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TaskSearchRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TaskAmountResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TaskResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TaskUserResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.TaskService;
import vn.com.mbbank.kanban.mbmonitor.server.services.TaskUserService;

/**
 * Controller logic Task.
 */
@RestController
@RequestMapping(ServerUrl.TASK_URL)
@RequiredArgsConstructor
public class TaskController extends BaseController {
  private final TaskService taskService;
  private final TaskUserService taskUserService;

  /**
   * create or update task.
   *
   * @param taskRequest task request.
   * @return task response.
   */
  @PostMapping
  public ResponseData<TaskResponse> createOrUpdate(@RequestBody @Valid TaskRequest taskRequest)
      throws BusinessException {
    // Update mode
    if (Objects.nonNull(taskRequest.getId())) {
      TaskEntity task = taskService.findById(taskRequest.getId());
      if (Objects.isNull(task)) {
        throw new BusinessException(ErrorCode.TASK_NOT_IS_NOT_EXISTED);
      }
      if (!getUserId().equals(task.getCreatedBy())) {
        makeSurePermissions(List.of(new AclPermissionModel(PermissionModuleEnum.TASK, PermissionActionEnum.EDIT)));
      }
    } else {
      makeSurePermissions(List.of(new AclPermissionModel(PermissionModuleEnum.TASK, PermissionActionEnum.CREATE)));
    }
    return ResponseUtils.success(taskService.createOrUpdate(taskRequest));
  }

  /**
   * find all task with paging.
   *
   * @param searchRequest search request.
   * @param requestParam  cursorRequest.
   * @return task response with paging.
   */
  @PostMapping("/search")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.TASK, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.TASK, action = PermissionActionEnum.CREATE),
      @AclPermission(module = PermissionModuleEnum.TASK, action = PermissionActionEnum.EDIT),
  })
  public ResponseData<CursorPageResponse<TaskResponse, TaskCursor>> findAll(
      @RequestBody TaskSearchRequest searchRequest,
      @ModelAttribute TaskSearchParamRequest requestParam)
      throws BusinessException {
    return ResponseUtils.success(taskService.findAllWithPaging(searchRequest, requestParam));
  }

  /**
   * find task by taskId.
   *
   * @param id taskId.
   * @return task response.
   */
  @GetMapping("/{id}")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.TASK, action = PermissionActionEnum.VIEW),
  })
  public ResponseData<TaskResponse> findById(@PathVariable Long id)
      throws BusinessException {
    return ResponseUtils.success(taskService.findByTaskId(id));
  }

  /**
   * count amount of task by search request.
   *
   * @param searchRequest searchRequest.
   * @return list amount of task and hanover task each day.
   */
  @PostMapping("/count")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.TASK, action = PermissionActionEnum.VIEW),
  })
  public ResponseData<List<TaskAmountResponse>> countTask(@RequestBody TaskSearchRequest searchRequest)
      throws BusinessException {
    return ResponseUtils.success(taskService.countTasks(searchRequest));
  }

  /**
   * delete a task.
   *
   * @param id taskId.
   * @return OK if success.
   */
  @DeleteMapping("/{id}")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.TASK, action = PermissionActionEnum.DELETE),
  })
  public ResponseData<String> deleteTask(@PathVariable Long id) throws BusinessException {
    taskService.deleteTaskById(id);
    return ResponseUtils.success("OK");
  }

  /**
   * complete a task.
   *
   * @param id taskId.
   * @return TaskResponse after complete task success.
   */
  @PostMapping("/complete")
  public ResponseData<TaskResponse> completeTask(@RequestParam(value = "id") Long id) throws BusinessException {
    TaskEntity task = taskService.findById(id);
    if (Objects.isNull(task)) {
      throw new BusinessException(ErrorCode.TASK_NOT_IS_NOT_EXISTED);
    }
    var userName = getUserId();
    if (!userName.equals(task.getCreatedBy()) && !userName.equals(task.getCurrentAssigneeUserName())) {
      makeSurePermissions(List.of(new AclPermissionModel(PermissionModuleEnum.TASK, PermissionActionEnum.EDIT)));
    }
    return ResponseUtils.success(taskService.completeTask(id));
  }

  /**
   * assign user to a task.
   *
   * @param assignTaskRequest information to add user to a task.
   * @return TaskResponse after assign user to task success.
   */
  @PostMapping("/assign")
  public ResponseData<TaskResponse> assignTask(@RequestBody @Valid AssignTaskRequest assignTaskRequest)
      throws BusinessException {
    TaskEntity task = taskService.findById(assignTaskRequest.getTaskId());
    if (Objects.isNull(task)) {
      throw new BusinessException(ErrorCode.TASK_NOT_IS_NOT_EXISTED);
    }
    if (!getUserId().equals(assignTaskRequest.getUserName())) {
      makeSurePermissions(List.of(new AclPermissionModel(PermissionModuleEnum.TASK, PermissionActionEnum.ASSIGN)));
    }
    return ResponseUtils.success(taskService.assignTask(assignTaskRequest));
  }

  /**
   * get all assignee of a task in the past.
   *
   * @param id taskId.
   * @return list of user assigned a task.
   */
  @GetMapping("{id}/histories")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.TASK, action = PermissionActionEnum.VIEW),
  })
  public ResponseData<List<TaskUserResponse>> getAssigneeHistory(@PathVariable Long id) {
    return ResponseUtils.success(taskUserService.findAllByTaskIdAndType(id, TaskUserTypeEnum.ASSIGNEE_USER));
  }
}