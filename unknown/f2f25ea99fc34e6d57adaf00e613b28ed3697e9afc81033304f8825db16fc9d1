package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ModifyAlertConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ModifyAlertConfigUpdatePositionRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ModifyAlertConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.ModifyAlertConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.ModifyAlertConfigService;

/**
 * Controller logic ModifyAlertConfigController.
 */
@RestController
@RequestMapping(ServerUrl.MODIFY_ALERT_CONFIG_URL)
@RequiredArgsConstructor
public class ModifyAlertConfigController extends BaseController {
  private final ModifyAlertConfigService modifyAlertConfigService;
  private final ModifyAlertConfigResponseMapper modifyAlertConfigResponseMapper =
      ModifyAlertConfigResponseMapper.INSTANCE;

  /**
   * Api find config by ID.
   *
   * @param id id of config.
   * @return ModifyAlertConfigResponse.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MODIFY_ALERT_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping(value = "/{id}")
  ResponseData<ModifyAlertConfigResponse> findById(@PathVariable Long id)
      throws BusinessException {
    return ResponseUtils.success(modifyAlertConfigService.findByIdWithDetail(id));
  }

  /**
   * Api find a list of config.
   *
   * @return List of ModifyAlertConfigResponse
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MODIFY_ALERT_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  ResponseData<List<ModifyAlertConfigResponse>> findAll()
      throws BusinessException {
    return ResponseUtils.success(modifyAlertConfigService.findAllWithSearch());
  }

  /**
   * Api save config.
   *
   * @param modifyAlertConfigRequest input data.
   * @return ModifyAlertConfigResponse.
   * @throws BusinessException exception
   */
  @PostMapping
  ResponseData<ModifyAlertConfigResponse> save(
      @Valid @RequestBody ModifyAlertConfigRequest modifyAlertConfigRequest) throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.MODIFY_ALERT_CONFIG, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(modifyAlertConfigRequest.getId()));
    return ResponseUtils.success(
        modifyAlertConfigResponseMapper.map(modifyAlertConfigService.save(modifyAlertConfigRequest)));
  }

  /**
   * Api delete config.
   *
   * @param id config id.
   * @return String
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MODIFY_ALERT_CONFIG, action = PermissionActionEnum.DELETE)
  })
  @DeleteMapping(value = "/{id}")
  ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    modifyAlertConfigService.deleteById(id);
    return ResponseUtils.success("OK");
  }


  /**
   * Api delete config.
   *
   * @param id     config id.
   * @param active active status
   * @return list of config
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MODIFY_ALERT_CONFIG, action = PermissionActionEnum.EDIT),
  })
  @PutMapping("/{id}")
  public ResponseData<ModifyAlertConfigResponse> updateActive(@PathVariable Long id,
                                                              @RequestParam(value = "active") Boolean active)
      throws BusinessException {
    return ResponseUtils.success(
        modifyAlertConfigResponseMapper.map(modifyAlertConfigService.updateActive(id, active)));
  }

  /**
   * Api update position.
   *
   * @param request modify alert config update info.
   * @return list of modify alert config
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MODIFY_ALERT_CONFIG, action = PermissionActionEnum.EDIT)
  })
  @PutMapping("/position")
  public ResponseData<List<ModifyAlertConfigResponse>> updatePosition(
      @RequestBody @Valid ModifyAlertConfigUpdatePositionRequest request) throws BusinessException {
    return ResponseUtils.success(modifyAlertConfigService.updatePosition(request));
  }
}
