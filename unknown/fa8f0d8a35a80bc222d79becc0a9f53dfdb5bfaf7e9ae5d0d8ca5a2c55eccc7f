package vn.com.mbbank.kanban.mbmonitor.server.controllers.core;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SysUserResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.UserWithRolesDto;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.SysUserResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.SysUserService;

/**
 * Controller logic users.
 */
@RestController
@RequestMapping(ServerUrl.USERS_URL)
@RequiredArgsConstructor
public class UsersController extends BaseController {

  private final SysUserService sysUserService;

  /**
   * get current user info.
   *
   * @return user info
   */
  @GetMapping("/me")
  public ResponseData<SysUserResponse> me() {
    return ResponseUtils.success(sysUserService.findOrCreateNew(getUserId()));

  }

  /**
   * Find detail by user.
   *
   * @param id id
   * @return user details
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.USER_MANAGEMENT, action = PermissionActionEnum.VIEW),
  })
  @GetMapping("{id}/with-roles")
  public ResponseData<SysUserResponse> findWithRolesById(@PathVariable Long id) {
    return ResponseUtils.success(sysUserService.findWithRolesById(id));
  }

  /**
   * Find all.
   *
   * @param request request
   * @param roleId  roleId
   * @return list user
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.USER_MANAGEMENT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.TASK, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.REPORT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.SYSLOG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.NOTIFICATION_EVENT, action = PermissionActionEnum.VIEW)
  })
  @GetMapping()
  public ResponseData<Page<SysUserResponse>> findAll(HttpServletRequest request,
                                                     @RequestParam(defaultValue = "0")
                                                     Long roleId) {
    var mapper = SysUserResponseMapper.INSTANCE;
    var page = getPaging(request);
    page.setPropertiesSearch(List.of("userName", "email"));
    var users = roleId > 0 ? sysUserService.findAllByRoleId(roleId, page)
        : sysUserService.findAllWithPaging(page);
    return ResponseUtils.success(mapper.map(users));
  }

  /**
   * setting user.
   *
   * @param id       id
   * @param isActive isActive
   * @param isAdmin  isAdmin
   * @return user
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.USER_MANAGEMENT, action = PermissionActionEnum.EDIT),
  })
  @PutMapping("{id}")
  public ResponseData<SysUserEntity> settingUser(@PathVariable Long id,
                                                 @RequestParam(name = "isActive", required = false)
                                                 Boolean isActive,
                                                 @RequestParam(name = "isAdmin", required = false)
                                                 Boolean isAdmin) throws BusinessException {
    return ResponseUtils.success(sysUserService.settingUser(id, isActive, isAdmin));
  }

  /**
   * Save user.
   *
   * @param body body
   * @return user
   * @throws BusinessException ex
   */
  @PostMapping
  public ResponseData<SysUserEntity> save(@RequestBody @Valid UserWithRolesDto body)
      throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.USER_MANAGEMENT, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT,
        Collections.singleton(body.getId()));
    return ResponseUtils.success(sysUserService.saveWithRoles(body));
  }

  /**
   * Delete by id.
   *
   * @param id id
   * @return OK
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.USER_MANAGEMENT, action = PermissionActionEnum.DELETE),
  })
  @DeleteMapping("{id}")
  public ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    sysUserService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }

  /**
   * Delete by batch.
   *
   * @param ids ids
   * @return String
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.USER_MANAGEMENT, action = PermissionActionEnum.DELETE),
  })
  @DeleteMapping("batch")
  public ResponseData<String> deleteByIdIn(@RequestParam(value = "ids[]") List<Long> ids) {
    sysUserService.deleteByIdIn(ids);
    return ResponseUtils.success("OK");
  }

}
