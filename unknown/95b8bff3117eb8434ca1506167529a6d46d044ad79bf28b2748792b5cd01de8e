package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysPermissionEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/23/2024
 */
@Data
public class SysRoleWithPermissionsRequest {
  private Long id;
  @NotNull
  private String name;

  private Long userId;
  private List<SysPermissionEntity> permissions;

  private String description;

  private Boolean active;
}
