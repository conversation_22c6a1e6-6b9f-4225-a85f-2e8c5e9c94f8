package vn.com.mbbank.kanban.mbmonitor.server.dtos.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WebHookConfigMapTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.server.constants.KanbanRegexContants;
import vn.com.mbbank.kanban.mbmonitor.server.enums.WebHookConfigDataTypeEnum;

/**
 * Object transfer data save config webhook.
 *
 * <AUTHOR>
 * @created_date 8/19/2024
 */
@Getter
@Setter
public class WebHookDto {

  private Long id;

  @NotNull(message = "WebHook Name is not empty.!")
  private String name;

  @NotNull(message = "Data type is not empty.!")
  private WebHookConfigDataTypeEnum dataType;

  private String token;
  @NotNull(message = "Service Name Type is not empty.!")
  private WebHookConfigMapTypeEnum serviceNameType;

  private String serviceId;

  @Pattern(
      regexp = KanbanRegexContants.JSON_PATH,
      message = "Service Name Invalid Mapping json  format"
  )
  private String serviceMapValue;

  @NotNull(message = "Application Type is not empty.!")
  private WebHookConfigMapTypeEnum applicationType;

  private String applicationId;

  @Pattern(
      regexp = KanbanRegexContants.JSON_PATH,
      message = "Application Name mapping json incorrect "
  )
  private String applicationMapValue;

  @NotNull(message = "Alert Content Type is not empty.!")
  private WebHookConfigMapTypeEnum alertContentType;

  @Size(max = CommonConstants.COMMON_CONTENT_ALERT_MAX_LENGTH)
  private String alertContentCustomValue;

  @Pattern(
      regexp = KanbanRegexContants.JSON_PATH,
      message = "Alert mapping json incorrect"
  )
  private String alertContentMapValue;

  @NotNull(message = "Priority Type Type is not empty.!")
  private WebHookConfigMapTypeEnum priorityType;

  private Long alertPriorityConfigId;

  @Pattern(
      regexp = KanbanRegexContants.JSON_PATH,
      message = "Priority mapping json incorrect"
  )
  private String priorityMapValue;

  @NotNull(message = "Contact Type Type is not empty.!")
  private WebHookConfigMapTypeEnum contactType;
  @Size(max = CommonConstants.COMMON_CONTACT_ALERT_MAX_LENGTH)
  private String contactCustomValue;

  @Pattern(
      regexp = KanbanRegexContants.JSON_PATH,
      message = "Contact mapping json incorrect"
  )
  private String contactMapValue;

}
