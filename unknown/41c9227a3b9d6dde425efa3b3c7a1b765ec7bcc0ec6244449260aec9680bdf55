package vn.com.mbbank.kanban.mbmonitor.server.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import vn.com.mbbank.kanban.mbmonitor.server.validator.EmailValidator;

/**
 * Annotation to validate an email address based on specific format and length rules.
 * This can be used on fields, method parameters, or list elements.
 */
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EmailValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.TYPE_USE})
public @interface ValidEmail {

  /**
   * The error message that will be shown when the validation fails.
   *
   * @return The error message string.
   */
  String message() default "Invalid email format";

  /**
   * Allows specifying validation groups to which this constraint belongs.
   *
   * @return The groups the constraint belongs to.
   */
  Class<?>[] groups() default {};

  /**
   * Can be used by clients of the Bean Validation API to assign custom payload objects to a constraint.
   *
   * @return Payload type.
   */
  Class<? extends Payload>[] payload() default {};
}
