package vn.com.mbbank.kanban.mbmonitor.server.services;

import java.util.List;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ExecutionDependenciesResponse;

/**
 * interface logic Execution.
 */
public interface ExecutionDependencyService extends BaseService<ExecutionEntity, String> {


  /**
   * Finds by executionGroupId.
   *
   * @param executionGroupId the ID of group
   * @return list of Execution
   */
  List<ExecutionEntity> findAllWithPermissionByExecutionGroupId(String executionGroupId)
      throws BusinessException;

  /**
   * Deletes a execution by its ID.
   *
   * @param id the ID of the execution
   * @throws BusinessException if a business-related exception occurs
   */
  void deleteWithId(String id) throws BusinessException;

  /**
   * find all dependencies by id in.
   *
   * @param id ID of execution.
   * @return a ExecutionDependenciesResponse.
   */
  ExecutionDependenciesResponse findAllDependenciesById(String id);

}