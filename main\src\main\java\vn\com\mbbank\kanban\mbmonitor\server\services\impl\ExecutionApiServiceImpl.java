package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionApiEntity;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.ExecutionApiEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ExecutionApiRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionApiService;

/**
 * Service Logic ExecutionApi.
 */
@Service
@RequiredArgsConstructor
public class ExecutionApiServiceImpl extends BaseServiceImpl<ExecutionApiEntity, String>
        implements ExecutionApiService {
  private final ExecutionApiRepository executionApiRepository;
  private final ExecutionApiEntityMapper executionApiEntityMapper = ExecutionApiEntityMapper.INSTANCE;

  @Override
  protected JpaCommonRepository<ExecutionApiEntity, String> getRepository() {
    return executionApiRepository;
  }

  @Override
  public ExecutionApiEntity findApiInfoByExecutionId(String executionId) {
    return  executionApiRepository.findAllByExecutionId(executionId);
  }

  @Override
  public void deleteApiInfoByExecutionId(String executionId) {
    executionApiRepository.deleteAllByExecutionId(executionId);
  }

  @Override
  public List<ExecutionApiEntity> findAllByExecutionIdIn(List<String> executionIds) {
    return executionApiRepository.findAllByExecutionIdIn(executionIds);
  }
}
