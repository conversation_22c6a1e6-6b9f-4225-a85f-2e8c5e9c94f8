package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.converter.FormBuilderElementListConverter;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.BaseFormBuilderElementModel;

@Entity
@Data
@Table(name = TableName.WORKFLOW_TEMPLATE)
@EqualsAndHashCode(callSuper = true)
@KanbanAutoGenerateUlId
public class WorkflowTemplateEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "NAME")
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "FORM_LAYOUT")
  @Convert(converter = FormBuilderElementListConverter.class)
  private List<BaseFormBuilderElementModel> formLayout;

  @Column(name = "WORKFLOW_ID")
  private String workflowId;

  @Override
  public String getId() {
    return id;
  }
}
