package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysRoleWithPermissionsModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysRoleEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AddRolesToUserRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.SysRoleWithPermissionsRequest;
import vn.com.mbbank.kanban.mbmonitor.server.services.SysRoleService;
import vn.com.mbbank.kanban.mbmonitor.server.services.SysUserRoleService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/24/2024
 */
@RestController
@RequestMapping(ServerUrl.ROLE_URL)
@AllArgsConstructor
public class RoleController extends BaseController {
  private final SysRoleService sysRoleService;
  private final SysUserRoleService sysUserRoleService;

  /**
   * Get all role.
   *
   * @return data
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ROLE_MANAGEMENT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.USER_MANAGEMENT, action = PermissionActionEnum.EDIT),
      @AclPermission(module = PermissionModuleEnum.NOTIFICATION_EVENT, action = PermissionActionEnum.VIEW),
  })
  @GetMapping
  public ResponseData<List<SysRoleEntity>> getAll() {
    return ResponseUtils.success(
        sysRoleService.findAll(Sort.by(Sort.Direction.DESC, "createdDate")));
  }

  /**
   * get role with all permission of role  by role id.
   *
   * @param id id
   * @return data
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ROLE_MANAGEMENT, action = PermissionActionEnum.VIEW)
  })
  @GetMapping("{id}/with-permissions")
  public ResponseData<SysRoleWithPermissionsModel> findWithPermissionById(@PathVariable Long id)
      throws BusinessException {
    return ResponseUtils.success(sysRoleService.findWithPermissionById(id));
  }

  /**
   * Save role.
   *
   * @param body body
   * @return data
   * @throws BusinessException ex
   */
  @PostMapping
  public ResponseData<String> save(@RequestBody @Valid SysRoleWithPermissionsRequest body)
      throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.ROLE_MANAGEMENT, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(body.getId()));
    sysRoleService.saveWithPermission(body);
    return ResponseUtils.success("OK");
  }

  /**
   * Delete by id.
   *
   * @param id id
   * @return data
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ROLE_MANAGEMENT, action = PermissionActionEnum.DELETE),
  })
  @DeleteMapping("/{id}")
  public ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    sysRoleService.deleteByRoleId(id);
    return ResponseUtils.success("OK");
  }

  /**
   * Delete batch.
   *
   * @param ids ids
   * @return data
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ROLE_MANAGEMENT, action = PermissionActionEnum.DELETE),
  })
  @DeleteMapping("/batch")
  public ResponseData<String> deleteBatch(@RequestParam(value = "ids[]") List<Long> ids) {
    sysRoleService.deleteByIdIn(ids);
    return ResponseUtils.success("OK");
  }

  /**
   * update status role.
   *
   * @param id       id
   * @param isActive isActive
   * @return data
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ROLE_MANAGEMENT, action = PermissionActionEnum.EDIT),
  })
  @PutMapping("/{id}/status")
  public ResponseData<String> settingStatus(@PathVariable Long id,
                                            @RequestParam(value = "active") boolean isActive) throws BusinessException {
    sysRoleService.setActiveById(id, isActive);
    return ResponseUtils.success("OK");
  }


  /**
   * Delete role of user with user id.
   *
   * @param roleId roleId
   * @param userId userId
   * @return data
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ROLE_MANAGEMENT, action = PermissionActionEnum.EDIT),
      @AclPermission(module = PermissionModuleEnum.ROLE_MANAGEMENT, action = PermissionActionEnum.DELETE),
      @AclPermission(module = PermissionModuleEnum.USER_MANAGEMENT, action = PermissionActionEnum.EDIT),
  })
  @DeleteMapping("/{roleId}/users/{userId}")
  public ResponseData<String> deleteRoleOfUser(@PathVariable Long roleId,
                                               @PathVariable Long userId) {
    sysUserRoleService.deleteAllByRoleIdAndUserId(roleId, userId);
    return ResponseUtils.success("OK");
  }

  /**
   * add roles to user.
   *
   * @param request add role to user request
   * @return string
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.ROLE_MANAGEMENT, action = PermissionActionEnum.EDIT),
      @AclPermission(module = PermissionModuleEnum.USER_MANAGEMENT, action = PermissionActionEnum.EDIT),
  })
  @PutMapping("/add-user-roles")
  public ResponseData<String> addRolesToUser(@RequestBody AddRolesToUserRequest request) throws BusinessException {
    sysUserRoleService.addRolesToUser(request);
    return ResponseUtils.success("OK");
  }
}
