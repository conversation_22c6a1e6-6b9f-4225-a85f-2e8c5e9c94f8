package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import static vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants.EMAIL_PARTNER_LIST_MAX_LENGTH;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.server.annotations.ValidEmail;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailPartnerModel;

/**
 * Model view attribute to create email partner.
 */

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailPartnerRequest extends EmailPartnerModel {
  @NotNull(message = "Address list cannot be null")
  @Size(max = EMAIL_PARTNER_LIST_MAX_LENGTH, message = "Addresses list cannot have more than "
      + EMAIL_PARTNER_LIST_MAX_LENGTH)
  private List<@ValidEmail(message = "Invalid email format for one or more addresses") String> addresses;


  @NotBlank
  @Size(min = 1, message = "Email Partner name can not be empty")
  @Size(
      max = CommonConstants.COMMON_NAME_MAX_LENGTH,
      message = "Email Partner name has max {max} character"
  )
  private String name;
}
