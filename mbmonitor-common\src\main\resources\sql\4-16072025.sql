CREATE TABLE MBMONITOR.CUSTOM_INPUT
(
    ID            VARCHAR2(50) NOT NULL ENABLE PRIMARY KEY,
    NAME          VARCHAR2(100 CHAR),
    DESCRIPTION   VARCHAR2(300 CHAR),
    CONFIGURATION CLOB,
    TYPE          VARCHAR2(50),
    CREATED_DATE  TIMESTAMP,
    CREATED_BY    VARCHAR2(100),
    MODIFIED_DATE TIMESTAMP,
    MODIFIED_BY   VARCHAR2(100)
)
/

CREATE TABLE MBMONITOR.WORKFLOW_TEMPLATE
(
    ID            VARCHAR2(50)       NOT NULL ENABLE PRIMARY KEY,
    NAME          VARCHAR2(100 CHAR) NOT NULL,
    DESCRIPTION   VARCHAR2(300 CHAR),
    FORM_LAYOUT   CLOB,
    WORKFLOW_ID   VARCHAR2(50)       NOT NULL,
    CREATED_DATE  TIMESTAMP,
    CREATED_BY    VARCHAR2(100),
    MOD<PERSON>IED_DATE TIMESTAMP,
    MODIFIED_BY   VARCHAR2(100)
)
/

CREATE INDEX MBMONITOR.WORKFLOW_TEMPLATE_WORKFLOW_ID_INDEX
    ON WORKFLOW_TEMPLATE (WORKFLOW_ID) TABLESPACE INDEXS
/

CREATE TABLE WORKFLOW
(
    ID               VARCHAR2(50) NOT NULL ENABLE PRIMARY KEY,
    START_TIME       TIMESTAMP,
    END_TIME         TIMESTAMP,
    STATUS           VARCHAR2(50),
    CREATED_DATE     TIMESTAMP,
    CREATED_BY       VARCHAR2(100),
    MODIFIED_DATE    TIMESTAMP,
    MODIFIED_BY      VARCHAR2(100)
)
/

CREATE TABLE MBMONITOR.WORKFLOW_NODE
(
    ID                VARCHAR2(50) NOT NULL ENABLE PRIMARY KEY,
    WORKFLOW_ID       VARCHAR2(50),
    STATUS            VARCHAR2(50),
    TYPE              VARCHAR2(50),
    INCOMING_NODE_IDS CLOB,
    OUTGOING_NODE_IDS CLOB,
    POSITION          CLOB,
    CONFIGURATION     CLOB,
    CREATED_DATE      TIMESTAMP,
    CREATED_BY        VARCHAR2(100),
    MODIFIED_DATE     TIMESTAMP,
    MODIFIED_BY       VARCHAR2(100)
)
/

CREATE INDEX MBMONITOR.WORKFLOW_NODE_WORKFLOW_ID_INDEX
    ON WORKFLOW_NODE (WORKFLOW_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.WORKFLOW_NODE_DEPENDENCY
(
    ID               VARCHAR2(50) NOT NULL ENABLE PRIMARY KEY,
    WORKFLOW_NODE_ID VARCHAR2(50),
    WORKFLOW_ID      VARCHAR2(50),
    REFERENCE_ID     VARCHAR2(50),
    TYPE             VARCHAR2(50),
    CREATED_DATE     TIMESTAMP,
    CREATED_BY       VARCHAR2(100),
    MODIFIED_DATE    TIMESTAMP,
    MODIFIED_BY      VARCHAR2(100)
)
/

CREATE INDEX MBMONITOR.WORKFLOW_NODE_DEPENDENCY_WORKFLOW_NODE_ID_INDEX
    ON WORKFLOW_NODE_DEPENDENCY (WORKFLOW_NODE_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.WORKFLOW_NODE_DEPENDENCY_WORKFLOW_ID_INDEX
    ON WORKFLOW_NODE_DEPENDENCY (WORKFLOW_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.WORKFLOW_NODE_DEPENDENCY_TYPE_REFERENCE_ID_INDEX
    ON MBMONITOR.WORKFLOW_NODE_DEPENDENCY (TYPE, REFERENCE_ID) TABLESPACE INDEXS
/