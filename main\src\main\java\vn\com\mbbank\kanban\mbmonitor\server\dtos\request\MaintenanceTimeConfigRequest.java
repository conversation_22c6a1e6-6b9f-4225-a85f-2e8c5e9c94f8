package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeUnitEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;

/**
 * DTO for handling maintenance time configuration requests.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MaintenanceTimeConfigRequest {
  Long id;

  @NotBlank
  @Size(min = 1, message = "Maintenance time name can not be empty")
  @Size(
      max = CommonConstants.COMMON_NAME_MAX_LENGTH,
      message = "Maintenance time name has max {max} character"
  )
  String name;

  @Size(
      max = CommonConstants.COMMON_DESCRIPTION_MAX_LENGTH,
      message = "Maintenance time name has max {max} character"
  )
  String description;

  @NotNull
  MaintenanceTimeConfigTypeEnum type;

  @NotNull
  RuleGroupType ruleGroup;

  @NotNull
  @Size(min = 1)
  @Builder.Default
  List<String> serviceIds = new ArrayList<>();

  @NotNull
  @Builder.Default
  List<String> applicationIds = new ArrayList<>();

  @Max(value = 100, message = "Next time value has max {value}")
  @Min(value = 1, message = "Invalid time config")
  Integer nextTime;
  MaintenanceTimeUnitEnum unit;
  String cronExpression;
  Date startTime;
  Date endTime;
}
