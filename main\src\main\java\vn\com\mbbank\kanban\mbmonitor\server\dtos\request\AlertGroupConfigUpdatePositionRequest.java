package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Comment PriorityConfigUpdatePositionRequest request.
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlertGroupConfigUpdatePositionRequest {
  @NotNull(message = "Alert Group Config From Id can not be empty")
  private Long alertGroupConfigFromId;
  @NotNull(message = "Alert Group Config to Id can not be empty")
  private Long alertGroupConfigToId;
  @NotNull(message = "From position name can not be empty")
  private Integer fromPosition;
  @NotNull(message = "To position can not be empty")
  private Integer toPosition;
}
