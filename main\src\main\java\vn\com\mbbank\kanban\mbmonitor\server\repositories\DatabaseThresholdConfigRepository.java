package vn.com.mbbank.kanban.mbmonitor.server.repositories;


import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseThresholdConfigEntity;

/**
 * Repository table  DatabaseThresholdConfig.
 */
@Repository
public interface DatabaseThresholdConfigRepository
    extends JpaCommonRepository<DatabaseThresholdConfigEntity, String>, DatabaseThresholdConfigRepositoryCustom {

  /**
   * exist of DatabaseThresholdConfigEntity objects with the specified name.
   *
   * @param name The name to count by.
   * @return The true/false of DatabaseThresholdConfigEntity objects with the specified name.
   */
  boolean existsByNameIgnoreCase(String name);

  /**
   * exist of DatabaseThresholdConfigEntity objects with the specified name and excluding the specified id.
   *
   * @param name The name to count by.
   * @param id   The id to exclude.
   * @return The true/false of DatabaseThresholdConfigEntity objects with the specified name and excluding id.
   */
  boolean existsByIdNotAndNameIgnoreCase(String id, String name);

  /**
   * find all config by database connection id.
   *
   * @param databaseConnectionId The id of database connection.
   * @return The list of DatabaseThresholdConfigEntity objects.
   */

  List<DatabaseThresholdConfigEntity> findAllByDatabaseConnectionId(Long databaseConnectionId);

  /**
   * find all database connection config by id of service.
   *
   * @param id The id of service .
   * @return The list of DatabaseThresholdConfigEntity objects.
   */

  List<DatabaseThresholdConfigEntity> findAllByServiceId(String id);

  /**
   * find all database connection config by id of application.
   *
   * @param id The id of application.
   * @return The list of DatabaseThresholdConfigEntity objects.
   */

  List<DatabaseThresholdConfigEntity> findAllByApplicationId(String id);

  /**
   * find all database connection config by id of v.
   *
   * @param id The id of alert priority config.
   * @return The list of DatabaseThresholdConfigEntity objects.
   */

  List<DatabaseThresholdConfigEntity> findAllByPriorityId(Long id);

}
