package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;

/**
 * ExecutionRepositoryCustom.
 */
public interface ExecutionRepositoryCustom {

  /**
   * Find all.
   *
   * @param page PaginationRequestDTO
   * @return list of ExecutionResponse
   */
  Page<ExecutionResponse> findAll(PaginationRequestDTO page);

  /**
   * Find all with type.
   *
   * @param type ExecutionTypeEnum
   * @return list of ExecutionResponse
   */
  List<ExecutionResponse> findAllByType(ExecutionTypeEnum type);

  /**
   * Find all auto trigger name by executionId.
   *
   * @param executionId executionId
   * @return list of auto trigger name
   */
  List<String> findAllAutoTriggerDependenciesNameById(String executionId);

  /**
   * Find all workflow template name by executionId.
   *
   * @param executionId executionId
   * @return list of workflow template name
   */
  List<String> findAllWorkflowTemplateDependenciesNameById(String executionId);

}
