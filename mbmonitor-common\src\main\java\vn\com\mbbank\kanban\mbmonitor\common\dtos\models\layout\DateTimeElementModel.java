package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.enums.FormBuilderElementTypeEnum;

/**
 * DateTimeElement.
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DateTimeElementModel extends BaseFormBuilderInputElementModel {
  String placeholder;
  FormBuilderElementTypeEnum type = FormBuilderElementTypeEnum.DATETIME;
  String defaultValue;
}
