package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import vn.com.mbbank.kanban.mbmonitor.server.enums.CustomObjectTypeEnum;

/**
 * CloseAlertsResponse.
 */
@Data
@Builder
@AllArgsConstructor
public class CustomObjectResponse {
  private Long id;
  private String name;
  private String description;
  private CustomObjectTypeEnum type;
  private String regex;
  private Integer fromIndex;
  private Integer toIndex;
  private String fromKeyword;
  private String toKeyword;
}
