package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants.FIELD_MAP;
import static vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils.truncateToMinutes;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.log.MaintenanceTimeConfigLogModelMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.MaintenanceTimeConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.MaintenanceTimeConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.MaintenanceTimeConfigEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.MaintenanceTimeConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.MaintenanceTimeConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ConditionMapService;
import vn.com.mbbank.kanban.mbmonitor.server.services.MaintenanceTimeConfigDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.MaintenanceTimeConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;

/**
 * Service Logic MaintenanceTimeConfigServiceImpl.
 */
@Service
@RequiredArgsConstructor
public class MaintenanceTimeConfigServiceImpl extends BaseServiceImpl<MaintenanceTimeConfigEntity, Long>
    implements MaintenanceTimeConfigService {

  private static final Logger logger = LoggerFactory.getLogger(MaintenanceTimeConfigService.class);
  private final MaintenanceTimeConfigRepository maintenanceTimeConfigRepository;
  private final MaintenanceTimeConfigEntityMapper maintenanceTimeConfigEntityMapper =
      MaintenanceTimeConfigEntityMapper.INSTANCE;
  private final ServiceService serviceService;
  private final ApplicationService applicationService;
  private final MaintenanceTimeConfigDependencyService maintenanceTimeConfigDependencyService;
  private final SysLogKafkaProducerService sysLogKafkaProducerService;
  private final ConditionMapService conditionMapService;
  private final MaintenanceTimeConfigResponseMapper maintenanceTimeConfigModelMapper =
      MaintenanceTimeConfigResponseMapper.INSTANCE;
  private final MaintenanceTimeConfigLogModelMapper maintenanceTimeConfigLogModelMapper =
      MaintenanceTimeConfigLogModelMapper.INSTANCE;

  @Override
  protected JpaCommonRepository<MaintenanceTimeConfigEntity, Long> getRepository() {
    return maintenanceTimeConfigRepository;
  }

  @Override
  public List<MaintenanceTimeConfigResponse> findAllMaintenance() {
    List<MaintenanceTimeConfigEntity> configs = this.maintenanceTimeConfigRepository.findAllByOrderByCreatedDateDesc();
    return configs.stream()
        .map(config -> maintenanceTimeConfigModelMapper
            .map(config, List.of(), List.of(), List.of(), false)).toList();
  }

  @Override
  @Transactional
  public MaintenanceTimeConfigEntity save(MaintenanceTimeConfigRequest request) throws BusinessException {
    var isCreateMode = Objects.isNull(request.getId());
    validateMaintenanceTimeRequest(request);
    MaintenanceTimeConfigEntity maintenanceTimeConfigEntity;
    var formatName = StringUtils.capitalizeFirstLetter(request.getName());
    var logName = formatName;
    if (isCreateMode) {
      maintenanceTimeConfigEntity = maintenanceTimeConfigEntityMapper.map(request);
      maintenanceTimeConfigEntity.setActive(false);
      maintenanceTimeConfigEntity.setStartTime(
          Objects.nonNull(request.getStartTime()) ? truncateToMinutes(request.getStartTime()) :
              truncateToMinutes(new Date()));

    } else {
      maintenanceTimeConfigEntity =
          maintenanceTimeConfigRepository.findById(request.getId()).orElseThrow(() -> new BusinessException(
              ErrorCode.MAINTENANCE_TIME_CONFIG_NOT_FOUND));
      logName = maintenanceTimeConfigEntity.getName();
      maintenanceTimeConfigDependencyService.deleteAllByMaintenanceTimeConfigId(request.getId());
      maintenanceTimeConfigEntityMapper.merge(maintenanceTimeConfigEntity, request);
    }

    maintenanceTimeConfigEntity.setStartTime(truncateToMinutes(request.getStartTime()));
    maintenanceTimeConfigEntity.setEndTime(truncateToMinutes(request.getEndTime()));
    maintenanceTimeConfigEntity.setName(formatName);
    save(maintenanceTimeConfigEntity);
    var services = serviceService.findAllByIdInAndDeleted(request.getServiceIds(), false);
    if (KanbanCommonUtil.isEmpty(services)) {
      throw new BusinessException(ErrorCode.SERVICE_HAS_BEEN_DELETED);
    }
    var applications = applicationService.findAllByIdInAndServiceIdInAndDeleted(
        request.getApplicationIds(),
        services.stream().map(ServiceEntity::getId).toList(),
        false);
    var applicationMap = applications.stream().collect(Collectors.groupingBy(ApplicationEntity::getServiceId));
    var dependencyServices = new ArrayList<ServiceEntity>();
    var serviceWithAllApplications = new ArrayList<ServiceEntity>();
    for (ServiceEntity service : services) {
      if (applicationMap.containsKey(service.getId())) {
        dependencyServices.add(service);
      } else {
        serviceWithAllApplications.add(service);
      }
    }
    var newDependencies = new ArrayList<MaintenanceTimeConfigDependencyEntity>();
    dependencyServices.forEach(service -> {
      var dependency = new MaintenanceTimeConfigDependencyEntity();
      dependency.setMaintenanceTimeConfigId(maintenanceTimeConfigEntity.getId());
      dependency.setDependencyId(service.getId());
      dependency.setType(MaintenanceTimeConfigDependencyTypeEnum.SERVICE);
      newDependencies.add(dependency);
    });
    applications.forEach(application -> {
      var dependency = new MaintenanceTimeConfigDependencyEntity();
      dependency.setMaintenanceTimeConfigId(maintenanceTimeConfigEntity.getId());
      dependency.setDependencyId(application.getId());
      dependency.setType(MaintenanceTimeConfigDependencyTypeEnum.APPLICATION);
      newDependencies.add(dependency);
    });
    serviceWithAllApplications.forEach(service -> {
      var dependency = new MaintenanceTimeConfigDependencyEntity();
      dependency.setMaintenanceTimeConfigId(maintenanceTimeConfigEntity.getId());
      dependency.setDependencyId(service.getId());
      dependency.setType(MaintenanceTimeConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);
      newDependencies.add(dependency);
    });
    maintenanceTimeConfigDependencyService.saveAll(newDependencies);

    sysLogKafkaProducerService.send(
        isCreateMode ? LogActionEnum.CREATE_MAINTENANCE_TIME_CONFIG : LogActionEnum.EDIT_MAINTENANCE_TIME_CONFIG,
        logName,
        maintenanceTimeConfigLogModelMapper.map(maintenanceTimeConfigEntity, services, serviceWithAllApplications,
            applications, conditionMapService.mapNameCondition(request.getRuleGroup(),
                        null, true, FIELD_MAP)));
    return maintenanceTimeConfigEntity;
  }

  public MaintenanceTimeConfigResponse findByIdWithDetail(Long id) throws BusinessException {
    var maintenanceTimeConfig = maintenanceTimeConfigRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.MAINTENANCE_TIME_CONFIG_NOT_FOUND));
    var maintenanceTimeConfigDependencies = maintenanceTimeConfigDependencyService.findAllByMaintenanceTimeConfigId(id);

    var serviceIds = new ArrayList<String>();
    var applicationIds = new ArrayList<String>();
    for (MaintenanceTimeConfigDependencyEntity dependency : maintenanceTimeConfigDependencies) {
      var type = dependency.getType();
      if (MaintenanceTimeConfigDependencyTypeEnum.SERVICE.equals(type)
          || MaintenanceTimeConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION.equals(type)) {
        serviceIds.add(dependency.getDependencyId());
      }
      if (MaintenanceTimeConfigDependencyTypeEnum.APPLICATION.equals(type)) {
        applicationIds.add(dependency.getDependencyId());
      }
    }
    List<ServiceEntity> services = CollectionUtils.isNotEmpty(serviceIds)
        ? serviceService.findAllByIdIn(serviceIds) : new ArrayList<>();
    List<ApplicationResponse> applications = CollectionUtils.isNotEmpty(applicationIds)
        ? applicationService.findAllByIdIn(applicationIds) : new ArrayList<>();
    return maintenanceTimeConfigModelMapper.map(maintenanceTimeConfig, services, applications,
        maintenanceTimeConfigDependencies, true);
  }


  @Override
  public MaintenanceTimeConfigEntity updateActive(Long id, Boolean active) throws BusinessException {
    var maintenanceTimeConfig = maintenanceTimeConfigRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.MAINTENANCE_TIME_CONFIG_NOT_FOUND));
    if (Boolean.TRUE.equals(active)
        && MaintenanceTimeConfigTypeEnum.NEXT_TIME.equals(maintenanceTimeConfig.getType())) {
      maintenanceTimeConfig.setStartTime(new Date());
      Calendar calendar = Calendar.getInstance();
      calendar.setTime(maintenanceTimeConfig.getStartTime());
      switch (maintenanceTimeConfig.getUnit()) {
        case SECOND -> calendar.add(Calendar.SECOND, maintenanceTimeConfig.getNextTime());
        case MINUTE -> calendar.add(Calendar.MINUTE, maintenanceTimeConfig.getNextTime());
        case HOUR -> calendar.add(Calendar.HOUR, maintenanceTimeConfig.getNextTime());
        case DAY -> calendar.add(Calendar.DAY_OF_YEAR, maintenanceTimeConfig.getNextTime());
        case WEEK -> calendar.add(Calendar.WEEK_OF_YEAR, maintenanceTimeConfig.getNextTime());
        case MONTH -> calendar.add(Calendar.MONTH, maintenanceTimeConfig.getNextTime());
        case YEAR -> calendar.add(Calendar.YEAR, maintenanceTimeConfig.getNextTime());
        default -> logger.warn("Unsupported time unit: " + maintenanceTimeConfig.getUnit());
      }
      maintenanceTimeConfig.setEndTime(calendar.getTime());
    }
    maintenanceTimeConfig.setActive(active);
    var res = maintenanceTimeConfigRepository.save(maintenanceTimeConfig);
    sysLogKafkaProducerService.send(
        active ? LogActionEnum.ACTIVE_MAINTENANCE_TIME_CONFIG : LogActionEnum.INACTIVE_MAINTENANCE_TIME_CONFIG,
        res.getName());
    return res;
  }

  @Override
  public List<MaintenanceTimeConfigEntity> findAllByActive(Boolean active) {
    return maintenanceTimeConfigRepository.findAllByActive(active);
  }

  @Override
  public List<String> findDependencyNameByDependencyId(String dependencyId,
                                                    List<MaintenanceTimeConfigDependencyTypeEnum> type) {
    return maintenanceTimeConfigRepository.findDependencyNameByDependencyId(dependencyId, type);
  }

  @Override
  public void deleteConfigById(Long id) throws BusinessException {
    MaintenanceTimeConfigEntity maintenanceTimeConfig = maintenanceTimeConfigRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.MAINTENANCE_TIME_CONFIG_NOT_FOUND));
    if (maintenanceTimeConfig.getActive()) {
      throw new BusinessException(ErrorCode.MAINTENANCE_TIME_CONFIG_CAN_NOT_BE_DELETED);
    }
    deleteById(id);
    sysLogKafkaProducerService.send(LogActionEnum.DELETE_MAINTENANCE_TIME_CONFIG,
        maintenanceTimeConfig.getName());
  }

  protected void validateMaintenanceTimeRequest(MaintenanceTimeConfigRequest request) throws BusinessException {
    var name = request.getName().trim();
    var type = request.getType();
    var isUpdateMode = Objects.nonNull(request.getId());
    var isExist =
        isUpdateMode
            ? maintenanceTimeConfigRepository.existsByIdNotAndNameIgnoreCase(request.getId(), name)
            : maintenanceTimeConfigRepository.existsByNameIgnoreCase(name);

    if (isExist) {
      throw new BusinessException(ErrorCode.MAINTENANCE_TIME_CONFIG_NAME_IS_EXISTED);
    }

    if (MaintenanceTimeConfigTypeEnum.NEXT_TIME.equals(type)
        && (Objects.isNull(request.getNextTime()) || Objects.isNull(request.getUnit()))) {
      throw new BusinessException(ErrorCode.MAINTENANCE_TIME_CONFIG_TYPE_TIME_INFO_CAN_NOT_EMPTY);
    }

    if (MaintenanceTimeConfigTypeEnum.FROM_TIME_TO_TIME.equals(type)
        && (Objects.isNull(request.getStartTime()) || Objects.isNull(request.getEndTime())
        || (request.getStartTime().after(request.getEndTime())))) {
      throw new BusinessException(ErrorCode.MAINTENANCE_TIME_CONFIG_TYPE_TIME_INFO_CAN_NOT_EMPTY);
    }

    if (MaintenanceTimeConfigTypeEnum.CRON_JOB.equals(type) && (Objects.isNull(request.getCronExpression()))) {
      throw new BusinessException(ErrorCode.MAINTENANCE_TIME_CONFIG_TYPE_TIME_INFO_CAN_NOT_EMPTY);
    }
  }

  @Override
  public List<String> findAllByCustomObjectId(Long id) {
    Set<String> maintenanceTimeConfigNamesSet = new HashSet<>();
    var configs = maintenanceTimeConfigRepository.findAll();
    for (MaintenanceTimeConfigEntity config : configs) {
      if (!KanbanCommonUtil.isEmpty(config.getRuleGroup()) && config.getRuleGroup().checkCustomObject(id)) {
        maintenanceTimeConfigNamesSet.add(config.getName());
      }
    }
    return new ArrayList<>(maintenanceTimeConfigNamesSet);
  }

  @Override
  public List<String> findAllByPriorityConfigId(Long id) {
    Set<String> maintenanceTimeConfigNamesSet = new HashSet<>();
    var configs = maintenanceTimeConfigRepository.findAll();
    for (MaintenanceTimeConfigEntity config : configs) {
      if (!KanbanCommonUtil.isEmpty(config.getRuleGroup()) && config.getRuleGroup().checkPriority(id)) {
        maintenanceTimeConfigNamesSet.add(config.getName());
      }
    }
    return new ArrayList<>(maintenanceTimeConfigNamesSet);
  }

}
