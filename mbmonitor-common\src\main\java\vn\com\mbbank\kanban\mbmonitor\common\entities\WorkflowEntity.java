package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowStatusEnum;

@Entity
@Data
@Table(name = TableName.WORKFLOW)
@EqualsAndHashCode(callSuper = true)
@KanbanAutoGenerateUlId
public class WorkflowEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "START_TIME")
  @Temporal(TemporalType.TIMESTAMP)
  private Date startTime;

  @Column(name = "END_TIME")
  @Temporal(TemporalType.TIMESTAMP)
  private Date endTime;

  @Column(name = "STATUS")
  @Enumerated(EnumType.STRING)
  private WorkflowStatusEnum status;

  @Override
  public String getId() {
    return id;
  }
}
