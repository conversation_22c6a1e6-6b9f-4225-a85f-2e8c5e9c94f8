package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;


import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WebHookEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.WebHookDto;
import vn.com.mbbank.kanban.mbmonitor.server.services.WebHookService;

/**
 * Controller webhook.
 *
 * <AUTHOR>
 * @created_date 8/19/2024
 */
@RestController
@RequestMapping(ServerUrl.WEB_HOOK_CONFIG_URL)
public class WebHookConfigController extends BaseController {

  @Autowired
  private WebHookService webHookService;

  /**
   * Api get all data config webhook.
   *
   * @param request request
   * @return List config webhook
   * @throws BusinessException exception
   */

  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.WEBHOOK_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping
  public ResponseData<Page<WebHookEntity>> getAll(HttpServletRequest request)
      throws BusinessException {
    var page = getPaging(request);
    page.setPropertiesSearch(List.of("name", "dataType", "createdBy", "createdDate"));
    return ResponseUtils.success(webHookService.findAllWithPaging(page));
  }


  /**
   * Api save web hook config.
   *
   * @param body data
   * @return WebHookDto
   * @throws BusinessException exception
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.WEBHOOK_CONFIG, action = PermissionActionEnum.EDIT),
      @AclPermission(module = PermissionModuleEnum.WEBHOOK_CONFIG, action = PermissionActionEnum.CREATE)
  })
  @PostMapping
  public ResponseData<WebHookEntity> save(@Valid @RequestBody WebHookDto body)
      throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.WEBHOOK_CONFIG, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT,
        Collections.singleton(body.getId()));
    return ResponseUtils.success(webHookService.saveConfig(body));
  }

  /**
   * Delete by Id.
   *
   * @param id id
   * @return Object
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.WEBHOOK_CONFIG, action = PermissionActionEnum.DELETE)
  })
  @Operation(summary = "Delete webhook by id")
  @DeleteMapping("/{id}")
  public ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    webHookService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }


  /**
   * Delete by batch.
   *
   * @param ids list ids
   * @return status
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.WEBHOOK_CONFIG, action = PermissionActionEnum.DELETE)
  })
  @Operation(summary = "Delete webhook by list ID")
  @DeleteMapping("/batch")
  public ResponseData<String> deleteByIdIn(@RequestParam(value = "ids[]") List<Long> ids)
      throws BusinessException {
    webHookService.deleteByIdIn(ids);
    return ResponseUtils.success("OK");
  }


  /**
   * find by id.
   *
   * @param id id
   * @return webhook config
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.WEBHOOK_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @Operation(summary = "Find webhook by id")
  @GetMapping("/{id}")
  public ResponseData<WebHookEntity> findById(@PathVariable Long id) throws BusinessException {
    return ResponseUtils.success(webHookService.findById(id));
  }


  /**
   * Refresh Token.
   *
   * @param id id
   * @return OK
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.WEBHOOK_CONFIG, action = PermissionActionEnum.EDIT)
  })
  @Operation(summary = "Refresh Token")
  @PutMapping("/{id}/refresh")
  public ResponseData<String> refreshToken(@PathVariable Long id) throws BusinessException {
    webHookService.refreshToken(id);
    return ResponseUtils.success("OK");
  }

}
