package vn.com.mbbank.kanban.mbmonitor.common.converter;

import com.alibaba.fastjson2.JSON;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.List;

/**
 * WorkflowNodeIdsConverter.
 */
@Converter
public class WorkflowNodeIdsConverter implements AttributeConverter<List<String>, String> {

  @Override
  public String convertToDatabaseColumn(List<String> attribute) {
    if (attribute == null) {
      return null;
    }
    return JSON.toJSONString(attribute);
  }

  @Override
  public List<String> convertToEntityAttribute(String dbData) {
    try {
      return JSON.parseArray(dbData, String.class);
    } catch (Exception e) {
      return null;
    }
  }
}
