package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CustomObjectUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ConditionMapService;
import vn.com.mbbank.kanban.mbmonitor.server.services.CustomObjectService;

@Service
public class ConditionMapServiceImpl implements ConditionMapService {

  @Lazy
  @Autowired
  private CustomObjectService customObjectService;

  @Lazy
  @Autowired
  private AlertPriorityConfigService alertPriorityConfigService;

  @Autowired
  private ObjectMapper objectMapper;

  @Override
  public String mapNameCondition(RuleGroupType ruleGroup,
                                 Class<?> clazz,
                                 boolean isPriorityMap,
                                 Set<String> fieldMap) {
    if (ruleGroup == null) {
      return null;
    }
    try {
      var logRuleGroup = objectMapper.convertValue(ruleGroup, RuleGroupType.class);

      Set<Long> customObjectIds = new HashSet<>();
      if (clazz != null) {
        customObjectIds.addAll(logRuleGroup.getCustomObjectIds(clazz));
      }
      if (fieldMap != null) {
        customObjectIds.addAll(logRuleGroup.getCustomObjectIds(fieldMap));
      }

      List<CustomObjectEntity> customObjectEntities = customObjectService.findAllById(customObjectIds);
      List<AlertPriorityConfigEntity> priorityEntities = null;

      if (isPriorityMap) {
        Set<Long> priorityIds = CustomObjectUtils.extractPriorityIdsFromRuleGroup(logRuleGroup);
        priorityEntities = alertPriorityConfigService.findAllById(priorityIds);
      }

      CustomObjectUtils.replaceFieldIdWithName(customObjectEntities, priorityEntities, logRuleGroup);
      return logRuleGroup.toString();
    } catch (Exception e) {
      return ruleGroup.toString();
    }

  }

  public List<String> mapNameListCondition(List<RuleGroupType> ruleGroups,
                                        Class<?> clazz,
                                        boolean isPriorityMap,
                                        Set<String> fieldMap) {
    if (ruleGroups == null || ruleGroups.isEmpty()) {
      return List.of();
    }
    try {
      List<RuleGroupType> logRuleGroups = objectMapper.convertValue(
              ruleGroups,
              new TypeReference<List<RuleGroupType>>() {});

      Set<Long> allCustomObjectIds = new HashSet<>();
      Set<Long> allPriorityIds = new HashSet<>();

      for (RuleGroupType ruleGroup : logRuleGroups) {
        if (clazz != null) {
          allCustomObjectIds.addAll(ruleGroup.getCustomObjectIds(clazz));
        }
        if (fieldMap != null) {
          allCustomObjectIds.addAll(ruleGroup.getCustomObjectIds(fieldMap));
        }
        if (isPriorityMap) {
          allPriorityIds.addAll(CustomObjectUtils.extractPriorityIdsFromRuleGroup(ruleGroup));
        }
      }

      List<CustomObjectEntity> allCustomObjects = customObjectService.findAllById(allCustomObjectIds);
      List<AlertPriorityConfigEntity> allPriorityEntities = isPriorityMap
              ? alertPriorityConfigService.findAllById(allPriorityIds)
              : null;

      return logRuleGroups.stream()
              .peek(ruleGroup ->
                      CustomObjectUtils.replaceFieldIdWithName(allCustomObjects, allPriorityEntities, ruleGroup))
              .map(RuleGroupType::toString)
              .collect(Collectors.toList());
    } catch (Exception e) {
      return ruleGroups.stream()
              .filter(ele -> !CollectionUtils.isEmpty(ele.getRules()))
              .map(RuleGroupType::toString)
              .collect(Collectors.toList());
    }
  }
}
