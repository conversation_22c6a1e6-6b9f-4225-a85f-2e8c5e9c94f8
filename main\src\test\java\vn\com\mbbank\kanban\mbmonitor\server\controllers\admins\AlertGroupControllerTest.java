package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.server.controllers.AlertGroupController;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertGroupSearchRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CloseAlertGroupsResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertGroupService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AlertService;

@ExtendWith({MockitoExtension.class})
public class AlertGroupControllerTest {

  @Mock
  AlertService alertService;
  @Mock
  AlertGroupService alertGroupService;
  @Mock
  AlertPriorityConfigService alertPriorityConfigService;
  @InjectMocks
  AlertGroupController alertGroupController;

  @Test
  void findAlertRecipientByAlertGroupStatus_success() {
    List<String> recipients = Collections.emptyList();
    when(alertGroupService.findAlertRecipientByAlertGroupStatus(
        any(AlertGroupStatusEnum.class))).thenReturn(
        recipients);

    var result =
        alertGroupController.findAlertRecipientByAlertGroupStatus(AlertGroupStatusEnum.CLOSE);

    Assertions.assertEquals(0, result.getData().size());
    verify(alertGroupService).findAlertRecipientByAlertGroupStatus(any(AlertGroupStatusEnum.class));
  }

  @Test
  void close_success() {
    var closeAlertsResponse =
        new CloseAlertGroupsResponse(new HashSet<>(), new HashSet<>(), "Test");
    when(alertGroupService.close(anyList())).thenReturn(closeAlertsResponse);

    var result = alertGroupController.close(List.of(1L));

    Assertions.assertNotNull(result);
    verify(alertGroupService).close(anyList());
  }

  @Test
  void findAllByAlertGroupId_In_success() {
    when(alertService.findAllByAlertGroupIdIn(anyList(), any())).thenReturn(Page.empty());
    var res = alertGroupController.findAllByAlertGroupIdIn(List.of(1L), new PaginationRequest());
    verify(alertService).findAllByAlertGroupIdIn(anyList(), any());
    assertNotNull(res);
  }

  @Test
  void search_success() {
    var searchRequest = new AlertGroupSearchRequest();
    HttpServletRequest request = mock(HttpServletRequest.class);
    when(alertGroupService.findAll(any(AlertGroupSearchRequest.class), any())).thenReturn(null);
    var res = alertGroupController.findAllByPostMethod(request, searchRequest);
    assertNull(res.getData());
  }
}
