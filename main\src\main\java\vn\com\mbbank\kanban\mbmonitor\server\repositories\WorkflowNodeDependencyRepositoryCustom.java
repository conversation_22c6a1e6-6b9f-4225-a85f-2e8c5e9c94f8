package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;

/**
 * Repository WorkflowTemplateNodeDependencyRepositoryCustom.
 */
@Repository
public interface WorkflowNodeDependencyRepositoryCustom {

  /**
   * find all workflow template name use this dependency.
   *
   * @param dependencyId dependencyId
   * @param type type
   * @return list of workflowTemplate name
   */
  List<String> findWorkflowTemplateNameByReferenceIdAndType(String dependencyId, WorkflowNodeTypeEnum type);
}
