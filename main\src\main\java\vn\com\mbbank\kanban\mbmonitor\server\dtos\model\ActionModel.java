package vn.com.mbbank.kanban.mbmonitor.server.dtos.model;


import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ActionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.FindElementByEnum;

/**
 * Model request of action config.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ActionModel {
  String id;
  String actionId;
  @NotNull
  ActionTypeEnum actionType;
  @NotNull
  FindElementByEnum findElementBy;
  String identifier;
  String value;
  Integer orders;
  String note;
}
