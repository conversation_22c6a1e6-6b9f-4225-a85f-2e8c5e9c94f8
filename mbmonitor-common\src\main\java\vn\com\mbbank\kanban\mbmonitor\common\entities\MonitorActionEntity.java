package vn.com.mbbank.kanban.mbmonitor.common.entities;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ActionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.FindElementByEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 29/05/2025
 */
@KanbanAutoGenerateUlId
@Getter
@Setter
@Entity
@Table(name = TableName.MONITOR_ACTION)
@ToString
public class MonitorActionEntity extends BaseEntity<String> {
  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "ACTION_ID", nullable = false)
  private String actionId;

  @Column(name = "ACTION_TYPE", nullable = false)
  @Enumerated(EnumType.STRING)
  private ActionTypeEnum actionType;

  @Column(name = "FIND_ELEMENT_BY", nullable = false)
  @Enumerated(EnumType.STRING)
  private FindElementByEnum findElementBy;

  @Lob
  @Column(name = "IDENTIFIER")
  private String identifier;

  @Column(name = "VALUE")
  private String value;

  @Column(name = "ORDERS", nullable = false)
  private Integer orders;
  
  @Override
  public String getId() {
    return id;
  }
}
