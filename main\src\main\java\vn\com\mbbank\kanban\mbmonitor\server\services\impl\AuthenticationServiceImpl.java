package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import jakarta.servlet.http.HttpServletRequest;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.com.mbbank.kanban.core.configs.configurations.AuthenticationLocalConfig;
import vn.com.mbbank.kanban.core.constants.KanbanAppConstants;
import vn.com.mbbank.kanban.core.dtos.TokenDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanTokenUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserRefreshTokenEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRedisService;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AuthRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.KeycloakTokenResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.RefreshTokenResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.SysUserRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AuthenticationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.SysUserRefreshTokenService;

@Service
@RequiredArgsConstructor
public class AuthenticationServiceImpl implements AuthenticationService {
  @Value("${mbmonitor.security.keycloak.clientId}")
  String clientId;

  @Value("${mbmonitor.security.keycloak.url}")
  String url;

  @Value("${kanban.authentication.local.config.refreshTokenExpireTime}")
  long refreshTokenExpire;


  private final SysUserRepository repository;
  private final SysUserRefreshTokenService sysUserRefreshTokenService;
  private final CommonRedisService commonRedisService;
  private final AuthenticationLocalConfig authenticationLocalConfig;
  private static final Logger logger = LoggerFactory.getLogger(AuthenticationService.class);

  @Override
  @Transactional(rollbackFor = BusinessException.class)
  public TokenDTO generateLoginToken(AuthRequest request) throws Exception {
    String userName = request.getUserName();
    SysUserEntity sysUserEntity = repository.findByUserName(userName)
            .orElseThrow(() -> new BusinessException(ErrorCode.UNAUTHORIZED));

    if (request.isUserLocal()) {
      BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
      if (sysUserEntity.getPassword() == null || !encoder.matches(request.getPassword(), sysUserEntity.getPassword())) {
        throw new BusinessException(ErrorCode.UNAUTHORIZED);
      }
    } else {
      if (getKeycloakResponse(userName, request.getPassword()).isEmpty()) {
        throw new BusinessException(ErrorCode.UNAUTHORIZED);
      }
    }
    Map<String, String> claims = new HashMap<>();
    claims.put(KanbanAppConstants.JWT_EMAIL, sysUserEntity.getEmail());
    Date refreshTokenExpireDate = new Date(System.currentTimeMillis() + refreshTokenExpire);
    var tokenResponse = generateToken(userName, claims, refreshTokenExpireDate);
    List<SysUserRefreshTokenEntity> entityList = sysUserRefreshTokenService.findByUserName(userName);
    var idExpiredList = entityList.stream()
            .filter(s -> s.getExpiredDate().before(new Date(System.currentTimeMillis())))
            .map(SysUserRefreshTokenEntity::getId)
            .toList();
    if (!idExpiredList.isEmpty()) {
      sysUserRefreshTokenService.deleteAllInListId(idExpiredList);
    }
    commonRedisService.save(tokenResponse.getRefreshToken(), userName, refreshTokenExpire);
    return tokenResponse;
  }

  @Override
  public RefreshTokenResponse refreshToken(String refreshToken, HttpServletRequest request) throws Exception {
    String userName;
    if (commonRedisService.isConnected()) {
      if (commonRedisService.isExistsByKey(refreshToken)) {
        userName = commonRedisService.get(refreshToken, String.class);
      } else {
        throw new BusinessException(ErrorCode.REFRESH_TOKEN_INVALID);
      }
    } else {
      var sysUserRefreshTokenEntity = sysUserRefreshTokenService.findByRefreshToken(refreshToken);
      if (sysUserRefreshTokenEntity.isPresent()
              && !sysUserRefreshTokenEntity.get().getExpiredDate().before(new Date(System.currentTimeMillis()))) {
        userName = sysUserRefreshTokenEntity.get().getUserName();
      } else {
        throw new BusinessException(ErrorCode.REFRESH_TOKEN_INVALID);
      }
    }
    RSAPublicKey publicKey = KanbanTokenUtils.loadPublicKey(authenticationLocalConfig.getPublicKey());
    RSAPrivateKey privateKey = KanbanTokenUtils.loadPrivateKey(authenticationLocalConfig.getPrivateKey());
    String token = KanbanTokenUtils.extractToken(request);
    Map<String, String> claims = KanbanTokenUtils.extractClaimsFromToken(token, publicKey);
    var tokenResponse = KanbanTokenUtils.generateToken(userName, claims,
            privateKey, publicKey, authenticationLocalConfig.getAccessTokenExpireTime());
    if (tokenResponse == null) {
      throw new BusinessException(ErrorCode.REFRESH_TOKEN_INVALID);
    }
    return RefreshTokenResponse.builder()
            .accessToken(tokenResponse.getAccessToken())
            .expiresAt(tokenResponse.getExpiresAt())
            .build();
  }

  @Override
  public int logout(String refreshToken) {
    if (refreshToken != null) {
      if (commonRedisService.isConnected()) {
        commonRedisService.delete(refreshToken);
      }
      return sysUserRefreshTokenService.deleteByRefreshToken(refreshToken);
    }
    return 0;
  }

  /**
   * get keyCloak response
   *
   * @param username for get token
   * @param password for get token
   * @return KeycloakTokenResponse
   */

  public Optional<KeycloakTokenResponse> getKeycloakResponse(String username, String password) {
    RestTemplate restTemplate = new RestTemplate();
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
    form.add("grant_type", "password");
    form.add("client_id", clientId);
    form.add("username", username);
    form.add("password", password);
    HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(form, headers);
    try {
      var response = restTemplate.postForEntity(url, request, KeycloakTokenResponse.class);
      return response.getStatusCode() == HttpStatus.OK ? Optional.ofNullable(response.getBody()) : Optional.empty();
    } catch (Exception e) {
      logger.info("Error when get token from keycloak: " + e.getMessage());
      return Optional.empty();
    }
  }

  /**
   * generate token.
   *
   * @param userName               for token
   * @param claims                 for token
   * @param refreshTokenExpireDate for save refresh token
   * @return TokenDTO
   * @throws Exception when call generateToken
   */
  public TokenDTO generateToken(String userName, Map<String, String> claims, Date refreshTokenExpireDate)
          throws Exception {
    RSAPrivateKey privateKey = KanbanTokenUtils.loadPrivateKey(authenticationLocalConfig.getPrivateKey());
    RSAPublicKey publicKey = KanbanTokenUtils.loadPublicKey(authenticationLocalConfig.getPublicKey());
    var tokenResponse = KanbanTokenUtils.generateToken(userName, claims,
            privateKey, publicKey, authenticationLocalConfig.getAccessTokenExpireTime());
    sysUserRefreshTokenService.saveSysUserRefreshToken(userName,
            tokenResponse.getRefreshToken(), refreshTokenExpireDate);
    return tokenResponse;
  }
}