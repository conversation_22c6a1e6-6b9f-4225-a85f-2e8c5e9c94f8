package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.Collections;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailPartnerModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailPartnerPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.EmailPartnerRequest;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailPartnerService;

/**
 * Controller logic email partner .
 *
 * <AUTHOR>
 * @created_date 11/04/2024
 */
@RestController
@RequestMapping(ServerUrl.EMAIL_PARTNER_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EmailPartnerController extends BaseController {
  EmailPartnerService emailPartnerService;

  /**
   * Finds all email partner.
   *
   * @param paginationRequest the pagination request containing paging details
   *                          and the list of service IDs to filter email partner
   * @return a paginated list of EmailPartnerModel
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_PARTNER_CONFIG, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.SEND_EMAIL, action = PermissionActionEnum.SEND_EMAIL)
  })
  @GetMapping
  public ResponseData<Page<EmailPartnerModel>> findAll(
      @ModelAttribute EmailPartnerPaginationRequest paginationRequest) {
    return ResponseUtils.success(this.emailPartnerService.findAll(paginationRequest));
  }

  /**
   * Find By id.
   *
   * @param id id
   * @return data
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_PARTNER_CONFIG, action = PermissionActionEnum.VIEW)
  })
  @GetMapping("/{id}")
  public ResponseData<EmailPartnerModel> findById(@PathVariable Long id) throws BusinessException {
    return ResponseUtils.success(this.emailPartnerService.findEmailPartnerById(id));
  }

  /**
   * Create or update partner.
   *
   * @param request request
   * @return new EmailPartnerModel
   */
  @PostMapping
  public ResponseData<EmailPartnerModel> createOrUpdate(
      @RequestBody @Valid EmailPartnerRequest request
  ) throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.EMAIL_PARTNER_CONFIG, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(request.getId()));
    return ResponseUtils.success(this.emailPartnerService.createOrUpdate(request));
  }

  /**
   * Delete by Id.
   *
   * @param id id
   * @return Object
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EMAIL_PARTNER_CONFIG, action = PermissionActionEnum.DELETE)
  })
  @Operation(summary = "Delete email partner by id")
  @DeleteMapping("/{id}")
  public ResponseData<String> deleteById(@PathVariable Long id) throws BusinessException {
    this.emailPartnerService.deleteWithId(id);
    return ResponseUtils.success(HttpStatus.OK.getReasonPhrase());
  }

}
