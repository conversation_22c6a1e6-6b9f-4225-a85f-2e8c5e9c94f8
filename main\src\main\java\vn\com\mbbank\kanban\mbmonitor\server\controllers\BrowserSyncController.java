package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.annotations.SkipAuthentication;
import vn.com.mbbank.kanban.mbmonitor.server.services.BrowserSyncService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 16/06/2025
 */
@RestController
@RequestMapping("/browser-sync")
@AllArgsConstructor
public class BrowserSyncController {
  
  private BrowserSyncService browserSyncService;
  
  /**
   * Endpoint to extract resources by checking and syncing browsers.
   * This method bypasses authentication due to the @SkipAuthentication annotation.
   *
   * @return A success message if the sync completes, or an error message if an exception occurs.
   */
  @SkipAuthentication
  @PostMapping("/extract-from-resource")
  public String extractResources() {
    try {
      return browserSyncService.checkAndSyncBrowsers();
    } catch (Exception e) {
      return String.format("Has an error when copy file : %s", e.getMessage());
    }
  }

}

