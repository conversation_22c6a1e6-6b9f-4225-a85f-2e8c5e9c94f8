package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.server.services.BrowserSyncService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 16/06/2025
 */
@Slf4j
@RestController
@RequestMapping("/browser-sync")
@AllArgsConstructor
public class BrowserSyncController extends BaseController {
  
  private BrowserSyncService browserSyncService;
  
  /**
   * Endpoint to extract resources by checking and syncing browsers.
   * This method bypasses authentication due to the @SkipAuthentication annotation.
   *
   * @param force force a copy file.
   * @return A success message if the sync completes, or an error message if an exception occurs.
   */
  @PostMapping("/extract-from-resource")
  public String extractResources(@RequestParam boolean force) {
    try {
      makeSureUserAdmin();
      return browserSyncService.checkAndSyncBrowsers(force);
    } catch (Exception e) {
      log.error("Has an error when copy file", e);
      return String.format("Has an error when copy file : %s", e.getMessage());
    }
  }

}

