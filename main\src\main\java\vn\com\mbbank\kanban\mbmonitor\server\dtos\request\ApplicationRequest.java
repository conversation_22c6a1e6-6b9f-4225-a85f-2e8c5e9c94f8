package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;

/**
 * Model request service to create or update service.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ApplicationRequest {
  String id;

  @NotBlank
  @Size(max = CommonConstants.APPLICATION_NAME_MAX_LENGTH, message = "eds 100 characters")
  @Size(min = 1, message = "Application name can not empty")
  String name;
  @Size(max = CommonConstants.APPLICATION_DESCRIPTION_MAX_LENGTH,
      message = "Application description exceeds 300 characters")
  String description;
  @NotNull(message = "Service can not empty.")
  String serviceId;
}
