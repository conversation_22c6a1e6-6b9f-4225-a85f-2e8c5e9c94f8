package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionGroupRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionGroupResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.ExecutionGroupResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionGroupDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionGroupService;

/**
 * ExecutionGroupController.
 */
@RestController
@RequestMapping(ServerUrl.EXECUTION_GROUP_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ExecutionGroupController extends BaseController {

  private final ExecutionGroupService executionGroupService;
  private final ExecutionGroupDependencyService executionGroupDependencyService;
  private final ExecutionGroupResponseMapper executionGroupResponseMapper = ExecutionGroupResponseMapper.INSTANCE;

  /**
   * Api find config by ID.
   *
   * @param id id of config.
   * @return ExecutionGroupResponse.
   */
  @GetMapping(value = "/{id}")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EXECUTION_GROUP, action = PermissionActionEnum.VIEW)
  })
  ResponseData<ExecutionGroupResponse> findById(@PathVariable String id)
      throws BusinessException {
    return ResponseUtils.success(executionGroupResponseMapper.map(executionGroupService.findWithId(id)));
  }

  /**
   * Api find a list of config.
   *
   * @param request search
   * @return Page of ExecutionGroupResponse
   */
  @GetMapping(value = "/with-paging")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EXECUTION_GROUP, action = PermissionActionEnum.VIEW),
  })
  ResponseData<Page<ExecutionGroupResponse>> findAllWithPaging(@ModelAttribute PaginationRequest request)
      throws BusinessException {
    return ResponseUtils.success(
        executionGroupService.findAllWithPaging(request).map(executionGroupResponseMapper::map));
  }

  /**
   * Api find a list of config.
   *
   * @param request search
   * @return Page of ExecutionGroupResponse
   */
  @GetMapping(value = "/with-permission")
  ResponseData<Page<ExecutionGroupResponse>> findAllWithPermission(@ModelAttribute PaginationRequest request)
      throws BusinessException {
    return ResponseUtils.success(
        executionGroupService.findAllWithPermission(request).map(executionGroupResponseMapper::map));
  }

  /**
   * Api find a list of config.
   *
   * @param orderBy orderBy
   * @return List of ExecutionGroupResponse
   */
  @GetMapping
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.USER_MANAGEMENT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ROLE_MANAGEMENT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.EXECUTION, action = PermissionActionEnum.EDIT),
      @AclPermission(module = PermissionModuleEnum.EXECUTION, action = PermissionActionEnum.VIEW)
  })
  ResponseData<List<ExecutionGroupResponse>> findAll(@RequestParam(required = false) String orderBy) {
    Sort sort =
        Sort.by(Sort.Order.desc(!KanbanCommonUtil.isEmpty(orderBy) ? orderBy : "createdDate"));
    return ResponseUtils.success(executionGroupResponseMapper.map(executionGroupService.findAll(sort)));
  }

  /**
   * Api find a all execution group with execution amount.
   *
   * @return List of ExecutionGroupResponse
   */
  @GetMapping(value = "/with-execution-amount")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.USER_MANAGEMENT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ROLE_MANAGEMENT, action = PermissionActionEnum.VIEW)
  })
  ResponseData<List<ExecutionGroupResponse>> findAllWithExecutionAmount() {
    return ResponseUtils.success(executionGroupService.findAllWithExecutionAmount());
  }

  /**
   * Api save config.
   *
   * @param request input data.
   * @return ExecutionGroupResponse.
   * @throws BusinessException exception
   */
  @PostMapping
  ResponseData<ExecutionGroupResponse> save(
      @Valid @RequestBody ExecutionGroupRequest request) throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.EXECUTION_GROUP,
        PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(request.getId()));
    return ResponseUtils.success(executionGroupResponseMapper.map(executionGroupService.createOrUpdate(request)));
  }

  /**
   * Api delete config.
   *
   * @param id config id.
   * @return String
   */
  @DeleteMapping(value = "/{id}")
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EXECUTION_GROUP, action = PermissionActionEnum.DELETE)
  })
  ResponseData<String> deleteById(@PathVariable String id) throws BusinessException {
    executionGroupDependencyService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }
}
