package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.Max;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogFunctionEnum;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;

/**
 * SysLogRequest.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@FieldDefaults(level = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
public class SysLogRequest extends SysLogCursor {

  List<LogFunctionEnum> functions;

  List<LogActionEnum> actions;

  String fromDate;

  String toDate;

  List<String> userNames;

  String message;

  @Builder.Default
  @Max(value = CommonConstants.MAX_ALERT_EXPORT_ROWS_LIMIT, message = "Size cannot be greater than"
      + CommonConstants.MAX_ALERT_EXPORT_ROWS_LIMIT)
  int pageSize = 10;
}
