package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.CustomInputRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomInputEntity;

/**
 * CustomInputResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CustomInputEntityMapper extends KanbanBaseMapper<CustomInputEntity, CustomInputRequest> {
  CustomInputEntityMapper INSTANCE = Mappers.getMapper(CustomInputEntityMapper.class);

  /**
   * map from CustomInputRequest to CustomInputEntity.
   *
   * @param request CustomInputRequest.
   * @return CustomInputEntity.
   */
  @Mapping(target = "configuration", expression = "java(request.getConfiguration())")
  CustomInputEntity map(CustomInputRequest request);

  /**
   * map from CustomInputRequest to CustomInputEntity.
   *
   * @param entity  CustomInputEntity.
   * @param request CustomInputRequest.
   */
  @Mapping(target = "configuration", expression = "java(request.getConfiguration())")
  void merge(@MappingTarget CustomInputEntity entity, CustomInputRequest request);

}
