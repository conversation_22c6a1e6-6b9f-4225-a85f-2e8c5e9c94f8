package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.CollectionUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogFunctionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.SysLogCursor;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.SysLogRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.SysLogResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.SysLogRepositoryCustom;

/**
 * Implement SysLogRepositoryCustomImpl table SysLog.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SysLogRepositoryCustomImpl implements SysLogRepositoryCustom {
  SqlQueryUtil sqlQueryUtil;

  @Override
  public CursorPageResponse<SysLogResponse, SysLogCursor> findAll(SysLogRequest request) {
    var query = new PrepareQuery(
        """
            SELECT sysLog.ID,
                   sysLog.FUNCTION,
                   sysLog.ACTION,
                   sysLog.LOG_BY,
                   sysLog.LOG_DATE,
                   sysLog.MESSAGE
            FROM SYS_LOG sysLog
            WHERE 1 = 1
            """
    )
        .append(buildFunctionIn(request.getFunctions()))
        .append(buildActionIn(request.getActions()))
        .append(buildQueryRangeDate(request.getFromDate(), request.getToDate()))
        .append(buildUserNameIn(request.getUserNames()))
        .append(buildQueryMessageLike(request.getMessage()), LikeMatcher.CONTAINING)
        .append(buildQueryCursor(request.getCursorSysLogId(), request.getCursorSysLogCreatedDate()))
        .append(" ORDER BY sysLog.CREATED_DATE DESC, sysLog.ID DESC")
        .append(" FETCH FIRST " + (request.getPageSize() + 1) + " ROWS ONLY ");

    // lấy dư 1 phần tử để xem có page tiếp theo không.
    var result = sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), SysLogResponse.class);
    if (result.size() > request.getPageSize()) {
      return new CursorPageResponse<>(result.subList(0, request.getPageSize()),
          new SysLogCursor(result.get(result.size() - 2).getId(), result.get(result.size() - 2).getLogDate())
      );
    }
    return new CursorPageResponse<>(result, null);
  }

  PrepareQuery buildFunctionIn(List<LogFunctionEnum> functions) {
    if (CollectionUtils.isEmpty(functions)) {
      return null;
    }
    return new PrepareQuery(" AND sysLog.FUNCTION IN (:functions) ", "functions",
        functions.stream().map(LogFunctionEnum::name).toList());
  }

  PrepareQuery buildActionIn(List<LogActionEnum> actions) {
    if (CollectionUtils.isEmpty(actions)) {
      return null;
    }
    return new PrepareQuery(" AND sysLog.ACTION IN (:actions) ", "actions",
        actions.stream().map(LogActionEnum::name).toList());
  }

  private PrepareQuery buildUserNameIn(List<String> userNames) {
    if (CollectionUtils.isEmpty(userNames)) {
      return null;
    }
    return new PrepareQuery(" AND sysLog.LOG_BY in (:userNames) ", "userNames",
        userNames);
  }

  PrepareQuery buildQueryCursor(String cursorSysLogId, String cursorCreatedDate) {
    if (Objects.isNull(cursorSysLogId) || StringUtils.isBlank(cursorCreatedDate)) {
      return null;
    }
    return new PrepareQuery("""
        AND (sysLog.CREATED_DATE < :createdDate
          OR (sysLog.CREATED_DATE = :createdDate AND sysLog.ID < :sysLogId))
        """, Map.of("createdDate", Timestamp.valueOf(cursorCreatedDate),
        "sysLogId",
        cursorSysLogId));
  }

  PrepareQuery buildQueryRangeDate(String fromDate, String toDate) {
    if (StringUtils.isBlank(fromDate) || StringUtils.isBlank(toDate)) {
      return null;
    }
    return new PrepareQuery(" AND sysLog.LOG_DATE BETWEEN :fromDate AND :toDate ",
        Map.of("fromDate", DateUtils.convertStringToDate(fromDate),
            "toDate", DateUtils.convertStringToDate(toDate)));
  }

  PrepareQuery buildQueryMessageLike(String message) {
    if (StringUtils.isBlank(message)) {
      return null;
    }
    return new PrepareQuery(" AND LOWER(sysLog.MESSAGE) LIKE :message", "message", message.toLowerCase());

  }

}
