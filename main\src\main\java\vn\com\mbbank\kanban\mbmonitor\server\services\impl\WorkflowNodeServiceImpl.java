package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowEdgeModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowNodeModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.EmailNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.ExecutionNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowNodeDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowNodeEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.WorkflowNodeRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.WorkflowNodeDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.WorkflowNodeService;

@Service
@RequiredArgsConstructor
public class WorkflowNodeServiceImpl extends BaseServiceImpl<WorkflowNodeEntity, String>
    implements WorkflowNodeService {
  private final WorkflowNodeRepository workflowNodeRepository;
  private final WorkflowNodeDependencyService workflowNodeDependencyService;

  @Override
  protected JpaCommonRepository<WorkflowNodeEntity, String> getRepository() {
    return workflowNodeRepository;
  }

  @Override
  @Transactional
  public int deleteAllByWorkflowId(String workflowId) {
    var res = workflowNodeRepository.deleteAllByWorkflowId(workflowId);
    workflowNodeDependencyService.deleteAllByWorkflowId(workflowId);
    return res;
  }

  @Override
  public void save(String workflowId, WorkflowModel workflow) throws BusinessException {
    List<WorkflowNodeEntity> nodes = new ArrayList<>();
    List<WorkflowNodeDependencyEntity> dependencies = new ArrayList<>();

    var newNodeIdMap = workflow.getNodes().stream()
        .collect(Collectors.toMap(WorkflowNodeModel::getId, (node) -> GeneratorUtil.generateId()));

    Map<String, Set<String>> incomingNodeMap = new HashMap<>();
    Map<String, Set<String>> outgoingNodeMap = new HashMap<>();

    for (WorkflowEdgeModel edge : workflow.getEdges()) {
      var source = edge.getSource();
      var target = edge.getTarget();
      if (newNodeIdMap.containsKey(source) && newNodeIdMap.containsKey(target)) {
        // Outgoing
        outgoingNodeMap
            .computeIfAbsent(edge.getSource(), k -> new HashSet<>())
            .add(newNodeIdMap.get(edge.getTarget()));

        // Incoming
        incomingNodeMap
            .computeIfAbsent(edge.getTarget(), k -> new HashSet<>())
            .add(newNodeIdMap.get(edge.getSource()));
      }
    }

    for (WorkflowNodeModel node : workflow.getNodes()) {
      var newNode = new WorkflowNodeEntity();
      var configuration = node.getConfiguration();
      var nodeId = newNodeIdMap.get(node.getId());
      newNode.setId(nodeId);
      newNode.setWorkflowId(workflowId);
      newNode.setConfiguration(configuration);
      newNode.setPosition(node.getPosition());
      newNode.setStatus(node.getStatus());
      newNode.setType(node.getType());
      newNode.setIncomingNodeIds(incomingNodeMap.getOrDefault(node.getId(), Set.of()).stream().toList());
      newNode.setOutgoingNodeIds(outgoingNodeMap.getOrDefault(node.getId(), Set.of()).stream().toList());
      dependencies.addAll(getDependencies(node, workflowId, nodeId));
      nodes.add(newNode);
    }

    workflowNodeRepository.saveAll(nodes);
    workflowNodeDependencyService.saveAll(dependencies);
  }

  @Override
  public List<WorkflowNodeEntity> findAllByWorkflowId(String workflowId) {
    return workflowNodeRepository.findAllByWorkflowId(workflowId);
  }

  private List<WorkflowNodeDependencyEntity> getDependencies(WorkflowNodeModel node, String workflowId,
                                                             String nodeId) {
    var configuration = node.getConfiguration();
    if (configuration instanceof EmailNodeConfigurationModel emailConfiguration) {
      return emailConfiguration.getEmailTemplateIds().stream().map(emailTemplateId -> {
        var dependency = new WorkflowNodeDependencyEntity();
        dependency.setId(GeneratorUtil.generateId());
        dependency.setWorkflowId(workflowId);
        dependency.setWorkflowNodeId(nodeId);
        dependency.setType(WorkflowNodeTypeEnum.EMAIL);
        dependency.setReferenceId(emailTemplateId);
        return dependency;
      }).toList();
    } else if (configuration instanceof ExecutionNodeConfigurationModel executionConfiguration) {
      return executionConfiguration.getExecutionIds().stream().map(executionId -> {
        var dependency = new WorkflowNodeDependencyEntity();
        dependency.setId(GeneratorUtil.generateId());
        dependency.setWorkflowId(workflowId);
        dependency.setWorkflowNodeId(nodeId);
        dependency.setType(WorkflowNodeTypeEnum.EXECUTION);
        dependency.setReferenceId(executionId);
        return dependency;
      }).toList();
    }
    return new ArrayList<>();
  }
}
