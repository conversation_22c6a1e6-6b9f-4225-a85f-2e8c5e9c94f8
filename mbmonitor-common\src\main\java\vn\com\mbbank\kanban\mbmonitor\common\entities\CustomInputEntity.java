package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.converter.FormBuilderInputElementConverter;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.BaseFormBuilderInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.FormBuilderElementTypeEnum;

@Entity
@Data
@Table(name = TableName.CUSTOM_INPUT)
@EqualsAndHashCode(callSuper = true)
@KanbanAutoGenerateUlId
public class CustomInputEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "NAME")
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private FormBuilderElementTypeEnum type;

  @Column(name = "CONFIGURATION")
  @Convert(converter = FormBuilderInputElementConverter.class)
  private BaseFormBuilderInputElementModel configuration;

  @Override
  public String getId() {
    return id;
  }
}
