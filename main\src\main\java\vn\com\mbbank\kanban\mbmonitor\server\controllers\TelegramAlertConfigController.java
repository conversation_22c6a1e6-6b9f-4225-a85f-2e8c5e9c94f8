package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram.TelegramAlertConfigModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.TelegramAlertConfigDetailRequest;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TelegramConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TelegramConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.TelegramConfigResponseToTelegramConfigModelMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.TelegramAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.TelegramConfigService;
/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */

@RestController
@RequestMapping(ServerUrl.TELEGRAM_ALERT_CONFIG_URL)
@RequiredArgsConstructor
public class TelegramAlertConfigController extends BaseController {
  final TelegramAlertConfigService telegramAlertConfigService;
  final TelegramConfigService telegramConfigService;
  final CommonKafkaProducerService commonKafkaProducerService;

  /**
   * Api save config telegram.
   *
   * @param body body
   * @return status
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.TELEGRAM_ALERT_CONFIG, action = PermissionActionEnum.CONFIG),
  })
  @PostMapping()
  public ResponseData<String> save(@RequestBody TelegramAlertConfigDetailRequest body)
      throws BusinessException {
    telegramAlertConfigService.save(body);
    return ResponseUtils.success("OK");
  }

  /**
   * Find all service with config telegram.
   *
   * @param request request page
   * @return page TelegramAlertConfigModel
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.TELEGRAM_ALERT_CONFIG, action = PermissionActionEnum.CONFIG),
  })
  @GetMapping("/services")
  public ResponseData<Page<TelegramAlertConfigModel>> findAllServices(HttpServletRequest request)
      throws BusinessException {
    return ResponseUtils.success(
        telegramAlertConfigService.findServicesWithPage(getPaging(request)));
  }

  /**
   * Find all config application  by service id.
   *
   * @param serviceId serviceId
   * @param request page
   * @return page TelegramAlertConfigModel
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.TELEGRAM_ALERT_CONFIG, action = PermissionActionEnum.CONFIG),
  })
  @GetMapping("/services/{serviceId}/applications")
  public ResponseData<Page<TelegramAlertConfigModel>> findApplicationByServiceIdWithPage(
      @PathVariable String serviceId, HttpServletRequest request)
      throws BusinessException {
    return ResponseUtils.success(
        telegramAlertConfigService.findApplicationByServiceIdWithPage(serviceId, getPaging(request)));
  }


  /**
   * Get config alert telegram.
   *
   * @return telegram config
   * @throws BusinessException ex
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.TELEGRAM_ALERT_CONFIG, action = PermissionActionEnum.CONFIG),
  })
  @GetMapping
  public ResponseData<TelegramConfigResponse> findConfig() {
    return ResponseUtils.success(
        TelegramConfigResponseToTelegramConfigModelMapper.INSTANCE.map(
            telegramConfigService.findFirstByType(TelegramConfigTypeEnum.ALERT)));
  }

}
