package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;


import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CollectEmailConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.enums.CollectEmailAlertContentTypeEnum;

/**
 * Model response collect email config .
 */
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CollectEmailConfigResponse {
  Long id;
  String name;
  String description;
  Long emailConfigIntervalTime;
  Long emailConfigId;
  String emailConfigEmail;
  RuleGroupType ruleGroup;
  CollectEmailConfigTypeEnum type;
  Long absenceInterval;
  Long alertRepeatInterval;
  String serviceId;
  String serviceName;
  String applicationId;
  String applicationName;
  Long priorityConfigId;
  String recipient;
  CollectEmailAlertContentTypeEnum contentType;
  String content;
  String contentValue;
  boolean active;
}
