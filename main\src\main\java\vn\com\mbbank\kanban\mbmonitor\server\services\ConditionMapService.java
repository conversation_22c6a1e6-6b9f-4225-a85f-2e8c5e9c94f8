package vn.com.mbbank.kanban.mbmonitor.server.services;

import java.util.List;
import java.util.Set;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

/**
 * Service interface for map condition.
 *
 */
public interface ConditionMapService {

  /**
   * Replaces custom object or priority IDs in the given RuleGroupType with their corresponding names,
   * and returns the string representation of the rule group after replacement.
   *
   * @param logRuleGroup  The rule group to process.
   * @param clazz         The class used to determine which custom object IDs to map to names (optional).
   * @param isPriorityMap Whether to also map priority IDs to names.
   * @param fieldMap      A set of field names to extract custom object IDs for name mapping (optional).
   * @return A string representation of the rule group with IDs replaced by names, or null if input is null.
   */
  String mapNameCondition(RuleGroupType logRuleGroup, Class<?> clazz, boolean isPriorityMap, Set<String> fieldMap);

  /**
   * Processes a list of RuleGroupType objects by replacing custom object and priority IDs
   * with their corresponding names, and returns the updated string representations.
   *
   * @param logRuleGroup     The list of rule groups to process.
   * @param clazz            The class used to extract custom object IDs for name mapping (optional).
   * @param isPriorityMap    Whether to also map priority IDs to names.
   * @param fieldMap         A set of field names to extract custom object IDs for name mapping (optional).
   * @return A list of string representations for each rule group after name replacement.
   */
  List<String> mapNameListCondition(List<RuleGroupType> logRuleGroup,
                                     Class<?> clazz,
                                     boolean isPriorityMap,
                                     Set<String> fieldMap);
}
