package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.BaseFormBuilderInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.CheckboxElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.DateTimeElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.NumberInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.RadioElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.RowElement;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.SectionElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.SelectElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.TableInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.TextInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.TextareaInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.TimeDurationElementModel;

/**
 * Mapper interface for mapping between `FormBuilderInputElementMapper` and `FormBuilderInputElementMapper`.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FormBuilderElementMapper {

  FormBuilderElementMapper INSTANCE = Mappers.getMapper(FormBuilderElementMapper.class);

  /**
   * clone value.
   *
   * @param source source.
   * @return element
   */
  TextInputElementModel clone(TextInputElementModel source);

  /**
   * clone value.
   *
   * @param source source.
   * @return element
   */
  NumberInputElementModel clone(NumberInputElementModel source);

  /**
   * clone value.
   *
   * @param source source.
   * @return element
   */
  CheckboxElementModel clone(CheckboxElementModel source);

  /**
   * clone value.
   *
   * @param source source.
   * @return element
   */
  RadioElementModel clone(RadioElementModel source);

  /**
   * clone value.
   *
   * @param source source.
   * @return element
   */
  SelectElementModel clone(SelectElementModel source);

  /**
   * clone value.
   *
   * @param source source.
   * @return element
   */
  DateTimeElementModel clone(DateTimeElementModel source);

  /**
   * clone value.
   *
   * @param source source.
   * @return element
   */
  TimeDurationElementModel clone(TimeDurationElementModel source);

  /**
   * clone value.
   *
   * @param source source.
   * @return element
   */
  TableInputElementModel clone(TableInputElementModel source);

  /**
   * clone value.
   *
   * @param source source.
   * @return element
   */
  TextareaInputElementModel clone(TextareaInputElementModel source);

  /**
   * clone value.
   *
   * @param source source.
   * @return element
   */
  SectionElementModel clone(SectionElementModel source);

  /**
   * clone value.
   *
   * @param source source.
   * @return element
   */
  RowElement clone(RowElement source);

  /**
   * clone value.
   *
   * @param source source.
   * @return element
   */
  default BaseFormBuilderInputElementModel clone(BaseFormBuilderInputElementModel source) {
    if (source instanceof TextInputElementModel text) {
      return clone(text);
    } else if (source instanceof NumberInputElementModel number) {
      return clone(number);
    } else if (source instanceof CheckboxElementModel checkbox) {
      return clone(checkbox);
    } else if (source instanceof RadioElementModel radio) {
      return clone(radio);
    } else if (source instanceof SelectElementModel select) {
      return clone(select);
    } else if (source instanceof DateTimeElementModel datetime) {
      return clone(datetime);
    } else if (source instanceof TimeDurationElementModel range) {
      return clone(range);
    } else if (source instanceof TableInputElementModel table) {
      return clone(table);
    } else if (source instanceof TextareaInputElementModel textarea) {
      return clone(textarea);
    } else {
      return null;
    }
  }
}
