package vn.com.mbbank.kanban.mbmonitor.server.controllers;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NoteEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ListBodyRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.NoteRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.NoteResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.NoteEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.NoteResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.NoteService;

/**
 * Controller logic note.
 */
@RestController
@RequestMapping(ServerUrl.NOTE_URL)
@RequiredArgsConstructor
public class NoteController {

  private final NoteService noteService;
  private final NoteEntityMapper noteEntityMapper = NoteEntityMapper.INSTANCE;
  private final NoteResponseMapper noteResponseMapper = NoteResponseMapper.INSTANCE;

  /**
   * find all note by alertId.
   *
   * @param alertGroupId id of alert
   * @return Result list comment
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MONITOR_ALERT, action = PermissionActionEnum.VIEW)
  })
  @GetMapping()
  ResponseData<List<NoteResponse>> findAllByAlertGroupId(
      @RequestParam("alertGroupId") Long alertGroupId) {
    return ResponseUtils.success(
        noteResponseMapper.map(noteService.findAllByAlertGroupId(alertGroupId)));
  }

  /**
   * create note.
   *
   * @param noteRequests notes info
   * @return List result created note
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.MONITOR_ALERT, action = PermissionActionEnum.COMMENT)
  })
  @PostMapping
  ResponseData<List<NoteResponse>> saveAll(@RequestBody ListBodyRequest<NoteRequest> noteRequests) {
    List<NoteEntity> noteEntities =
        noteRequests.getData().stream().map(noteEntityMapper::map).toList();
    return ResponseUtils.success(noteResponseMapper.map(noteService.saveAll(noteEntities)));
  }
}
