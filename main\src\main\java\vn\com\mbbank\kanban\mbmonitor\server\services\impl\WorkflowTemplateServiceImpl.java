package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.BaseFormBuilderElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.BaseFormBuilderInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.CheckboxElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.DateTimeElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.NumberInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.OptionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.RadioElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.RowElement;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.SectionElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.SelectElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.TextInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.layout.TextareaInputElementModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowEdgeModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.WorkflowNodeModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.EmailNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.ExecutionNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.workflow.configuration.TeamsNodeConfigurationModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.WorkflowTemplateRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.WorkflowTemplateResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomInputEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WorkflowTemplateEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WorkflowNodeTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.FormBuilderElementMapper;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.WorkflowTemplateResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;
import vn.com.mbbank.kanban.mbmonitor.common.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.WorkflowTemplateEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.WorkflowTemplateRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.CustomInputService;
import vn.com.mbbank.kanban.mbmonitor.server.services.EmailTemplateService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionService;
import vn.com.mbbank.kanban.mbmonitor.server.services.WorkflowService;
import vn.com.mbbank.kanban.mbmonitor.server.services.WorkflowTemplateService;

@Service
@RequiredArgsConstructor
public class WorkflowTemplateServiceImpl extends BaseServiceImpl<WorkflowTemplateEntity, String>
    implements WorkflowTemplateService {
  private final WorkflowTemplateRepository workflowTemplateRepository;
  private final CustomInputService customInputService;
  private final WorkflowService workflowService;
  private final EmailTemplateService emailTemplateService;
  private final ExecutionService executionService;
  private final WorkflowTemplateEntityMapper workflowTemplateEntityMapper = WorkflowTemplateEntityMapper.INSTANCE;
  private final FormBuilderElementMapper formBuilderElementMapper = FormBuilderElementMapper.INSTANCE;
  private final WorkflowTemplateResponseMapper workflowTemplateResponseMapper = WorkflowTemplateResponseMapper.INSTANCE;

  @Override
  protected JpaCommonRepository<WorkflowTemplateEntity, String> getRepository() {
    return workflowTemplateRepository;
  }

  @Override
  public WorkflowTemplateResponse findWithId(String id) throws BusinessException {
    var workflowTemplate = workflowTemplateRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_NOT_FOUND));
    workflowTemplate.setFormLayout(getLayoutInfo(workflowTemplate.getFormLayout()));
    var res = workflowTemplateResponseMapper.map(workflowTemplate);
    res.setWorkflow(workflowService.findWithId(workflowTemplate.getWorkflowId()));
    return res;
  }

  @Override
  @Transactional
  public WorkflowTemplateEntity createOrUpdate(WorkflowTemplateRequest request) throws BusinessException {
    var id = request.getId();
    var name = StringUtils.capitalizeFirstLetter(request.getName().trim());
    var isCreateMode = KanbanCommonUtil.isEmpty(id);
    validateRequest(request);
    WorkflowTemplateEntity workflowTemplate = null;
    if (isCreateMode) {
      workflowTemplate = workflowTemplateEntityMapper.map(request);
      workflowTemplate.setId(GeneratorUtil.generateId());
    } else {
      workflowTemplate = workflowTemplateRepository.findById(id)
          .orElseThrow(() -> new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_NOT_FOUND));
      workflowTemplateEntityMapper.merge(workflowTemplate, request);

      // delete old nodes
      workflowService.deleteWithId(workflowTemplate.getWorkflowId());
    }
    workflowTemplate.setName(name);

    // create new nodes
    var workflow = workflowService.create(request.getWorkflow());
    workflowTemplate.setWorkflowId(workflow.getId());

    return workflowTemplateRepository.save(workflowTemplate);
  }

  @Override
  public void deleteWithId(String id) throws BusinessException {
    var customInput = workflowTemplateRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_NOT_FOUND));
    workflowTemplateRepository.delete(customInput);
    // delete old node
    workflowService.deleteWithId(customInput.getId());
  }

  private List<BaseFormBuilderElementModel> getLayoutInfo(List<BaseFormBuilderElementModel> elements) {
    var customInputs = customInputService.findAllById(getCustomInputIds(elements));
    var customInputMap = customInputs.stream().collect(Collectors.toMap(CustomInputEntity::getId, Function.identity()));
    var response = new ArrayList<BaseFormBuilderElementModel>();
    for (BaseFormBuilderElementModel element : elements) {
      if (element instanceof SectionElementModel section) {
        response.add(getSectionElement(section, customInputMap));
      } else if (element instanceof BaseFormBuilderInputElementModel input) {
        getInputElement(input, customInputMap).ifPresent(response::add);
      }
    }
    return response;
  }

  private SectionElementModel getSectionElement(SectionElementModel section,
                                                Map<String, CustomInputEntity> customInputMap) {
    List<RowElement> rows = new ArrayList<>();
    for (RowElement row : section.getRows()) {
      var cloneRow = formBuilderElementMapper.clone(row);
      List<BaseFormBuilderInputElementModel> elements = new ArrayList<>();
      for (BaseFormBuilderInputElementModel input : row.getElements()) {
        getInputElement(input, customInputMap).ifPresent(elements::add);
      }
      if (!elements.isEmpty()) {
        cloneRow.setElements(elements);
        rows.add(cloneRow);
      }
    }
    var cloneSection = formBuilderElementMapper.clone(section);
    cloneSection.setRows(rows);
    return cloneSection;
  }

  private Optional<BaseFormBuilderInputElementModel> getInputElement(BaseFormBuilderInputElementModel input,
                                                                     Map<String, CustomInputEntity> customInputMap) {
    var customInputId = input.getCustomInputId();
    if (!KanbanCommonUtil.isEmpty(customInputId)) {
      if (customInputMap.containsKey(customInputId)) {
        var cloneConfiguration =
            formBuilderElementMapper.clone(customInputMap.get(customInputId).getConfiguration());
        cloneConfiguration.setLabel(input.getLabel());
        cloneConfiguration.setId(input.getId());
        return Optional.of(cloneConfiguration);
      } else {
        // Ignore not found customInput
        return Optional.empty();
      }
    }
    return Optional.of(formBuilderElementMapper.clone(input));
  }

  private List<String> getCustomInputIds(List<BaseFormBuilderElementModel> elements) {
    Set<String> ids = new HashSet<>();

    for (BaseFormBuilderElementModel element : elements) {
      if (element instanceof SectionElementModel section) {
        for (var row : section.getRows()) {
          for (var child : row.getElements()) {
            if (!KanbanCommonUtil.isEmpty(child.getCustomInputId())) {
              ids.add(child.getCustomInputId());
            }
          }
        }
      } else if (element instanceof BaseFormBuilderInputElementModel input
          && !KanbanCommonUtil.isEmpty(input.getCustomInputId())) {
        ids.add(input.getCustomInputId());
      }
    }

    return new ArrayList<>(ids);
  }

  private void validateRequest(WorkflowTemplateRequest request) throws BusinessException {
    var id = request.getId();
    var name = StringUtils.capitalizeFirstLetter(request.getName().trim());
    var isCreateMode = KanbanCommonUtil.isEmpty(id);
    if (!isCreateMode) {
      workflowTemplateRepository.findById(id)
          .orElseThrow(() -> new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_NOT_FOUND));
    }

    var isNameExisted =
        isCreateMode ? workflowTemplateRepository.existsByNameIgnoreCase(name) :
            workflowTemplateRepository.existsByIdNotAndNameIgnoreCase(request.getId(), name);

    // Validate name is exist
    if (isNameExisted) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_NAME_IS_EXISTED);
    }

    // Validate workflow
    validateWorkflow(request.getWorkflow());

    // Validate form builder
    validateFormLayout(request.getFormLayout());
  }

  /**
   * Validates the workflow structure and node configurations.
   *
   * @param workflow the workflow to validate
   * @throws BusinessException if validation fails
   */
  private void validateWorkflow(WorkflowModel workflow) throws BusinessException {
    if (KanbanCommonUtil.isEmpty(workflow)) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_WORKFLOW_CAN_NOT_BE_EMPTY);
    }

    List<WorkflowNodeModel> nodes = workflow.getNodes();
    List<WorkflowEdgeModel> edges = workflow.getEdges();

    for (WorkflowNodeModel node : nodes) {
      // 1. Validate node have labels
      validateNodeLabel(node);

      // 2. Validate nodes configuration
      validateNodeConfiguration(node);
    }

    // 3. Validate start and end nodes
    validateStartAndEndNodes(nodes, edges);

    // 4. Validate no cycles
    validateNoCycles(nodes, edges);
  }

  /**
   * Validates the form layout structure and element configurations.
   *
   * @param formLayout the formLayout to validate
   * @throws BusinessException if validation fails
   */
  private void validateFormLayout(List<BaseFormBuilderElementModel> formLayout) throws BusinessException {
    if (KanbanCommonUtil.isEmpty(formLayout)) {
      return; // Empty form layout is allowed
    }

    for (BaseFormBuilderElementModel element : formLayout) {
      validateFormElement(element);
    }
  }

  /**
   * Validates a single form element.
   *
   * @param element the form element to validate
   * @throws BusinessException if validation fails
   */
  private void validateFormElement(BaseFormBuilderElementModel element) throws BusinessException {
    // Validate label is not empty and max length 100
    String label = element.getLabel();
    if (KanbanCommonUtil.isEmpty(label)) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_FORM_ELEMENT_MISSING_LABEL);
    }

    if (label.length() > CommonConstants.WORKFLOW_TEMPLATE_FORM_ITEM_LABEL_MAX_LENGTH) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_FORM_ELEMENT_LABEL_TOO_LONG);
    }

    // Validate placeholder length for elements that have placeholders
    validateElementPlaceholder(element);

    // Validate options for elements that have options
    validateElementOptions(element);

    // If element is a section, validate its nested elements
    if (element instanceof SectionElementModel section) {
      validateSectionElement(section);
    }
  }

  /**
   * Validates placeholder length for form elements that have placeholders.
   *
   * @param element the form element to validate
   * @throws BusinessException if validation fails
   */
  private void validateElementPlaceholder(BaseFormBuilderElementModel element) throws BusinessException {
    String placeholder = null;

    if (element instanceof TextInputElementModel textElement) {
      placeholder = textElement.getPlaceholder();
    } else if (element instanceof TextareaInputElementModel textareaElement) {
      placeholder = textareaElement.getPlaceholder();
    } else if (element instanceof NumberInputElementModel numberElement) {
      placeholder = numberElement.getPlaceholder();
    } else if (element instanceof DateTimeElementModel dateTimeElement) {
      placeholder = dateTimeElement.getPlaceholder();
    } else if (element instanceof SelectElementModel selectElement) {
      placeholder = selectElement.getPlaceholder();
    }

    if (!KanbanCommonUtil.isEmpty(placeholder)
        && placeholder.length() > CommonConstants.WORKFLOW_TEMPLATE_FORM_ITEM_PLACEHOLDER_MAX_LENGTH) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_FORM_ELEMENT_PLACEHOLDER_TOO_LONG);
    }
  }

  /**
   * Validates options for form elements that have options.
   *
   * @param element the form element to validate
   * @throws BusinessException if validation fails
   */
  private void validateElementOptions(BaseFormBuilderElementModel element) throws BusinessException {
    List<OptionModel> options = null;

    if (element instanceof CheckboxElementModel checkboxElement) {
      options = checkboxElement.getOptions();
    } else if (element instanceof RadioElementModel radioElement) {
      options = radioElement.getOptions();
    } else if (element instanceof SelectElementModel selectElement) {
      options = selectElement.getOptions();
    }

    if (options != null) {
      // Check that there is at least one option
      if (KanbanCommonUtil.isEmpty(options)) {
        throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_FORM_ELEMENT_MISSING_OPTIONS);
      }

      // Validate each option and check for duplicates
      Set<String> seenValues = new HashSet<>();
      Set<String> seenLabels = new HashSet<>();

      for (OptionModel option : options) {
        // Check that value is not empty
        if (KanbanCommonUtil.isEmpty(option.getValue()) || option.getValue().trim().isEmpty()) {
          throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_FORM_ELEMENT_OPTION_MISSING_VALUE);
        }

        // Check that label is not empty
        if (KanbanCommonUtil.isEmpty(option.getLabel()) || option.getLabel().trim().isEmpty()) {
          throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_FORM_ELEMENT_OPTION_MISSING_LABEL);
        }

        // Check for duplicate values
        if (seenValues.contains(option.getValue())) {
          throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_FORM_ELEMENT_DUPLICATE_OPTION_VALUE);
        }
        seenValues.add(option.getValue());

        // Check for duplicate labels
        if (seenLabels.contains(option.getLabel())) {
          throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_FORM_ELEMENT_DUPLICATE_OPTION_LABEL);
        }
        seenLabels.add(option.getLabel());
      }
    }
  }

  /**
   * Validates a section element and its nested rows and elements.
   *
   * @param section the section element to validate
   * @throws BusinessException if validation fails
   */
  private void validateSectionElement(SectionElementModel section) throws BusinessException {
    List<RowElement> rows = section.getRows();
    if (KanbanCommonUtil.isEmpty(rows)) {
      return; // Empty section is allowed
    }

    for (RowElement row : rows) {
      List<BaseFormBuilderInputElementModel> elements = row.getElements();
      if (!KanbanCommonUtil.isEmpty(elements)) {
        for (BaseFormBuilderInputElementModel nestedElement : elements) {
          validateFormElement(nestedElement); // Recursive validation
        }
      }
    }
  }

  /**
   * Validates that workflow has exactly one START and one END node,
   * and that they are properly connected.
   *
   * @param nodes the workflow nodes
   * @param edges the workflow edges
   * @throws BusinessException if validation fails
   */
  private void validateStartAndEndNodes(List<WorkflowNodeModel> nodes, List<WorkflowEdgeModel> edges)
      throws BusinessException {
    // Find START and END nodes
    List<WorkflowNodeModel> startNodes = nodes.stream()
        .filter(node -> WorkflowNodeTypeEnum.START.equals(node.getType()))
        .toList();

    List<WorkflowNodeModel> endNodes = nodes.stream()
        .filter(node -> WorkflowNodeTypeEnum.END.equals(node.getType()))
        .toList();

    if (startNodes.size() != 1 || endNodes.size() != 1) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_MISSING_START_OR_END_NODE);
    }

    if (KanbanCommonUtil.isEmpty(edges)) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_START_NODE_NOT_CONNECTED);
    }

    // Validate connections if edges exist
    String startNodeId = startNodes.get(0).getId();
    String endNodeId = endNodes.get(0).getId();

    // Check if START node connects to at least one other node
    boolean startNodeConnected = edges.stream()
        .anyMatch(edge -> startNodeId.equals(edge.getSource()));

    if (!startNodeConnected) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_START_NODE_NOT_CONNECTED);
    }

    // Check if END node is connected from at least one other node
    boolean endNodeConnected = edges.stream()
        .anyMatch(edge -> endNodeId.equals(edge.getTarget()));

    if (!endNodeConnected) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_END_NODE_NOT_CONNECTED);
    }
  }

  /**
   * Validates that workflow contains no cycles using DFS.
   *
   * @param nodes the workflow nodes
   * @param edges the workflow edges
   * @throws BusinessException if cycles are detected
   */
  private void validateNoCycles(List<WorkflowNodeModel> nodes, List<WorkflowEdgeModel> edges) throws BusinessException {
    // Build adjacency list
    Map<String, List<String>> adjacencyList = nodes.stream()
        .collect(Collectors.toMap(
            WorkflowNodeModel::getId,
            node -> new ArrayList<>()
        ));

    for (WorkflowEdgeModel edge : edges) {
      String source = edge.getSource();
      String target = edge.getTarget();
      if (adjacencyList.containsKey(source) && adjacencyList.containsKey(target)) {
        adjacencyList.get(source).add(target);
      }
    }

    // DFS cycle detection
    Set<String> visited = new HashSet<>();
    Set<String> recursionStack = new HashSet<>();

    for (String nodeId : adjacencyList.keySet()) {
      if (!visited.contains(nodeId)) {
        if (hasCycleDfs(nodeId, adjacencyList, visited, recursionStack)) {
          throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_CYCLE_DETECTED);
        }
      }
    }
  }

  /**
   * DFS helper method for cycle detection.
   *
   * @param nodeId         the current node
   * @param adjacencyList  the graph adjacency list
   * @param visited        the visited nodes set
   * @param recursionStack the current recursion stack
   * @return true if cycle is detected
   */
  private boolean hasCycleDfs(String nodeId, Map<String, List<String>> adjacencyList,
                              Set<String> visited, Set<String> recursionStack) {
    visited.add(nodeId);
    recursionStack.add(nodeId);

    List<String> neighbors = adjacencyList.get(nodeId);
    if (KanbanCommonUtil.isEmpty(neighbors)) {
      for (String neighbor : neighbors) {
        if (!visited.contains(neighbor)) {
          if (hasCycleDfs(neighbor, adjacencyList, visited, recursionStack)) {
            return true;
          }
        } else if (recursionStack.contains(neighbor)) {
          return true;
        }
      }
    }

    recursionStack.remove(nodeId);
    return false;
  }

  /**
   * Validates node configurations for email and execution nodes.
   *
   * @param node the workflow nodes
   * @throws BusinessException if validation fails
   */
  private void validateNodeConfiguration(WorkflowNodeModel node) throws BusinessException {
    if (WorkflowNodeTypeEnum.EMAIL.equals(node.getType())) {
      validateEmailNode(node);
    } else if (WorkflowNodeTypeEnum.EXECUTION.equals(node.getType())
        || WorkflowNodeTypeEnum.SQL.equals(node.getType())
        || WorkflowNodeTypeEnum.PYTHON.equals(node.getType())
        || WorkflowNodeTypeEnum.API.equals(node.getType())) {
      validateExecutionNode(node);
    } else if (WorkflowNodeTypeEnum.TEAMS.equals(node.getType())) {
      validateTeamsNode(node);
    }
  }

  /**
   * Validates email node configuration.
   *
   * @param node the email node
   * @throws BusinessException if validation fails
   */
  private void validateEmailNode(WorkflowNodeModel node) throws BusinessException {
    if (!(node.getConfiguration() instanceof EmailNodeConfigurationModel emailConfig)) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_EMAIL_NODE_INVALID);
    }

    List<String> emailTemplateIds = emailConfig.getEmailTemplateIds();
    if (KanbanCommonUtil.isEmpty(emailTemplateIds) && !emailConfig.getAllTemplates()) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_EMAIL_NODE_INVALID);
    }

    if (!emailConfig.getAllTemplates()) {
      // Validate that all email template IDs exist
      for (String emailTemplateIdStr : emailTemplateIds) {
        try {
          Long emailTemplateId = Long.parseLong(emailTemplateIdStr);
          emailTemplateService.findEmailTemplateById(emailTemplateId);
        } catch (NumberFormatException | BusinessException e) {
          throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_EMAIL_NODE_INVALID);
        }
      }
    }
  }

  /**
   * Validates execution node configuration.
   *
   * @param node the execution node
   * @throws BusinessException if validation fails
   */
  private void validateExecutionNode(WorkflowNodeModel node) throws BusinessException {
    if (!(node.getConfiguration() instanceof ExecutionNodeConfigurationModel executionConfig)) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_EXECUTION_NODE_INVALID);
    }

    List<String> executionIds = executionConfig.getExecutionIds();
    if (KanbanCommonUtil.isEmpty(executionIds) && !executionConfig.getAllExecutions()) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_EXECUTION_NODE_INVALID);
    }

    if (!executionConfig.getAllExecutions()) {
      // Validate that all execution IDs exist
      List<String> existingExecutionIds = executionService.findAllByIdIn(executionIds)
          .stream()
          .map(ExecutionEntity::getId)
          .toList();

      if (existingExecutionIds.size() != executionIds.size()) {
        throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_EXECUTION_NODE_INVALID);
      }
    }
  }

  /**
   * Validates that all workflow node have label.
   *
   * @param node the workflow node
   * @throws BusinessException if validation fails
   */
  private void validateNodeLabel(WorkflowNodeModel node) throws BusinessException {
    var configuration = node.getConfiguration();
    if (KanbanCommonUtil.isEmpty(configuration)
        || KanbanCommonUtil.isEmpty(configuration.getName())
        || configuration.getName().trim().length()
        >= CommonConstants.WORKFLOW_TEMPLATE_NODE_NAME_MAX_LENGTH) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_NODE_MISSING_LABEL);
    }
  }

  /**
   * Validates TEAMS node configuration.
   *
   * @param node the workflow node
   * @throws BusinessException if validation fails
   */
  private void validateTeamsNode(WorkflowNodeModel node) throws BusinessException {
    if (!WorkflowNodeTypeEnum.TEAMS.equals(node.getType())) {
      return;
    }
    if (!(node.getConfiguration() instanceof TeamsNodeConfigurationModel messageConfig)) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_MESSAGE_NODE_INVALID_CONTACT);
    }

    // Validate contacts
    List<String> contacts = messageConfig.getContacts();
    if (KanbanCommonUtil.isEmpty(contacts)) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_MESSAGE_NODE_INVALID_CONTACT);
    }

    // Validate message
    String message = messageConfig.getMessage();
    if (KanbanCommonUtil.isEmpty(message)) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_MESSAGE_NODE_INVALID_MESSAGE);
    }

    if (message.length() > CommonConstants.WORKFLOW_TEMPLATE_TEAMS_NODE_MESSAGE_MAX_LENGTH) {
      throw new BusinessException(ErrorCode.WORKFLOW_TEMPLATE_MESSAGE_NODE_INVALID_MESSAGE);
    }
  }
}
