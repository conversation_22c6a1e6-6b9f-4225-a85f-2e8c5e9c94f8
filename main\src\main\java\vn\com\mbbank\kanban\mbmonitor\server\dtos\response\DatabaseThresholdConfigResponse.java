package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionOperatorEnum;

/**
 * Model request service to create or update database threshold config.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DatabaseThresholdConfigResponse {
  String id;
  String name;
  String description;
  Long databaseConnectionId;
  String databaseConnectionName;
  String sqlCommand;
  String cronTime;
  Long conditionValue;
  ConditionOperatorEnum conditionOperator;
  String serviceId;
  String serviceName;
  String applicationId;
  String applicationName;
  Long priorityId;
  String priorityConfigName;
  String recipient;
  String content;
  String contentJson;
  boolean active;
}
