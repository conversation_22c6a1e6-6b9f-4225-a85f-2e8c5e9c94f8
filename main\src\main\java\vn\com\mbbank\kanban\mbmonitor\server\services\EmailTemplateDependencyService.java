package vn.com.mbbank.kanban.mbmonitor.server.services;

import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailTemplateEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.EmailTemplateDependenciesResponse;

/**
 * interface logic EmailTemplateDependencyService.
 */
public interface EmailTemplateDependencyService extends BaseService<EmailTemplateEntity, Long> {
  /**
   * Deletes a email Template by its ID.
   *
   * @param id the ID of the ServiceEntity
   * @throws BusinessException if a business-related exception occurs
   */
  void deleteWithId(Long id) throws BusinessException;

  /**
   * find all dependencies by id in.
   *
   * @param id ID of emailTemplate.
   * @return a EmailTemplateDependenciesResponse.
   */
  EmailTemplateDependenciesResponse findAllDependenciesById(Long id);

}
