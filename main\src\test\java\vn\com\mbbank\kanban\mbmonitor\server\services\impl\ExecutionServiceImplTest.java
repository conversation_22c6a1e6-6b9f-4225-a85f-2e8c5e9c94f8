package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito .Mockito.verify;
import static org.mockito.Mockito.when;

import java.lang.reflect.UndeclaredThrowableException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ApiInfoRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionApiEntity;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ExternalExecutionUrl;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionApiMethodEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionStatusEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.antlr4.PythonParserUtils;
import vn.com.mbbank.kanban.mbmonitor.common.configs.QueryHikariDataSourceConfig;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.PythonAnalysisModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecuteScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.VariableRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecuteScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionParamEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.DatabaseQueryService;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.ExecutionEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ExecutionApiRepository;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ExecutionGroupRepository;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ExecutionRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AutoTriggerActionConfigDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AutoTriggerActionConfigExecutionMapService;
import vn.com.mbbank.kanban.mbmonitor.server.services.DatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionApiService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionHistoryService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionParamService;
import vn.com.mbbank.kanban.mbmonitor.server.services.VariableService;
import vn.com.mbbank.kanban.mbmonitor.server.services.SysPermissionService;


@ExtendWith(MockitoExtension.class)
class ExecutionServiceImplTest {

  @Mock
  private ExecutionRepository executionRepository;
  @Mock
  private ExecutionEntityMapper executionEntityMapper;

  @Mock
  private ExecutionGroupRepository executionGroupRepository;

  @Mock
  private ExecutionParamService executionParamService;

  @Mock
  private VariableService variableService;

  @Mock
  private DatabaseConnectionService databaseConnectionService;

  @Mock
  private QueryHikariDataSourceConfig queryHikariDataSourceConfig;

  @Mock
  private DatabaseQueryService databaseQueryService;

  @Mock
  private ExecutionHistoryService executionHistoryService;

  @Mock
  private SysPermissionService sysPermissionService;

  @Mock
  private CommonAclPermissionService commonAclPermissionService;

  @Mock
  private DatabaseQueryService superiorsService;
  @Mock
  AutoTriggerActionConfigDependencyService autoTriggerActionConfigDependencyService;
  @Mock
  private  AutoTriggerActionConfigExecutionMapService triggerExecutionMapService;
  @Mock
  private ExecutionApiService executionApiService;
  @Mock
  private ExecutionApiRepository executionApiRepository;

  @Mock
  private ExecutionDependencyService executionDependencyService;

  @Mock
  private RestTemplate restTemplateKeycloakCentralized;

  @InjectMocks
  private ExecutionServiceImpl executionService;


  private ExecutionEntity executionEntity;
  private ExecutionRequest executionRequest;
  private ExecutionResponse executionResponse;

  private ExecuteScriptRequest executeScriptRequest;
  private ExecuteScriptResponse executeScriptResponse;
  private ExecutionGroupEntity executionGroupEntity;
  private DatabaseConnectionEntity databaseConnectionEntity;
  private List<ExecutionParamEntity> executionParamEntities;
  private List<VariableEntity> variableEntities;
  private String testId;
  private String groupId;
  private Long dbConnectionId;

  @BeforeEach
  void setUp() {
    // Arrange common test data
    testId = "test-id-123";
    groupId = "group-id-456";
    dbConnectionId = 789L;

    // Setup ExecutionEntity
    executionEntity = new ExecutionEntity();
    executionEntity.setId(testId);
    executionEntity.setName("Test Execution");
    executionEntity.setDescription("Test Description");
    executionEntity.setType(ExecutionTypeEnum.SQL);
    executionEntity.setDatabaseConnectionId(dbConnectionId);
    executionEntity.setExecutionGroupId(groupId);
    executionEntity.setScript("SELECT * FROM test_table");

    // Setup ExecutionRequest
    executionRequest = ExecutionRequest.builder()
        .id(testId)
        .name("Test Execution")
        .description("Test Description")
        .type(ExecutionTypeEnum.SQL)
        .databaseConnectionId(dbConnectionId)
        .executionGroupId(groupId)
        .script("SELECT * FROM test_table")
        .build();

    // Setup ExecutionResponse
    executionResponse = ExecutionResponse.builder()
        .id(testId)
        .name("Test Execution")
        .description("Test Description")
        .type(ExecutionTypeEnum.SQL)
        .databaseConnectionId(dbConnectionId)
        .executionGroupId(groupId)
        .script("SELECT * FROM test_table")
        .build();

    // Setup ExecuteScriptRequest
    executeScriptRequest = ExecuteScriptRequest.builder()
        .executionId(testId)
        .variables(new ArrayList<>())
        .paginationRequest(new PaginationRequest())
        .name("Test Execution")
        .description("Test Description")
        .type(ExecutionTypeEnum.SQL)
        .databaseConnectionId(dbConnectionId)
        .executionGroupId(groupId)
        .script("SELECT * FROM test_table")
        .build();

    // Setup ExecuteScriptResponse
    executeScriptResponse = ExecuteScriptResponse.builder()
        .id(testId)
        .name("Test Execution")
        .scriptResponse("Script executed successfully")
        .build();

    // Setup ExecutionGroupEntity
    executionGroupEntity = new ExecutionGroupEntity();
    executionGroupEntity.setId(groupId);
    executionGroupEntity.setName("Test Group");
    executionGroupEntity.setDescription("Test Group Description");

    // Setup DatabaseConnectionEntity
    databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setId(dbConnectionId);
    databaseConnectionEntity.setName("Test Connection");
    databaseConnectionEntity.setIsActive(true);

    // Setup ExecutionParamEntities
    executionParamEntities = new ArrayList<>();
    ExecutionParamEntity paramEntity = new ExecutionParamEntity();
    paramEntity.setId("param-id-1");
    paramEntity.setName("param1");
    paramEntity.setExecutionId(testId);
    executionParamEntities.add(paramEntity);

    // Setup VariableEntities
    variableEntities = new ArrayList<>();
    VariableEntity variableEntity = new VariableEntity();
    variableEntity.setId("var-id-1");
    variableEntity.setName("param1");
    variableEntity.setValue("value1");
    variableEntity.setHidden(false);
    variableEntities.add(variableEntity);

    ReflectionTestUtils.setField(executionService, "executeTimeOut", 30L);

    // Inject the mocked RestTemplate
    ReflectionTestUtils.setField(executionService, "restTemplateKeycloakCentralized", restTemplateKeycloakCentralized);

    queryHikariDataSourceConfig = new QueryHikariDataSourceConfig();
    ReflectionTestUtils.setField(queryHikariDataSourceConfig, "minimumIdle", 5);
    ReflectionTestUtils.setField(queryHikariDataSourceConfig, "maximumPoolSize", 10);
    ReflectionTestUtils.setField(queryHikariDataSourceConfig, "idleTimeout", 30000);
    ReflectionTestUtils.setField(queryHikariDataSourceConfig, "maxLifetime", 1800000);
    ReflectionTestUtils.setField(queryHikariDataSourceConfig, "connectionTimeout", 30000);
    ReflectionTestUtils.setField(queryHikariDataSourceConfig, "poolName", "TestPool");
  }

  @Test
  void findAllByExecutionGroupId_success() {
    // Arrange
    List<ExecutionEntity> expectedList = Collections.singletonList(executionEntity);
    when(executionRepository.findAllByExecutionGroupId(groupId)).thenReturn(expectedList);
    // Act
    List<ExecutionResponse> result = executionService.findAllByExecutionGroupId(groupId);

    // Assert
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals(testId, result.get(0).getId());
    verify(executionRepository, times(1)).findAllByExecutionGroupId(groupId);
  }

  @Test
  void findAll_success() {
    // Arrange
    PaginationRequestDTO requestDTO = new PaginationRequestDTO();
    Page<ExecutionResponse> expectedPage = new PageImpl<>(Collections.singletonList(executionResponse));
    when(executionRepository.findAll(requestDTO)).thenReturn(expectedPage);

    // Act
    Page<ExecutionResponse> result = executionService.findAll(requestDTO);

    // Assert
    assertNotNull(result);
    assertEquals(1, result.getTotalElements());
    assertEquals(testId, result.getContent().get(0).getId());
    verify(executionRepository, times(1)).findAll(requestDTO);
  }

  @Test
  void createOrUpdate_success_whenCreate() throws BusinessException {
    // Arrange
    executionRequest.setId(null); // Create mode
    when(executionGroupRepository.findById(groupId)).thenReturn(Optional.of(executionGroupEntity));
    when(databaseConnectionService.findById(dbConnectionId)).thenReturn(databaseConnectionEntity);
    when(executionRepository.existsByNameIgnoreCaseAndExecutionGroupId(anyString(), anyString())).thenReturn(false);
    when(executionRepository.save(any(ExecutionEntity.class))).thenReturn(executionEntity);

    // Act
    ExecutionEntity result = executionService.createOrUpdate(executionRequest);

    // Assert
    assertNotNull(result);
    assertEquals(testId, result.getId());
    assertEquals("Test Execution", result.getName());
    verify(executionRepository, times(1)).save(any(ExecutionEntity.class));
    verify(executionParamService, times(1)).saveAll(anyList());
  }

  @Test
  void createOrUpdate_success_whenUpdate() throws BusinessException {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(executionGroupRepository.findById(groupId)).thenReturn(Optional.of(executionGroupEntity));
    when(databaseConnectionService.findById(dbConnectionId)).thenReturn(databaseConnectionEntity);
    when(executionRepository.existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(anyString(), anyString(),
        anyString())).thenReturn(false);
    when(executionRepository.save(any(ExecutionEntity.class))).thenReturn(executionEntity);

    // Act
    ExecutionEntity result = executionService.createOrUpdate(executionRequest);

    // Assert
    assertNotNull(result);
    assertEquals(testId, result.getId());
    assertEquals("Test Execution", result.getName());
    verify(executionRepository, times(1)).save(any(ExecutionEntity.class));
    verify(executionParamService, times(1)).deleteAllByExecutionId(testId);
    verify(executionParamService, times(1)).saveAll(anyList());
  }

  @Test
  void createOrUpdate_throwsException_whenExecutionNotFound() {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      executionService.createOrUpdate(executionRequest);
    });
  }

  @Test
  void createOrUpdate_throwsException_whenNameExists() {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(executionRepository.existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(anyString(), anyString(),
        anyString())).thenReturn(true);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      executionService.createOrUpdate(executionRequest);
    });
  }

  @Test
  void deleteWithId_success() throws BusinessException {
    // Arrange
    String executionId = "test-execution-id";

    // Mock the dependency service to not throw exception
    doNothing().when(executionDependencyService).deleteWithId(executionId);

    // Act
    executionDependencyService.deleteWithId(executionId);

    // Assert
    verify(executionDependencyService, times(1)).deleteWithId(executionId);
  }

  @Test
  void deleteWithId_throwsException_whenExecutionNotFound() throws BusinessException {
    // Arrange
    String executionId = "non-existent-id";

    // Mock the dependency service to throw exception
    doThrow(new BusinessException(ErrorCode.EXECUTION_NOT_FOUND))
        .when(executionDependencyService).deleteWithId(executionId);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      executionDependencyService.deleteWithId(executionId);
    });

    assertEquals(ErrorCode.EXECUTION_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void findWithId_success() throws BusinessException {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(executionApiService.findApiInfoByExecutionId(testId)).thenReturn(new ExecutionApiEntity());
    // Act
    ExecutionResponse result = executionService.findWithId(testId);

    // Assert
    assertNotNull(result);
    assertEquals(testId, result.getId());
    assertEquals("Test Execution", result.getName());
    verify(executionRepository, times(1)).findById(testId);
  }

  @Test
  void findWithId_throwsException_whenExecutionNotFound() {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      executionService.findWithId(testId);
    });
  }

  @Test
  void findByIdWithVariable_success_withSqlType() throws BusinessException {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(executionParamService.findAllByExecutionId(testId)).thenReturn(executionParamEntities);
    when(commonAclPermissionService.isAnyPermission(anyList(), any(Boolean.class))).thenReturn(true);

    // Act
    ExecutionResponse result = executionService.findByIdWithVariable(testId);

    // Assert
    assertNotNull(result);
    assertEquals(testId, result.getId());
    assertEquals("Test Execution", result.getName());
    verify(executionRepository, times(1)).findById(testId);
    verify(executionParamService, times(1)).findAllByExecutionId(testId);
  }

  @Test
  void findByIdWithVariable_success_withPythonType() throws BusinessException {
    // Arrange
    executionEntity.setType(ExecutionTypeEnum.PYTHON);
    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(executionParamService.findAllByExecutionId(testId)).thenReturn(executionParamEntities);
    when(variableService.findAllByNameIn(anyList())).thenReturn(variableEntities);
    when(commonAclPermissionService.isAnyPermission(anyList(), any(Boolean.class))).thenReturn(true);

    // Act
    ExecutionResponse result = executionService.findByIdWithVariable(testId);

    // Assert
    assertNotNull(result);
    assertEquals(testId, result.getId());
    assertEquals("Test Execution", result.getName());
    verify(executionRepository, times(1)).findById(testId);
    verify(executionParamService, times(1)).findAllByExecutionId(testId);
    verify(variableService, times(1)).findAllByNameIn(anyList());
  }

  @Test
  void findByIdWithVariable_throwsException_whenExecutionNotFound() {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      executionService.findByIdWithVariable(testId);
    });
  }


  @Test
  void execute_success_withValidRequest() throws BusinessException {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(commonAclPermissionService.isAnyPermission(anyList(), any(Boolean.class))).thenReturn(true);
    when(executionParamService.findAllByExecutionId(testId)).thenReturn(new ArrayList<>());
    when(databaseConnectionService.findById(dbConnectionId)).thenReturn(databaseConnectionEntity);

    // Mock RestTemplate response
    ExecutionScriptResponse scriptResponse = ExecutionScriptResponse.builder()
        .status(ExecutionStatusEnum.COMPLETED)
        .result("Query executed successfully")
        .error(null)
        .build();

    ResponseData<ExecutionScriptResponse> responseData = new ResponseData<>();
    responseData.setData(scriptResponse);
    responseData.setStatus(200);

    ResponseEntity<ResponseData<ExecutionScriptResponse>> responseEntity =
        new ResponseEntity<>(responseData, HttpStatus.OK);

    // Mock static method call
    try (MockedStatic<ExternalExecutionUrl> mockedStatic = Mockito.mockStatic(ExternalExecutionUrl.class)) {
      mockedStatic.when(() -> ExternalExecutionUrl.getUrl(anyString()))
          .thenReturn("http://localhost:8080/api/external-execution/v1/execute_scripts");

      when(restTemplateKeycloakCentralized.exchange(
          anyString(),
          eq(HttpMethod.POST),
          any(HttpEntity.class),
          any(ParameterizedTypeReference.class)
      )).thenReturn(responseEntity);

      // Act
      ExecuteScriptResponse result = executionService.execute(executeScriptRequest);

      // Assert
      assertNotNull(result);
      assertEquals(testId, result.getId());
      assertEquals("Test Execution", result.getName());
      assertEquals("Query executed successfully", result.getScriptResponse());
      assertEquals(ExecutionStatusEnum.COMPLETED, result.getStatus());

      verify(executionRepository, times(1)).findById(testId);
      verify(commonAclPermissionService, times(1)).isAnyPermission(anyList(), any(Boolean.class));
      verify(restTemplateKeycloakCentralized, times(1)).exchange(
          anyString(), eq(HttpMethod.POST), any(HttpEntity.class), any(ParameterizedTypeReference.class));
    }
  }

  @Test
  void execute_success_withNullResponseBody() throws BusinessException {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(commonAclPermissionService.isAnyPermission(anyList(), any(Boolean.class))).thenReturn(true);
    when(executionParamService.findAllByExecutionId(testId)).thenReturn(new ArrayList<>());
    when(databaseConnectionService.findById(dbConnectionId)).thenReturn(databaseConnectionEntity);

    // Mock RestTemplate response with null body
    ResponseEntity<ResponseData<ExecutionScriptResponse>> responseEntity =
        new ResponseEntity<>(null, HttpStatus.OK);

    // Mock static method call
    try (MockedStatic<ExternalExecutionUrl> mockedStatic = Mockito.mockStatic(ExternalExecutionUrl.class)) {
      mockedStatic.when(() -> ExternalExecutionUrl.getUrl(anyString()))
          .thenReturn("http://localhost:8080/api/external-execution/v1/execute_scripts");

      when(restTemplateKeycloakCentralized.exchange(
          anyString(),
          eq(HttpMethod.POST),
          any(HttpEntity.class),
          any(ParameterizedTypeReference.class)
      )).thenReturn(responseEntity);

      // Act
      ExecuteScriptResponse result = executionService.execute(executeScriptRequest);

      // Assert
      assertNotNull(result);
      assertEquals(testId, result.getId());
      assertEquals("Test Execution", result.getName());
      assertEquals(ExecutionStatusEnum.COMPLETED, result.getStatus());

      verify(executionRepository, times(1)).findById(testId);
      verify(commonAclPermissionService, times(1)).isAnyPermission(anyList(), any(Boolean.class));
      verify(restTemplateKeycloakCentralized, times(1)).exchange(
          anyString(), eq(HttpMethod.POST), any(HttpEntity.class), any(ParameterizedTypeReference.class));
    }
  }

  @Test
  void execute_success_withSqlExecutionResponse() throws BusinessException {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(commonAclPermissionService.isAnyPermission(anyList(), any(Boolean.class))).thenReturn(true);
    when(executionParamService.findAllByExecutionId(testId)).thenReturn(new ArrayList<>());
    when(databaseConnectionService.findById(dbConnectionId)).thenReturn(databaseConnectionEntity);

    // Create SQL execution response
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listDataMappings(new ArrayList<>())
        .listColumns(List.of("id", "name"))
        .total(10)
        .isNonQuery(false)
        .build();

    // Mock RestTemplate response with SQL data
    ExecutionScriptResponse scriptResponse = ExecutionScriptResponse.builder()
        .status(ExecutionStatusEnum.COMPLETED)
        .result("SQL query executed")
        .error(null)
        .sqlExecutionResponse(sqlExecutionResponse)
        .build();

    ResponseData<ExecutionScriptResponse> responseData = new ResponseData<>();
    responseData.setData(scriptResponse);
    responseData.setStatus(200);

    ResponseEntity<ResponseData<ExecutionScriptResponse>> responseEntity =
        new ResponseEntity<>(responseData, HttpStatus.OK);

    // Mock static method call
    try (MockedStatic<ExternalExecutionUrl> mockedStatic = Mockito.mockStatic(ExternalExecutionUrl.class)) {
      mockedStatic.when(() -> ExternalExecutionUrl.getUrl(anyString()))
          .thenReturn("http://localhost:8080/api/external-execution/v1/execute_scripts");

      when(restTemplateKeycloakCentralized.exchange(
          anyString(),
          eq(HttpMethod.POST),
          any(HttpEntity.class),
          any(ParameterizedTypeReference.class)
      )).thenReturn(responseEntity);

      // Act
      ExecuteScriptResponse result = executionService.execute(executeScriptRequest);

      // Assert
      assertNotNull(result);
      assertEquals(testId, result.getId());
      assertEquals("Test Execution", result.getName());
      assertEquals("SQL query executed", result.getScriptResponse());
      assertEquals(ExecutionStatusEnum.COMPLETED, result.getStatus());
      assertNotNull(result.getSqlExecutionResponse());
      assertEquals(10, result.getSqlExecutionResponse().getTotal());
      assertEquals(2, result.getSqlExecutionResponse().getColumns().size());

      verify(executionRepository, times(1)).findById(testId);
      verify(commonAclPermissionService, times(1)).isAnyPermission(anyList(), any(Boolean.class));
      verify(restTemplateKeycloakCentralized, times(1)).exchange(
          anyString(), eq(HttpMethod.POST), any(HttpEntity.class), any(ParameterizedTypeReference.class));
    }
  }

  @Test
  void execute_throwsException_whenNoPermission() throws BusinessException {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(commonAclPermissionService.isAnyPermission(anyList(), any(Boolean.class))).thenReturn(false);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      executionService.execute(executeScriptRequest);
    });

    assertEquals(ErrorCode.USER_DONT_HAVE_PERMISSIONS.getCode(), exception.getCode());
    verify(executionRepository, times(1)).findById(testId);
    verify(commonAclPermissionService, times(1)).isAnyPermission(anyList(), any(Boolean.class));
  }

  @Test
  void execute_throwsException_whenExecutionNotFound() {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      executionService.execute(executeScriptRequest);
    });

    assertEquals(ErrorCode.EXECUTION_NOT_FOUND.getCode(), exception.getCode());
    verify(executionRepository, times(1)).findById(testId);
  }

  @Test
  void execute_throwsException_whenSocketTimeout() throws BusinessException {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(commonAclPermissionService.isAnyPermission(anyList(), any(Boolean.class))).thenReturn(true);
    when(executionParamService.findAllByExecutionId(testId)).thenReturn(new ArrayList<>());
    when(databaseConnectionService.findById(dbConnectionId)).thenReturn(databaseConnectionEntity);

    // Mock RestTemplate to throw SocketTimeoutException
    RestClientException restException = new RestClientException("Timeout", new SocketTimeoutException("Connection timeout"));

    // Mock static method call
    try (MockedStatic<ExternalExecutionUrl> mockedStatic = Mockito.mockStatic(ExternalExecutionUrl.class)) {
      mockedStatic.when(() -> ExternalExecutionUrl.getUrl(anyString()))
          .thenReturn("http://localhost:8080/api/external-execution/v1/execute_scripts");

      when(restTemplateKeycloakCentralized.exchange(
          anyString(),
          eq(HttpMethod.POST),
          any(HttpEntity.class),
          any(ParameterizedTypeReference.class)
      )).thenThrow(restException);

      // Act & Assert
      BusinessException exception = assertThrows(BusinessException.class, () -> {
        executionService.execute(executeScriptRequest);
      });

      assertEquals(ErrorCode.EXECUTION_IS_RUNNING_LONGER_THAN_EXPECTED.getCode(), exception.getCode());
      verify(executionRepository, times(1)).findById(testId);
      verify(restTemplateKeycloakCentralized, times(1)).exchange(
          anyString(), eq(HttpMethod.POST), any(HttpEntity.class), any(ParameterizedTypeReference.class));
    }
  }

  @Test
  void execute_throwsException_whenConnectException() throws BusinessException {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(commonAclPermissionService.isAnyPermission(anyList(), any(Boolean.class))).thenReturn(true);
    when(executionParamService.findAllByExecutionId(testId)).thenReturn(new ArrayList<>());
    when(databaseConnectionService.findById(dbConnectionId)).thenReturn(databaseConnectionEntity);

    // Mock RestTemplate to throw ConnectException
    RestClientException restException = new RestClientException("Connection failed", new ConnectException("Connection refused"));

    // Mock static method call
    try (MockedStatic<ExternalExecutionUrl> mockedStatic = Mockito.mockStatic(ExternalExecutionUrl.class)) {
      mockedStatic.when(() -> ExternalExecutionUrl.getUrl(anyString()))
          .thenReturn("http://localhost:8080/api/external-execution/v1/execute_scripts");

      when(restTemplateKeycloakCentralized.exchange(
          anyString(),
          eq(HttpMethod.POST),
          any(HttpEntity.class),
          any(ParameterizedTypeReference.class)
      )).thenThrow(restException);

      // Act & Assert
      BusinessException exception = assertThrows(BusinessException.class, () -> {
        executionService.execute(executeScriptRequest);
      });

      assertEquals(ErrorCode.EXECUTION_IS_RUNNING_LONGER_THAN_EXPECTED.getCode(), exception.getCode());
      verify(executionRepository, times(1)).findById(testId);
      verify(restTemplateKeycloakCentralized, times(1)).exchange(
          anyString(), eq(HttpMethod.POST), any(HttpEntity.class), any(ParameterizedTypeReference.class));
    }
  }

  @Test
  void execute_throwsException_whenGeneralRestClientException() throws BusinessException {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(commonAclPermissionService.isAnyPermission(anyList(), any(Boolean.class))).thenReturn(true);
    when(executionParamService.findAllByExecutionId(testId)).thenReturn(new ArrayList<>());
    when(databaseConnectionService.findById(dbConnectionId)).thenReturn(databaseConnectionEntity);

    // Mock RestTemplate to throw general RestClientException
    RestClientException restException = new RestClientException("General error");

    // Mock static method call
    try (MockedStatic<ExternalExecutionUrl> mockedStatic = Mockito.mockStatic(ExternalExecutionUrl.class)) {
      mockedStatic.when(() -> ExternalExecutionUrl.getUrl(anyString()))
          .thenReturn("http://localhost:8080/api/external-execution/v1/execute_scripts");

      when(restTemplateKeycloakCentralized.exchange(
          anyString(),
          eq(HttpMethod.POST),
          any(HttpEntity.class),
          any(ParameterizedTypeReference.class)
      )).thenThrow(restException);

      // Act & Assert
      BusinessException exception = assertThrows(BusinessException.class, () -> {
        executionService.execute(executeScriptRequest);
      });

      assertEquals(ErrorCode.EXECUTION_RUN_FAILED.getCode(), exception.getCode());
      verify(executionRepository, times(1)).findById(testId);
      verify(restTemplateKeycloakCentralized, times(1)).exchange(
          anyString(), eq(HttpMethod.POST), any(HttpEntity.class), any(ParameterizedTypeReference.class));
    }
  }

  @Test
  void existsByDatabaseConnectionId_returnsTrue() {
    // Arrange
    when(executionRepository.existsByDatabaseConnectionId(dbConnectionId)).thenReturn(true);

    // Act
    boolean result = executionService.existsByDatabaseConnectionId(dbConnectionId);

    // Assert
    assertTrue(result);
    verify(executionRepository, times(1)).existsByDatabaseConnectionId(dbConnectionId);
  }

  @Test
  void existsByDatabaseConnectionId_returnsFalse() {
    // Arrange
    when(executionRepository.existsByDatabaseConnectionId(dbConnectionId)).thenReturn(false);

    // Act
    boolean result = executionService.existsByDatabaseConnectionId(dbConnectionId);

    // Assert
    assertFalse(result);
    verify(executionRepository, times(1)).existsByDatabaseConnectionId(dbConnectionId);
  }

  @Test
  void isExecutionInfoUpdated_returnsFalse_whenNoChanges() throws Exception {
    // Arrange
    executeScriptRequest.setScript(executionEntity.getScript());
    executeScriptRequest.setName(executionEntity.getName());
    executeScriptRequest.setDescription(executionEntity.getDescription());
    executeScriptRequest.setType(executionEntity.getType());
    executeScriptRequest.setDatabaseConnectionId(executionEntity.getDatabaseConnectionId());
    executeScriptRequest.setExecutionGroupId(executionEntity.getExecutionGroupId());

    List<ExecuteScriptParamModel> params = new ArrayList<>();
    ExecuteScriptParamModel param = new ExecuteScriptParamModel();
    param.setName("param1");
    param.setValue("value1");
    params.add(param);

    List<VariableRequest> variables = new ArrayList<>();
    VariableRequest variable = new VariableRequest();
    variable.setName("param1");
    variable.setValue("value1");
    variables.add(variable);

    executeScriptRequest.setVariables(variables);

    // Act
    boolean result = ReflectionTestUtils.invokeMethod(
        executionService, "isExecutionInfoUpdated", 
        executeScriptRequest, executionEntity, params);

    // Assert
    assertFalse(result);
  }

  @Test
  void isExecutionInfoUpdated_returnsTrue_whenScriptChanged() throws Exception {
    // Arrange
    executeScriptRequest.setScript("DIFFERENT SCRIPT");
    executeScriptRequest.setName(executionEntity.getName());
    executeScriptRequest.setDescription(executionEntity.getDescription());
    executeScriptRequest.setType(executionEntity.getType());
    executeScriptRequest.setDatabaseConnectionId(executionEntity.getDatabaseConnectionId());
    executeScriptRequest.setExecutionGroupId(executionEntity.getExecutionGroupId());

    List<ExecuteScriptParamModel> params = new ArrayList<>();
    ExecuteScriptParamModel param = new ExecuteScriptParamModel();
    param.setName("param1");
    param.setValue("value1");
    params.add(param);

    List<VariableRequest> variables = new ArrayList<>();
    VariableRequest variable = new VariableRequest();
    variable.setName("param1");
    variable.setValue("value1");
    variables.add(variable);

    executeScriptRequest.setVariables(variables);

    // Act
    boolean result = ReflectionTestUtils.invokeMethod(
        executionService, "isExecutionInfoUpdated", 
        executeScriptRequest, executionEntity, params);

    // Assert
    assertTrue(result);
  }

  // Tests for protected and private methods

  @Test
  void buildParams_success_withEmptyRequireParams() throws Exception {
    // Arrange
    List<VariableRequest> requestVariables = new ArrayList<>();
    when(executionParamService.findAllByExecutionId(testId)).thenReturn(new ArrayList<>());

    // Act
    @SuppressWarnings("unchecked")
    List<ExecuteScriptParamModel> result = (List<ExecuteScriptParamModel>) ReflectionTestUtils.invokeMethod(
        executionService, "buildParams", requestVariables, executionEntity);

    // Assert
    assertNotNull(result);
    assertTrue(result.isEmpty());
    verify(executionParamService, times(1)).findAllByExecutionId(testId);
  }

  @Test
  void buildParams_success_withRequireParamsAndDefaultVariables() throws Exception {
    // Arrange
    List<VariableRequest> requestVariables = new ArrayList<>();
    VariableRequest variableRequest = VariableRequest.builder()
        .id("var-id-1")
        .name("param1")
        .value("value1")
        .build();
    VariableRequest variableRequest2 = VariableRequest.builder()
        .id("var-id-2")
        .name("param2")
        .value("value2")
        .build();
    requestVariables.add(variableRequest);
    requestVariables.add(variableRequest2);

    VariableEntity variableEntity1 = new VariableEntity();
    variableEntity1.setId("var-id-2");
    variableEntity1.setName("param2");
    variableEntity1.setValue("value2");
    variableEntity1.setHidden(true);
    variableEntities.add(variableEntity1);

    when(executionParamService.findAllByExecutionId(testId)).thenReturn(executionParamEntities);

    // Act
    @SuppressWarnings("unchecked")
    List<ExecuteScriptParamModel> result = (List<ExecuteScriptParamModel>) ReflectionTestUtils.invokeMethod(
        executionService, "buildParams", requestVariables, executionEntity);

    // Assert
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("param1", result.get(0).getName());
    assertEquals("value1", result.get(0).getValue());
    verify(executionParamService, times(1)).findAllByExecutionId(testId);
  }

  @Test
  void validateExecutionRunningRequest_success_withValidParams() throws Exception {
    // Arrange
    List<ExecuteScriptParamModel> params = new ArrayList<>();

    when(databaseConnectionService.findById(dbConnectionId)).thenReturn(databaseConnectionEntity);

    // Act
    executionService.validateExecutionRunningRequest(executeScriptRequest, executionEntity, params);

    // Assert
    verify(databaseConnectionService, times(1)).findById(dbConnectionId);
  }

  @Test
  void validateExecutionRunningRequest_throwsException_whenInvalidParams() throws Exception {
    // Arrange
    List<ExecuteScriptParamModel> params = new ArrayList<>();
    ExecuteScriptParamModel param = new ExecuteScriptParamModel();
    param.setName("param1");
    // Missing value
    params.add(param);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class,
        () -> executionService.validateExecutionRunningRequest(new ExecuteScriptRequest(), executionEntity, params));
  }

  @Test
  void validateExecutionRunningRequest_throwsException_whenDatabaseConnectionNotFound() throws Exception {
    // Arrange
    List<ExecuteScriptParamModel> params = new ArrayList<>();


    when(databaseConnectionService.findById(dbConnectionId)).thenReturn(null);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class,
        () -> executionService.validateExecutionRunningRequest(executeScriptRequest, executionEntity, params));
  }

  @Test
  void validateExecutionRunningRequest_throwsException_whenDatabaseConnectionInactive() throws Exception {
    // Arrange
    List<ExecuteScriptParamModel> params = new ArrayList<>();
    ExecuteScriptParamModel param = new ExecuteScriptParamModel();

    databaseConnectionEntity.setIsActive(false);
    when(databaseConnectionService.findById(dbConnectionId)).thenReturn(databaseConnectionEntity);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class,
        () -> executionService.validateExecutionRunningRequest(executeScriptRequest, executionEntity, params));
  }

  @Test
  void validateExecutionRequest_success_withValidSqlRequest() throws Exception {
    // Arrange
    executionRequest.setType(ExecutionTypeEnum.SQL);
    executionRequest.setScript("SELECT * FROM table");
    when(executionRepository.findById(testId)).thenReturn(
        Optional.of(executionEntity)); // executionEntity is SQL type by default in setUp
    when(executionRepository.existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(anyString(), anyString(),
        anyString())).thenReturn(false);
    when(executionGroupRepository.findById(groupId)).thenReturn(Optional.of(executionGroupEntity));
    when(databaseConnectionService.findById(dbConnectionId)).thenReturn(databaseConnectionEntity);

    // Act
    ReflectionTestUtils.invokeMethod(executionService, "validateExecutionRequest", executionRequest);

    // Assert
    verify(executionRepository, times(1)).findById(testId);
    verify(executionRepository, times(1)).existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(anyString(), anyString(),
        anyString());
    verify(executionGroupRepository, times(1)).findById(groupId);
    verify(databaseConnectionService, times(1)).findById(dbConnectionId);
  }

  @Test
  void validateExecutionRequest_failed_caseDatabaseConnectIdNotFound() throws Exception {
    // Arrange
    executionRequest.setType(ExecutionTypeEnum.SQL);
    executionRequest.setDatabaseConnectionId(null);
    executionRequest.setScript("SELECT * FROM table");
    when(executionRepository.findById(testId)).thenReturn(
        Optional.of(executionEntity)); // executionEntity is SQL type by default in setUp
    when(executionRepository.existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(anyString(), anyString(),
        anyString())).thenReturn(false);
    when(executionGroupRepository.findById(groupId)).thenReturn(Optional.of(executionGroupEntity));

    assertThrows(BusinessException.class, () -> executionService.validateExecutionRequest(executionRequest));
  }

  @Test
  void validateExecutionRequest_failed_caseDatabaseConnectNotFound() throws Exception {
    // Arrange
    executionRequest.setType(ExecutionTypeEnum.SQL);
    executionRequest.setScript("SELECT * FROM table");
    when(executionRepository.findById(testId)).thenReturn(
        Optional.of(executionEntity)); // executionEntity is SQL type by default in setUp
    when(executionRepository.existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(anyString(), anyString(),
        anyString())).thenReturn(false);
    when(executionGroupRepository.findById(groupId)).thenReturn(Optional.of(executionGroupEntity));

    assertThrows(BusinessException.class, () -> executionService.validateExecutionRequest(executionRequest));
  }

  @Test
  void validateExecutionRequest_failed_invalidQuery() throws Exception {
    // Arrange
    executionRequest.setType(ExecutionTypeEnum.SQL);
    executionRequest.setScript("SELECT from to");
    when(executionRepository.findById(testId)).thenReturn(
        Optional.of(executionEntity)); // executionEntity is SQL type by default in setUp
    when(executionRepository.existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(anyString(), anyString(),
        anyString())).thenReturn(false);
    when(executionGroupRepository.findById(groupId)).thenReturn(Optional.empty());

    assertThrows(BusinessException.class, () -> executionService.validateExecutionRequest(executionRequest));
  }

  @Test
  void validateExecutionRequest_success_withValidPythonScriptAndAllowedImports() {
    // Arrange
    executionRequest.setType(ExecutionTypeEnum.PYTHON);
    executionRequest.setScript(
        "import os\nimport sys\nprint(os.name)"); // os, sys are typically in ExecutionConstants.WHITE_LISTED_LIBS
    executionRequest.setDatabaseConnectionId(null); // Python doesn't need db connection id

    ExecutionEntity pythonExecutionEntity = new ExecutionEntity();
    pythonExecutionEntity.setId(testId);
    pythonExecutionEntity.setType(ExecutionTypeEnum.PYTHON);


    when(executionRepository.findById(testId)).thenReturn(Optional.of(pythonExecutionEntity));
    when(executionRepository.existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(anyString(), anyString(),
        anyString())).thenReturn(false);
    when(executionGroupRepository.findById(groupId)).thenReturn(Optional.of(executionGroupEntity));

    PythonAnalysisModel analysisResult =
        new PythonAnalysisModel(true, List.of("requests"), Set.of(), Set.of(), Set.of());
    try (MockedStatic<PythonParserUtils> mocked = Mockito.mockStatic(PythonParserUtils.class)) {
      mocked.when(() -> PythonParserUtils.analyzePythonScript(anyString())).thenReturn(analysisResult);

      // Act
      assertDoesNotThrow(
          () -> ReflectionTestUtils.invokeMethod(executionService, "validateExecutionRequest", executionRequest));
    }
    // Assert
    verify(executionRepository, times(1)).findById(testId);
    verify(executionRepository, times(1)).existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(anyString(), anyString(),
        anyString());
    verify(executionGroupRepository, times(1)).findById(groupId);
  }

  @Test
  void validateExecutionRequest_throwsException_whenPythonScriptIsInvalid() {
    // Arrange
    executionRequest.setType(ExecutionTypeEnum.PYTHON);
    executionRequest.setScript("print( 'invalid syntax");
    executionRequest.setDatabaseConnectionId(null);

    ExecutionEntity pythonExecutionEntity = new ExecutionEntity();
    pythonExecutionEntity.setId(testId);
    pythonExecutionEntity.setType(ExecutionTypeEnum.PYTHON);

    when(executionRepository.findById(testId)).thenReturn(Optional.of(pythonExecutionEntity));
    when(executionRepository.existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(anyString(), anyString(),
        anyString())).thenReturn(false);
    when(executionGroupRepository.findById(groupId)).thenReturn(Optional.of(executionGroupEntity));

    PythonAnalysisModel analysisResult =
        new PythonAnalysisModel(false, List.of("requests"), Set.of(), Set.of(), Set.of());
    try (MockedStatic<PythonParserUtils> mocked = Mockito.mockStatic(PythonParserUtils.class)) {
      mocked.when(() -> PythonParserUtils.analyzePythonScript(anyString())).thenReturn(analysisResult);

      // Act & Assert
      BusinessException exception =
          assertThrows(BusinessException.class, () -> executionService.validateExecutionRequest(executionRequest));
      assertEquals(ErrorCode.EXECUTION_SCRIPT_IS_INVALID.getCode(), exception.getCode());
    }
  }

  @Test
  void validateExecutionRequest_throwsException_whenPythonScriptUsesDisallowedImports() {
    // Arrange
    executionRequest.setType(ExecutionTypeEnum.PYTHON);
    executionRequest.setScript("import uq\nprint('dangerous import')"); // requests might not be whitelisted
    executionRequest.setDatabaseConnectionId(null);

    ExecutionEntity pythonExecutionEntity = new ExecutionEntity();
    pythonExecutionEntity.setId(testId);
    pythonExecutionEntity.setType(ExecutionTypeEnum.PYTHON);

    when(executionRepository.findById(testId)).thenReturn(Optional.of(pythonExecutionEntity));
    when(executionRepository.existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(anyString(), anyString(),
        anyString())).thenReturn(false);
    when(executionGroupRepository.findById(groupId)).thenReturn(Optional.of(executionGroupEntity));

    // Assume 'requests' is not in ExecutionConstants.WHITE_LISTED_LIBS
    PythonAnalysisModel analysisResult = new PythonAnalysisModel(true, List.of(), Set.of("uq"), Set.of(), Set.of());
    try (MockedStatic<PythonParserUtils> mocked = Mockito.mockStatic(PythonParserUtils.class)) {
      mocked.when(() -> PythonParserUtils.analyzePythonScript(anyString())).thenReturn(analysisResult);

      // Act & Assert
      BusinessException exception =
          assertThrows(BusinessException.class, () -> executionService.validateExecutionRequest(executionRequest));
      assertEquals(ErrorCode.EXECUTION_PYTHON_SCRIPT_USE_DISALLOWED_MODULE.getCode(), exception.getCode());
    }
  }


  @Test
  void validateExecutionRequest_throwsException_whenTypeChanged() throws Exception {
    // Arrange
    ExecutionEntity existingExecution = new ExecutionEntity();
    existingExecution.setId(testId);
    existingExecution.setType(ExecutionTypeEnum.PYTHON);

    when(executionRepository.findById(testId)).thenReturn(Optional.of(existingExecution));

    // Act & Assert
    BusinessException exception =
        assertThrows(BusinessException.class, () -> executionService.validateExecutionRequest(executionRequest));
  }

  @Test
  void validateExecutionRequest_throwsException_whenNameExists() throws Exception {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(executionRepository.existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(anyString(), anyString(),
        anyString())).thenReturn(true);

    // Act & Assert
    BusinessException exception =
        assertThrows(BusinessException.class, () -> executionService.validateExecutionRequest(executionRequest));
  }

  @Test
  void validateExecutionRequest_throwsException_whenGroupNotFound() throws Exception {
    // Arrange
    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(executionRepository.existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(anyString(), anyString(),
        anyString())).thenReturn(false);
    when(executionGroupRepository.findById(groupId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception =
        assertThrows(BusinessException.class, () -> executionService.validateExecutionRequest(executionRequest));
  }

  @Test
  void processExecutionParams_success_withSqlType() throws Exception {
    // Arrange
    executionEntity.setType(ExecutionTypeEnum.SQL);
    executionEntity.setScript("SELECT * FROM test WHERE id = :id");

    // Act
    executionService.processExecutionParams(executionEntity, null);

    // Assert
    verify(executionParamService, times(1)).saveAll(anyList());
  }

  @Test
  void processExecutionParams_success_withPythonType() throws Exception {
    // Arrange
    executionEntity.setType(ExecutionTypeEnum.PYTHON);
    executionEntity.setScript("import os\nprint(os.getenv('VAR1'))");

    // Act
    executionService.processExecutionParams(executionEntity, null);

    // Assert
    verify(executionParamService, times(1)).saveAll(anyList());
  }

  @Test
  void isInvalidQuery_returnsTrueForDropStatement() throws Exception {
    // Arrange
    String sql = "DROP TABLE users";

    // Act
    boolean result = ReflectionTestUtils.invokeMethod(executionService, "isInvalidQuery", sql);

    // Assert
    assertTrue(result);
  }

  @Test
  void isInvalidQuery_returnsFalseForSelectStatement() throws Exception {
    // Arrange
    String sql = "SELECT * FROM users";

    // Act
    boolean result = ReflectionTestUtils.invokeMethod(executionService, "isInvalidQuery", sql);

    // Assert
    assertFalse(result);
  }


  @Test
  void getRepository_success() {
    JpaCommonRepository<ExecutionEntity, String> result = executionService.getRepository();
    assertEquals(executionRepository, result);
  }

  @Test
  void findAllByIdIn_shouldReturnExecutionEntities() {
    List<String> ids = List.of("id1", "id2");
    List<ExecutionEntity> expected = List.of(new ExecutionEntity(), new ExecutionEntity());

    when(executionRepository.findAllByIdIn(ids)).thenReturn(expected);

    List<ExecutionEntity> result = executionService.findAllByIdIn(ids);

    assertEquals(expected, result);
    verify(executionRepository).findAllByIdIn(ids);
  }

  @Test
  void findAllByType_shouldReturnExecutionEntitiesByType() {
    ExecutionTypeEnum type = ExecutionTypeEnum.PYTHON;
    List<ExecutionResponse> expected = List.of(new ExecutionResponse());

    when(executionRepository.findAllByType(type)).thenReturn(expected);

    List<ExecutionResponse> result = executionService.findAllByType(type);

    assertEquals(expected, result);
    verify(executionRepository).findAllByType(type);
  }
  @Test
  void processExecutionParams_withApiType_andEmptyParams_success() throws Exception {
    // Arrange
    executionEntity.setType(ExecutionTypeEnum.API);
    ExecutionRequest request = new ExecutionRequest();
    request.setApiInfo(null);

    // Act
    ReflectionTestUtils.invokeMethod(executionService, "processExecutionParams", executionEntity, request);

    // Assert
    verify(executionParamService, times(1)).saveAll(anyList());
  }

  @Test
  void validateExecutionRequest_success_withApiType() throws Exception {
    // Arrange
    executionEntity.setType(ExecutionTypeEnum.API);
    executionRequest.setType(ExecutionTypeEnum.API);
    executionRequest.setDatabaseConnectionId(null);
    executionRequest.setScript(null);

    when(executionRepository.findById(testId)).thenReturn(Optional.of(executionEntity));
    when(executionRepository.existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(anyString(), anyString(), anyString()))
            .thenReturn(false);
    when(executionGroupRepository.findById(groupId)).thenReturn(Optional.of(executionGroupEntity));

    // Act & Assert
    assertDoesNotThrow(() ->
            ReflectionTestUtils.invokeMethod(executionService, "validateExecutionRequest", executionRequest));
  }

  @Test
  void validateExecutionRunningRequest_failure_throwsException_whenUnsupportedType() {
    // Arrange
    executionEntity.setType(null);
    List<ExecuteScriptParamModel> params = new ArrayList<>();

    // Act & Assert
    UndeclaredThrowableException exception = assertThrows(UndeclaredThrowableException.class, () ->
            ReflectionTestUtils.invokeMethod(executionService, "validateExecutionRunningRequest",
                    executeScriptRequest, executionEntity, params));
  }
  @Test
  void createOrUpdate_success_whenUpdateWithApiInfo_shouldHandleApiSectionCorrectly() throws BusinessException {
    // Arrange
    executionRequest.setType(ExecutionTypeEnum.API);
    ApiInfoRequest request = new ApiInfoRequest();
    request.setUrl("acccc");
    request.setMethod(ExecutionApiMethodEnum.GET);
    executionRequest.setApiInfo(request);

    ExecutionEntity existing = new ExecutionEntity();
    existing.setId(testId);
    existing.setExecutionGroupId(groupId);
    existing.setType(ExecutionTypeEnum.API);
    existing.setName("Old Name");

    when(executionRepository.findById(testId)).thenReturn(Optional.of(existing));
    when(executionGroupRepository.findById(groupId)).thenReturn(Optional.of(executionGroupEntity));
    when(executionRepository.save(any(ExecutionEntity.class))).thenReturn(existing);
    when(executionApiService.findApiInfoByExecutionId(testId)).thenReturn(null);
    // Act
    ExecutionEntity result = executionService.createOrUpdate(executionRequest);

    // Assert
    assertNotNull(result);
    verify(executionApiService).findApiInfoByExecutionId(testId);
    verify(executionParamService).deleteAllByExecutionId(testId);
    verify(executionApiService).save(any(ExecutionApiEntity.class));
  }

  @Test
  void createOrUpdate_failure_whenUpdateWithApiInfo() throws BusinessException {
    // Arrange
    executionRequest.setType(ExecutionTypeEnum.API);
    executionRequest.setApiInfo(new ApiInfoRequest());

    ExecutionEntity existing = new ExecutionEntity();
    existing.setId(testId);
    existing.setExecutionGroupId(groupId);
    existing.setType(ExecutionTypeEnum.API);
    existing.setName("Old Name");

    when(executionRepository.findById(testId)).thenReturn(Optional.of(existing));
    when(executionGroupRepository.findById(groupId)).thenReturn(Optional.of(executionGroupEntity));
    when(executionRepository.save(any(ExecutionEntity.class))).thenReturn(existing);

    BusinessException exception =
            assertThrows(BusinessException.class, () -> executionService.createOrUpdate(executionRequest));
  }
}
