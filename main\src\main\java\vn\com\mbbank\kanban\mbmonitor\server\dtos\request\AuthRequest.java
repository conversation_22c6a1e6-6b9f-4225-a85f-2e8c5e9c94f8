package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * request login.
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Builder
public class AuthRequest {
  @NotNull
  String userName;
  @NotNull
  String password;
  boolean userLocal;
}
