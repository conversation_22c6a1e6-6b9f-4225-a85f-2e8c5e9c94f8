package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionApiAuthTypeEnum;

/**
 * AuthenApiResponse.
 */
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AuthenticationApiResponse {
  ExecutionApiAuthTypeEnum authType;
  String token;
  String username;
  String password;
}
