package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.SysLogCursor;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.SysLogRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.SysLogResponse;
import vn.com.mbbank.kanban.mbmonitor.server.services.SysLogService;

/**
 * Controller logic AlertGroupConfigController.
 */
@RestController
@RequestMapping(ServerUrl.SYS_LOG_URL)
@RequiredArgsConstructor
public class SysLogController extends BaseController {

  private final SysLogService sysLogService;

  /**
   * find all log by paginationRequest.
   *
   * @param request Pagination parameters for the request.
   * @return ResponseEntity containing the response data with HTTP status OK.
   */
  @GetMapping
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.SYSLOG, action = PermissionActionEnum.VIEW)
  })
  public ResponseData<CursorPageResponse<SysLogResponse, SysLogCursor>> findAll(
      @ModelAttribute SysLogRequest request) {
    return ResponseUtils.success(sysLogService.findAll(request));
  }

}
